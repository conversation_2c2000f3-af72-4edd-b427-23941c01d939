<?
	$conn = @mysql_connect ("**********","sgg","sggroup") or die ("koneksi gagal");
	mysql_select_db ("eis", $conn);

	include_once("sapfrc/sapclasses/sap.php");


	
	
		$sap = new SAPConnection();
		$sap->Connect("logon_data.conf");


		if ($sap->GetStatus() == SAPRFC_OK ) //$sap->Open ();
		   $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce = &$sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}			
		
		//$tahun = date("Y");
       // $bulan = date("m");
		//$tanggal_mulai_sql=$tahun.$bulan.'1';
		
               // $kdplant='2403';
		$tanggal_mulai_sql=date('Ymd');
		$tanggal_mulai_sql1=date('Ymd');
               // $tglspj = date('Ymd');
	  	$fce->X_VKORG = '2000';
		$fce->X_TGL1 = $tanggal_mulai_sql;//date('Ymd', strtotime("-".$n_days." days"));//
		$fce->X_TGL2 = $tanggal_mulai_sql1;//date('Ymd', strtotime("-".$n_days." days"));//
                $fce->X_STATUS = '70';
		$fce->X_WO_KONFIRMASI ='X';
               // $fce->X_WERKS =$kdplant;
		$fce->Call();
		
		$q = "delete from sap_t_realisasi_shipment where tgl_spj between '".$tanggal_mulai_sql."' and '".$tanggal_mulai_sql1."'";
		mysql_query($q);

		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
				$NO_SPJ[$s] = $fce->ZDATA->row["NO_SPJ"];
				$TGL_SPJ[$s] = $fce->ZDATA->row["TGL_SPJ"];
				$NO_POLISI[$s] = $fce->ZDATA->row["NO_POLISI"];
				$KODE_DA[$s] = $fce->ZDATA->row["KODE_DA"];
				$NAMA_TOKO[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$PROPINSI[$s] = $fce->ZDATA->row["PROPINSI"];
				$NAMA_PROP[$s] = $fce->ZDATA->row["NAMA_PROP"];
				$AREA[$s] = $fce->ZDATA->row["AREA"];
				$NAMA_AREA[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$PLANT[$s] = $fce->ZDATA->row["PLANT"];
				$NO_EXPEDITUR[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$NAMA_EXPEDITUR[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$STATUS[$s] = $fce->ZDATA->row["STATUS"];
				$PRODUK[$s] = $fce->ZDATA->row["PRODUK"];
                                $ITEM_NO[$s] = $fce->ZDATA->row["ITEM_NO"];
				$UOM[$s] = $fce->ZDATA->row["UOM"];
				$BERAT_ISI[$s] = $fce->ZDATA->row["BERAT_ISI"];

				$conn = @mysql_connect ("**********","sgg","sggroup") or die ("koneksi gagal");
				mysql_select_db ("eis", $conn);
				$q_stock ="INSERT INTO sap_t_realisasi_shipment (no_spj, tgl_spj, no_polisi, ship_to, nm_ship_to, prop, nama_prop, area, nama_area, plant, no_expeditur, nama_expeditur, status, produk, item_no, uom, berat_isi) values ('".$NO_SPJ[$s]."','".$TGL_SPJ[$s]."','".$NO_POLISI[$s]."','".$KODE_DA[$s]."','".$NAMA_TOKO[$s]."','".$PROPINSI[$s]."','".$NAMA_PROP[$s]."','".$AREA[$s]."','".$NAMA_AREA[$s]."','".$PLANT[$s]."','".$NO_EXPEDITUR[$s]."','".$NAMA_EXPEDITUR[$s]."','".$STATUS[$s]."','".$PRODUK[$s]."','".$ITEM_NO[$s]."','".$UOM[$s]."','".$BERAT_ISI[$s]."')";

				$s++;
				$hasil=mysql_query($q_stock);
				if ($hasil) {
					echo "Success insert ".$s."Record for".$tgl_spj."<br>";
				} else {
					echo ("<br>Maaf input data gagal");
				}
		       		
			}
		}

		$sap->Close();

		
?> 