<? 
ob_start();
session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');

function HeaderingExcel($filename) {
	header("Content-type: application/vnd.ms-excel");
	header("Content-Disposition: attachment; filename=$filename" );
	header("Expires: 0");
	header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
	header("Pragma: public");
}
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 

$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();
# -----------------------------------------------------------------------
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
        $fce->X_VKORG = '2000';
		$fce->X_TGL1 = '20110610';
		$fce->X_TGL2 = '20110610'; // tgl sampai
		$fce->X_WERKS = ''; // plant
		$fce->X_KUNNR = ''; // distributor
		$fce->X_SHIP_TO_CODE = $shipto; // shipto
		$fce->X_NOFLAG = 'X'; // flag belum sampai gudang
        $fce->X_CURAHBAG = '20';
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_kontrak[$s] = $fce->ZDATA->row["VGBEL"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$tgl_so[$s] = $fce->ZDATA->row["AUDAT"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$pricegrp[$s] = $fce->ZDATA->row["PLTYP"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				$no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$nama_sold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["PRODUK"];
				$uom[$s] = $fce->ZDATA->row["UOM"];
				$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	

$total = count($nmplant);


	#Export Excell
	HeaderingExcel('daftar_penjualan.xls');

	// Creating a workbook
	$workbook = new Workbook("-");
	// Creating the first worksheet
	$worksheet1 =& $workbook->add_worksheet('Penjualan');
	$worksheet1->write(0, 0, 'No');
	$worksheet1->write(0, 1, 'No SPJ');
	$worksheet1->write(0, 2, 'Tgl SPJ');
	$worksheet1->write(0, 3, 'No Polisi');
	$worksheet1->write(0, 4, 'Sopir');
	$worksheet1->write(0, 5, 'Kode Distributor');
	$worksheet1->write(0, 6, 'Nama Distributor');
	$worksheet1->write(0, 7, 'Kode Shipto');
	$worksheet1->write(0, 8, 'Nama Shipto');
	$worksheet1->write(0, 9, 'Alamat');
	$worksheet1->write(0, 10, 'Kode Distrik');
	$worksheet1->write(0, 11, 'Nama Distrik');
	$worksheet1->write(0, 12, 'Kode Produk');
	$worksheet1->write(0, 13, 'Nama Produk');
	$worksheet1->write(0, 14, 'Qty');
	$worksheet1->write(0, 15, 'Kode Gudang');
	$worksheet1->write(0, 16, 'Nama Gudang');
	$workbook->close();

?>



