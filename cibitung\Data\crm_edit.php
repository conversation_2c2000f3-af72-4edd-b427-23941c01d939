<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$user_id=379;
$user_org=2000;
$page="crm_edit.php";
$distr_id=$fungsi->sapcode(260);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");
$nm_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"NAMA_PLANT");

if(isset($_POST['cari'])){
	    $nopol = $_POST['nopol'];
		$kddistr = $_POST['kddistr'];
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

//		$cek_pa= $tglm;
//        $cek_pass=$cek_pa;
//        $cek_ba=date("m/d/Y");
//        $cek_banding=$cek_ba;
//       
//        $tgl_pass_cek=strtotime($cek_pass);
//        $tgl_banding_cek=strtotime($cek_banding);

//        if($tgl_pass_cek<$tgl_banding_cek){                   
//		  echo "<script>alert('Data Edit Expayer');</script>";		            
//          }
//        else{

			$sql="select a.NO_TRANSAKSI,b.KD_GDG,b.NM_GDG,b.NO_TRANSAKSI,a.NO_SPJ,a.KD_DISTR,a.NM_DISTR,a.KD_SHIPTO,a.NM_SHIPTO,a.ALAMAT,a.DELETE_MARK,a.CREATE_DATE,a.QTY_DO,a.KD_DISTRIK,a.NM_DISTRIK,a.KD_MATERIAL, a.NM_MATERIAL from crm_spj_dtl a,crm_spj_hdr b where a.no_transaksi=b.no_transaksi and a.delete_mark='0'and a.kd_distr='".$kddistr."' and b.nopol='".$nopol."' and   a.create_date BETWEEN TO_Date('$tglm 00:00:00', 'YYYYMMDD HH24:MI:SS')AND TO_Date('$tgls 23:59:59', 'YYYYMMDD HH24:MI:SS') ";
				$query= oci_parse($conn, $sql);
				oci_execute($query);
				$s=0;
				$tglstock = date('Ymd');
				while($data=oci_fetch_array($query)){
					$idnya[$s]=$data['ID'];
					$NO_TRANSAKSI[$s]=$data['NO_TRANSAKSI'];
					$NO_SPJ[$s]=$data['NO_SPJ'];
					$KD_GDG[$s]=$data['KD_GDG'];
					$NM_GDG[$s]=$data['NM_GDG'];
					$KD_DISTR[$s]=$data['KD_DISTR'];
					$NM_DISTR[$s]=$data['NM_DISTR'];
					$KD_SHIPTO[$s]=$data['KD_SHIPTO'];
					$NM_SHIPTO[$s]=$data['NM_SHIPTO'];
					$ALAMAT[$s]=$data['ALAMAT'];
					$DELETE_MARK[$s]=$data['DELETE_MARK'];
					$CREATE_DATE[$s]=$data['CREATE_DATE'];
					$QTY_DO[$s]=$data['QTY_DO'];
					$DISTRIK[$s] = $data["KD_DISTRIK"];
					$NAMA_KOTA[$s] =$data["NM_DISTRIK"];
					$KD_MATERIAL[$s] = $data["KD_MATERIAL"];
					$NM_MATERIAL[$s] =$data["NM_MATERIAL"];		

			 $sqldelete="select * from crm_stok_gdg where kd_gdg='".$KD_GDG[$s]."' and kd_material= '".$KD_MATERIAL[$s]."'";
				$querydelete= oci_parse($conn, $sqldelete);
				oci_execute($querydelete);
				while($ok=oci_fetch_array($querydelete)){
					$KD_GDG1[$s]=$ok['KD_GDG'];
					$NM_GDG1[$s]=$ok['NM_GDG'];
					$KD_MATERIAL1[$s]=$ok['KD_MATERIAL'];
					$NM_MATERIAL1[$s]=$ok['NM_MATERIAL'];
					$STOK1[$s]=$ok['STOK'];
					$DELETE_MARK1[$s]=$ok['DELETE_MARK'];
					$STOK_AWAL1[$s]=$ok['STOK_AWAL'];
					
				    $s++;	
				    }
		           // }
			
		      }
		         $total=count($KD_SHIPTO);
	}
	if(isset($_POST['simpan'])){  
		    	$NO_TRANSAKSI = $_POST['NO_TRANSAKSI'];
				$KD_GDG = $_POST['KD_GDG'];
				$NM_GDG = $_POST['NM_GDG'];
				$NM_DISTR = $_POST['NM_DISTR'];
				$KD_DISTR = $_POST['KD_DISTR'];
				$KD_SHIPTO = $_POST['KD_SHIPTO'];
				$NM_SHIPTO = $_POST['NM_SHIPTO'];
				$KD_MATERIAL = $_POST['KD_MATERIAL'];
				$NM_MATERIAL = $_POST['NM_MATERIAL'];
				$ALAMAT = $_POST['ALAMAT'];
				$CREATE_DATE = $_POST['CREATE_DATE'];
				$DISTRIK = $_POST['DISTRIK'];
				$NAMA_KOTA = $_POST['NAMA_KOTA'];
		        $QTY_DO = $_POST['QTY_DO'];
			    $tglstock = $_POST ['tgl'];
				$KD_GDG1 = $_POST['KD_GDG1'];
	    	    $KD_MATERIAL1 = $_POST ['KD_MATERIAL1'];
				
      
			 
		$update1= "UPDATE CRM_SPJ_DTL  SET QTY_DO='$QTY_DO',KD_SHIPTO='$KD_SHIPTO',NM_SHIPTO='$NM_SHIPTO',ALAMAT='$ALAMAT',KD_DISTRIK='$DISTRIK',NM_DISTRIK='$NAMA_KOTA',KD_DISTR='$KD_DISTR',NM_DISTR='$NM_DISTR',UPDATE_DATE= '',UPDATE_BY='',KD_MATERIAL='$KD_MATERIAL',NM_MATERIAL='$NM_MATERIAL' WHERE NO_TRANSAKSI='$NO_TRANSAKSI'";
				$query_update1= oci_parse($conn, $update1);
				$hasil_update1 = oci_execute($query_update1);

		$update2= "UPDATE CRM_SPJ_HDR  SET KD_GDG='$KD_GDG',NM_GDG='$NM_GDG',KD_DISTR='$KD_DISTR',NM_DISTR='$NM_SHIPTO',UPDATE_DATE='',UPDATE_BY='' WHERE NO_TRANSAKSI='$NO_TRANSAKSI'";
				$query_update2= oci_parse($conn, $update2);
				$hasil_update2 = oci_execute($query_update2);

		$update3= "UPDATE CRM_STOK_GDG SET STOK=STOK-$QTY_DO WHERE KD_GDG='$KD_GDG1' AND KD_MATERIAL='$KD_MATERIAL1'";
			    $query_update3= oci_parse($conn, $update3);
				$hasil_update3 = oci_execute($query_update3);

		$update4= "UPDATE CRM_STOK_GDG SET STOK=STOK+$QTY_DO WHERE KD_GDG='$KD_GDG' AND KD_MATERIAL='$KD_MATERIAL'";
			    $query_update4= oci_parse($conn, $update4);
				$hasil_update4 = oci_execute($query_update4);
			   
	}	
	 
    
?> 
<script language=javascript>
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function ketik_produk(obj) {
	var strURL="ketik_produk.php?produk="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk() {	
		var strURL="cari_produk.php";
		popUp(strURL);
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto_all.php?&sold_to="+com_sold.value;
		popUp(strURL);
}
</script>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />


</head>
<body>

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Edit Release </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Edit </th>
</tr>
</table>
</div>

<form id="tambah"  method="post" action="<? echo $page; ?>" onSubmit="tes()">
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>    
	<tr>
      <td  class="puso">Kode Distributor</td>
      <td  class="puso">:</td>
      <td ><input name="kddistr" type="text" id="kddistr" size=12 value=" "/></td>
    </tr>
	<tr>
      <td  class="puso">No Polisi</td>
      <td  class="puso">:</td>
      <td ><input name="nopol" type="text" id="nopol" size=12 value=" "/></td>
    </tr>
	<tr>
      <td  class="puso">Priode</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Cari" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
<?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
 
		?>   
	<div align="center">
    <table width="470" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Edit Release</span></th>
	</tr>
	</div>
	</table>
    <form  action="crm_edit.php" method="post" name="temukan" id="temukan" onsubmit="return cek_input()">
    <table width="470" align="center" class="adminform">
	  <tr class="puso">
		<td align="left"><strong>No SPJ</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="hidden" name="NO_SPJ" size="10" value="<? echo $NO_SPJ[$i]; ?>" readonly="true" /><? echo $NO_SPJ[$i]; ?></td>
	 </tr>
	 <tr>
		 <td align="left"><strong>Kode Gudang</strong></td>
		 <td><b>:</b></td>
	    <td align="left"><div id="plantdiv">
		<input name="KD_GDG" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>
		&nbsp;&nbsp;&nbsp;&nbsp;
      	<input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
		
		</div></td>


	 </tr>
	 <tr>
		 <td align="left"><strong>Nama Gudang</strong></td>
		 <td><b>:</b></td><td><input name="NM_GDG" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20" onChange="ketik_plant(this)"/>
		</td>
		

	 </tr>
	  <tr>
		 <td align="left"><strong>Kode Distributor</strong></td>
		 <td><b>:</b></td>
	    <td align="left"><input type="hidden" name="KD_DISTR"  size="10"value="<? echo $KD_DISTR[$i]; ?>" readonly="true"/><? echo $KD_DISTR[$i]; ?></td>

	 </tr>
	 <tr>
		 <td align="left"><strong>Nama Distributor</strong></td>
		 <td><b>:</b></td>
	    <td align="left"><input type="hidden" name="NM_DISTR"  size="35"value="<? echo $NM_DISTR[$i]; ?>"readonly="true" /><? echo $NM_DISTR[$i]; ?></td>

	 </tr>
    <tr><!--
      <td  class="puso">Produk </td>
      <td  class="puso">:</td>
      <td ><div id="produkdiv">
	  <input name="produk" type="text" class="inputlabel" id="produk" value="<?=$produk?>" onChange="ketik_produk(this)" maxlength="10" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_produk" type="text" class="inputlabel" id="nama_produk" value="<?=$nama_produk?>" readonly="true"  size="30"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="btn_produk" type="button" class="button" id="btn_produk" value="..." onClick="findproduk()"/>
      <input name="val_error_produk" type="hidden" id="val_error_produk" value="0" />
    </div></td> -->
    </tr>

	 <tr>
		<td align="left"><strong >Kode Toko</strong></td>
		<td><b>:</b></td>
		<td align="left">
		  <input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
          <input name="sold_to" type="hidden" id="sold_to" value="<?=$distr_id?>"/><div id="shiptodiv">
		<input name="KD_SHIPTO" type="text" class="inputlabel" id="shipto" value="<?=$shipto?>" onChange="ketik_shipto(this)" maxlength="10" size="10"/>
		 <input name="btn_shipto" type="button" class="button" id="btn_shipto" value="..." onClick="findshipto()"/>
		</td>

	 </tr>
	 <tr>
		<td align="left"><strong >Nama Toko</strong></td>
		<td><b>:</b></td>
		<td align="left"><input name="NM_SHIPTO" type="text" class="inputlabel" id="nama_shipto" value="<?echo $nama_shipto?>" readonly="true"  size="30"/><?echo $nama_shipto?></td>
	 </tr>
	 <tr>
		<td align="left"><strong >Alamat</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="text" name="ALAMAT" class="inputlabel" id="alamat_shipto" value="<?=$alamat_shipto?>" size="50"/></td>

	 </tr>
	 <tr>
		 <td align="left"><strong>Kode Material</strong></td>
		 <td><b>:</b></td>
	    <td align="left">
		 <input name="KD_MATERIAL" type="text" class="inputlabel" id="produk" value="<?=$produk?>" onChange="ketik_produk(this)" maxlength="10" size="10"/>
		 <input name="btn_produk" type="button" class="button" id="btn_produk" value="..." onClick="findproduk()"/>
	 </tr>
	 <tr>
		 <td align="left"><strong>Nama Material</strong></td>
		 <td><b>:</b></td>
	    <td align="left">
		<input name="NM_MATERIAL" type="text" class="inputlabel" id="nama_produk" value="<?=$nama_produk?>" readonly="true"  size="30"/>
	</tr>
	 <tr>
		<td align="left"><strong >Tgl release</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="hidden" name="CREATE_DATE" size="10"  value="<? echo $CREATE_DATE[$i]; ?>" readonly="true"  /><? echo $CREATE_DATE[$i]; ?></td>

		</tr>
	 <tr>
		<td align="left"><strong >Kota</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="text" name="NAMA_KOTA"  value="<? echo $NAMA_KOTA[$i]; ?>"/><input type="hidden" name="DISTRIK"  value="<? echo $DISTRIK[$i]; ?>"/></td>

	 </tr>
	 <tr>
		<td align="left"><strong >QTY</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="text" name="QTY_DO" size="10" value="<? echo $QTY_DO[$i]; ?>"/></td>		
     </tr >
	 <tr>
		<td></td>
		<td></td>
		<td></td>		
     </tr >
	 <tr>
		<td></td>
		<td></td>
		<td></td>		
     </tr >
    <tr>
		<td colspan="3"><center>		
		<input name="simpan" type="submit" class="button" id="simpan" value="Update" />&nbsp;&nbsp;
		<input class="button" type=button value='Cancel' onClick="self.history.back();">
		<input type="hidden" name="NO_TRANSAKSI" size="10" value="<? echo $NO_TRANSAKSI[$i]; ?>"/>
		<input type="hidden" name="tgl" size="10" value="<? echo $tglstock ?>"/>
		<input type="hidden" name="tgl" size="10" value="<? echo $nm_gdg ?>"/>			
		<input type="hidden" name="KD_GDG1" size="10" value="<? echo $KD_GDG1[$i] ?>"/>
		<input type="hidden" name="KD_MATERIAL1" size="10" value="<? echo $KD_MATERIAL1[$i] ?>"/></center>
	</td>
	</tr>
	  <? } ?>
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
</table>
</body>
</html>