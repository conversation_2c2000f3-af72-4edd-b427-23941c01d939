<? 
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=875;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($_SESSION['distr_id']);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?
//
exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="daftar_gr_distr.php";
$no_spj = $fungsi->linenum($_POST['no_spj']);
$plant = $_POST['plant'];
$ship_to = $_POST['shipto'];
$produk = $_POST['produk'];
$status = $_POST['status'];
$tgl1 = $_POST['tgl1'];
$tgl2 = $_POST['tgl2'];
$sold_to = $_POST['sold_to'];
$currentPage="daftar_gr_distr.php";
$komen="";
if(isset($_POST['cari'])){
	if($no_spj=="" and $plant=="" and $status=="" and $ship_to == "" and $produk == "" and $tgl1 == "" and $tgl2 == ""){
		$sql= "SELECT CRM_GR_SPJ.*, to_char(TGL_GR,'DD-MM-YYYY HH24:MI:SS') as TGL_GR FROM CRM_GR_SPJ WHERE ORG='$user_org' AND KD_DISTR='$sold_to' ORDER BY ID ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT CRM_GR_SPJ.*, to_char(TGL_GR,'DD-MM-YYYY HH24:MI:SS') as TGL_GR FROM CRM_GR_SPJ WHERE ORG='$user_org' AND KD_DISTR='$sold_to' AND ";
		if($no_spj!=""){
			if($pakeor==1){
			$sql.=" AND NO_SPJ_GR LIKE '$no_spj' ";
			}else{
			$sql.=" NO_SPJ_GR LIKE '$no_spj' ";
			$pakeor=1;
			}
		}
		if($status!=""){
			if($pakeor==1){
			$sql.=" AND STATUS LIKE '$status' ";
			}else{
			$sql.=" STATUS LIKE '$status' ";
			$pakeor=1;
			}
		}
		if($plant!=""){
			if($pakeor==1){
			$sql.=" AND KD_GDG LIKE '$plant' ";
			}else{
			$sql.=" KD_GDG LIKE '$plant' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KD_MATERIAL LIKE '$produk' ";
			}else{
			$sql.=" KD_MATERIAL LIKE '$produk' ";
			$pakeor=1;
			}
		}
		if($tgl1!="" and $tgl2!=""){
			if ($tgl1=="")$tgl1_sql = "01-01-1990";
			else $tgl1_sql = $tgl1;
			if ($tgl2=="")$tgl2_sql = "12-12-9999";
			else $tgl2_sql = $tgl2;
			if($pakeor==1){
			$sql.=" AND TGL_GR BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			}else{
			$sql.=" TGL_GR BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			$pakeor=1;
			}
		}			
		$sql.=" ORDER BY ID ASC";
	}
	//echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$no_spj_v[]=$row['NO_SPJ_GR'];
		$tgl_gr_v[]=$row['TGL_GR'];
		$nopol_v[]=$row['NOPOL'];
		$sopir_v[]=$row['SOPIR'];
		$berat_ksg_v[]=$row['BERAT_KSG'];
		$berat_isi_v[]=$row['BERAT_ISI'];
		$berat_ksg1_v[]=$row['BERAT_KSG1'];
		$berat_isi1_v[]=$row['BERAT_ISI1'];
		$kdproduk_v[]=$row['KD_MATERIAL'];
		$produk_v[]=$row['NM_MATERIAL'];
		$sold_to_v[]=$row['KD_DISTR'];
		$namasold_to_v[]=$row['NM_DISTR'];
		$ship_to_v[]=$row['KD_SHIPTO'];
		$namaship_to_v[]=$row['NM_SHIPTO'];
		$qty_v[]=$row['QTY'];
		$id_v[]=$row['ID'];
	}
	$total=count($no_spj_v);
	if ($total < 1)$komen = '<div align="center" class="login">Tidak Ada Data Yang Ditemukan</div>';

}



?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		} 
		  
function ketik_distr(obj) {
	var com_org = document.getElementById('org');		
	var strURL="ketik_distr.php?org="+com_org.value+"&distr="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() { 
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {	
					document.getElementById("distrdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}


function ketik_produk(obj) {
	var strURL="ketik_produk.php?produk="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("produkdiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk() {	
		var strURL="cari_produk.php";
		popUp(strURL);
}
	
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Penerimaan :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Penerimaan </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search SPJ </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Gudang / Silo</td>
      <td  class="puso">:</td>
      <td ><div id="plantdiv">
	    <input name="plant" type="text" class="inputlabel" id="plant" value="<?=$plant_asal_up?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>&nbsp;&nbsp;&nbsp;&nbsp;
		<input name="nama_plant" type="text" id="nama_plant" value="<?=$nama_plant_up?>" readonly="true"size="20"/>&nbsp;&nbsp;&nbsp;&nbsp;
      	<input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
		<input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
	  </div></td>
    </tr>
    <tr>
      <td  class="puso">Ship To </td>
      <td  class="puso">:</td>
      <td ><input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
          <input name="sold_to" type="hidden" id="sold_to" value="<?=$distr_id?>"/><div id="shiptodiv">
      <input name="shipto" type="text" class="inputlabel" id="shipto" value="<?=$shipto?>" onChange="ketik_shipto(this)" maxlength="10" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_shipto" type="text" class="inputlabel" id="nama_shipto" value="<?=$nama_shipto?>" readonly="true"  size="30"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="btn_shipto" type="button" class="button" id="btn_shipto" value="..." onClick="findshipto()"/>
      <input name="val_error_shipto" type="hidden" id="val_error_shipto" value="0" />
    </div></td>
    </tr>
    <tr>
      <td  class="puso">Produk </td>
      <td  class="puso">:</td>
      <td ><div id="produkdiv">
	  <input name="produk" type="text" class="inputlabel" id="produk" value="<?=$produk?>" onChange="ketik_produk(this)" maxlength="10" size="10"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="nama_produk" type="text" class="inputlabel" id="nama_produk" value="<?=$nama_produk?>" readonly="true"  size="30"/>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <input name="btn_produk" type="button" class="button" id="btn_produk" value="..." onClick="findproduk()"/>
      <input name="val_error_produk" type="hidden" id="val_error_produk" value="0" />
    </div></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal Shipment</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
      <td  class="puso">Status</td>
      <td  class="puso">:</td>
      <td ><select name="status" id="Status" >
		  <option value="">---All---</option>
		  <option value="1">---Complete---</option>
          <option value="0">---Timbang Masuk---</option>
      </select></td>
    </tr> 
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
<form name="export" method="post" action="daftar_gr_xls.php">
<input name="plant" type="hidden" id="plant" value="<?=$plant?>"/>
<input name="no_so" id="no_so" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$no_so;?>"/>
<input type="hidden" value="<?=$sold_to;?>" class="inputlabel" id="sold_to" name="sold_to" size="10">
<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
<input name="ship_to" type="hidden" id="ship_to" size=12 value="<?=$ship_to?>"/>
<input name="produk" type="hidden" id="produk" size=12 value="<?=$produk?>"/>
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tgl1?>" />
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgl2?>" />
<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	
&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="Export" /> 	
</form>
	<table width="1200" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1200" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Penerimaan SPJ </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1200" align="center" class="adminlist">
	  
	  <tr class="quote">
	    <td rowspan="2" align="center"><strong>&nbsp;&nbsp;No.</strong></td>
	    <td rowspan="2" align="center"><strong >No SPJ</strong></td>
	    <td rowspan="2" align="center"><strong >Tgl Masuk</strong></td>
	    <td rowspan="2" align="center"><strong>No Polisi  </strong></td>
	    <td rowspan="2" align="center"><strong>Sopir  </strong></td>
	    <td rowspan="2" align="center"><strong>Kode Produk</strong></td>
	    <td rowspan="2" align="center"><strong>Nama Produk</strong></td>
	    <td colspan="3" align="center"><strong>Pengiriman Plant (Kg)</strong></td>
	    <td colspan="3" align="center"><strong>Penerimaan (Kg)</strong></td>
		<td rowspan="2" align="center"><strong>Selisih (Kg)</strong></td>
      </tr >
	  <tr class="quote">
		<td align="center"><strong>Berat Ksg (Kg)</strong></td>
		 <td align="center"><strong>Berat Isi (Kg)</strong></td>
		 <td align="center"><strong>Netto</strong></td>
		 <td align="center"><strong>Berat Ksg (Kg)</strong></td>
		 <td align="center"><strong>Berat Isi (Kg)</strong></td>
		 <td align="center"><strong>Netto</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {

		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_spj_v[$i]; ?></td>
		<td align="center"><? echo $tgl_gr_v[$i]; ?></td>
		<td align="center"><? echo $nopol_v[$i]; ?></td>
		<td align="left"><? echo $sopir_v[$i]; ?></td>
		<td align="left"><? echo $kdproduk_v[$i]; ?></td>
		<td align="left"><? echo $produk_v[$i]; ?></td>
		<td align="center"><? echo number_format($berat_ksg_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($berat_isi_v[$i],0,",","."); ?></td>
		<td align="center"><? $qty_v=$berat_isi_v[$i]-$berat_ksg_v[$i]; echo number_format($qty_v,0,",","."); ?></td>
		<td align="center"><? echo number_format($berat_ksg1_v[$i],0,",","."); ?></td>
		<td align="center"><? echo number_format($berat_isi1_v[$i],0,",","."); ?></td>
		<td align="center"><? $qty1_v=$berat_isi1_v[$i]-$berat_ksg1_v[$i]; echo number_format($qty1_v,0,",","."); ?></td>
		<td align="center"><? $selisih_v=$qty_v-$qty1_v; echo number_format($selisih_v,3,",","."); ?></td>
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="17" align="center"></td>
	    </tr>
	</table>

	</div>
	<? } ?>
<?
echo $komen;
?>

<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
