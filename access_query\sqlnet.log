

***********************************************************************
Fatal NI connect error 12170.

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 30-AUG-2022 09:00:31
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12535
    TNS-12535: Message 12535 not found; No message file for product=network, facility=TNS
    ns secondary err code: 12560
    nt main err code: 505
    TNS-00505: Message 505 not found; No message file for product=network, facility=TNS
    nt secondary err code: 110
    nt OS err code: 0
  Client address: <unknown>


***********************************************************************
Fatal NI connect error 12170.

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 30-AUG-2022 11:00:11
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12535
    TNS-12535: Message 12535 not found; No message file for product=network, facility=TNS
    ns secondary err code: 12560
    nt main err code: 505
    TNS-00505: Message 505 not found; No message file for product=network, facility=TNS
    nt secondary err code: 110
    nt OS err code: 0
  Client address: <unknown>


***********************************************************************
Fatal NI connect error 12170.

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 01-SEP-2022 02:58:12
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12535
    TNS-12535: Message 12535 not found; No message file for product=network, facility=TNS
    ns secondary err code: 12560
    nt main err code: 505
    TNS-00505: Message 505 not found; No message file for product=network, facility=TNS
    nt secondary err code: 110
    nt OS err code: 0
  Client address: <unknown>


***********************************************************************
Fatal NI connect error 12170.

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 01-SEP-2022 15:00:12
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12535
    TNS-12535: Message 12535 not found; No message file for product=network, facility=TNS
    ns secondary err code: 12560
    nt main err code: 505
    TNS-00505: Message 505 not found; No message file for product=network, facility=TNS
    nt secondary err code: 110
    nt OS err code: 0
  Client address: <unknown>
