<?
session_start();
include ('../include/ex_fungsi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$cancelUrl='mapping_leadtime_so_act.php?';
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$dirr = $_SERVER['PHP_SELF'];
//$halaman_id = $fungsi->get_halam_id($dirr);
$halaman_id = $fungsi->getmainhalam_id($conn,$dirr);

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
    ?>
                    <SCRIPT LANGUAGE="JavaScript">
                        alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
                    </SCRIPT>
    
         <a href="../index.php">Login....</a>
    <?
    
   exit();
    }

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Mapping Lead time</title>
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-dnd.js"></script>
</head>   
<body>

<!-- <div align="center">    -->
    <!-- <table id="dg" title="Data List Leadtime SO" class="easyui-datagrid" style="width:100%;height:350px">
        <thead>
            <tr>
                <th field="ID" width="5%" hidden="true">ID</th>
                <th field="PLANT" width="10%">PLANT</th>
                <th field="NAMA_PLANT" width="20%">NAMA PLANT</th>
                <th field="KOTA" width="10%">KOTA</th>
                <th field="NAMA_KOTA" width="25%">NAMA KOTA</th>
                <th field="STANDART_AREA" width="15%">STANDART AREA (Hari)</th>
                <th field="MATERIAL" width="15%">MATERIAL</th>
                <th field="DEL" width="5%">STATUS</th>
            </tr>
        </thead>
    </table> -->
    <div id="toolbar">
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="newAct()">QUERY</a>
    </div>
    
    <div id="dlg" class="easyui-dialog" style="width:700px;height:340px;padding:10px 20px"
         closed="true" buttons="#dlg-buttons">
        <div class="ftitle">Leadtime SO</div>
        <form id="fm" method="post" novalidate>
            <div class="fitem">
                <textarea name="input_query" id="input_query" rows="3" style="width:620px; height:170px;" cols="10"></textarea>
            </div>
        </form>
    </div>


    
    <div id="dlg-buttons">  
    <a href="javascript:void(0)" class="easyui-linkbutton c6" iconCls="icon-ok" onclick="saveAct()" style="width:90px" id="savedata">Save</a>
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-cancel" onclick="javascript:$('#dlg').dialog('close')" style="width:90px">Cancel</a>
    </div>
    <p id="result_query"></p>
<!-- </div> -->

<script type="text/javascript">
////////////////////

///////////////////



  function changeHandler(val)
  {
    if (Number(val.value) > 100)
    {
      val.value = 100
    }else if(val.value<=0){
        val.value = 0
    }
  }


 $(function(){
    $("#dg").datagrid({
            url:'query_act.php?act=qry_show',
            singleSelect:true,
            pagination:true, 
            pageList:[5,10,20,30,40,50,100,200,300],
            pageSize:10,
            rownumbers:true,
            loadMsg:'Processing,please wait',
            height:'auto', 
            toolbar:'#toolbar'
    });
    $('#dg').datagrid('enableFilter');
    
 }); 


 function newAct(){
    url = 'query_act.php?act=qry';
    $('#dlg').dialog('open').dialog('setTitle','Tabel Mapping Matrix Brand');
}
//////////
function saveAct(){

    var jalankanquery =     document.getElementById('input_query').value;
//        if(row.KET != 'Dibatalkan'){            
            $.messager.confirm('Confirm','Apakah Anda Yakin Ingin Mengupdate Statusnya?',function(r){
                if(r){                    
                    var strURL="query_act.php?act=qry&input_query="+jalankanquery;           
                    var req = getXMLHTTP();                    
                    if (req) {                        
                        req.onreadystatechange = function() {
                            $.messager.progress({
                                title:'Please waiting',
                                msg:'Loading data...'
                            });
                            if (req.readyState == 4) {
                                if (req.status == 200) {
                                    var mdxlconf = req.responseText;
                                    // alert(mdxlconf);
                                    document.getElementById("result_query").innerHTML = mdxlconf;
                                    $('#dlg').dialog('close'); // close the dialog
                                    // $('#dg').datagrid('reload'); // reload the user data
                                } else {
                                    alert("There was a problem while using XMLHTTP:\n" + req.statusText);
                                }
                                $.messager.progress('close');
                                $('#dg').datagrid('reload');
                            }				
                        }			
                        req.open("GET", strURL, true);
                        req.send(null);
                    }                    
                }
            })
}
///////////////////////////////
function savexAct(){
$('#fm').form('submit',{
    url: url,
    onSubmit: function(){ 
        return $(this).form('validate');
    },
    success: function(result){
        var result = eval('('+result+')');
        if (result.errorMsg){
            $.messager.show({
                title: 'Error',
                msg: result.errorMsg
            });
        } else {

            $('#dlg').dialog('close'); // close the dialog
            $('#dg').datagrid('reload'); // reload the user data
        }
    }
});
}

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

</script>
<style type="text/css">
#fm{
margin:0;
padding:10px;
}
#fm2{
margin:0;
padding:10px;
}
.ftitle{
font-size:14px;
font-weight:bold;
padding:5px 0;
margin-bottom:10px;
border-bottom:1px solid #ccc;
}
.fitem{
margin-bottom:5px;
}
.fitem label{
display:inline-block;
width:120px;
}
.fitem input{
width:160px;
}
</style>
</div>
<? 
// include ('../include/ekor.php'); 
?>
</body>
</html>
