

***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 09:54:52
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 19-DEC-2022 15:09:59
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 19-DEC-2022 15:14:02
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 12:58:53
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 13:15:36
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 28-DEC-2022 09:57:07
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 05-JAN-2023 16:36:36
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 10:07:54
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 19-JAN-2023 12:31:11
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 24-JAN-2023 11:02:10
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 17-JUL-2023 17:45:27
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 17-JUL-2023 20:39:34
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
