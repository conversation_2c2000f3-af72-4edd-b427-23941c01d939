<?
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();
		$sql="SELECT   A<PERSON>,
						A.<PERSON>_DISTR,
						A.<PERSON>IP<PERSON>, 
						A.NM_SHIPTO,
						A.ALAMAT,
						B.ORG,
						B.DELETE_MARK, 
						STATUS,        
						A.CREATE_DATE,
						SUM(B.BERAT_ISI - B.BERAT_KSG) AS  TOTAL
				FROM 
						CRM_SPJ_DTL A,
						CRM_SPJ_HDR B 
				WHERE 
						B.NO_TRANSAKSI=A.NO_TRANSAKSI AND STATUS='2' AND B.DELETE_MARK='0'AND  A.KD_SHIPTO IN ('2600043000','2600001000','2600001000')
				GROUP BY
						A.KD_DISTR,A.<PERSON>_<PERSON>,KD_SHIPTO,NM_SHIPTO,B.<PERSON>_MARK,STATUS,B.ORG,ALAMAT,A.<PERSON>_D<PERSON>E ";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$s=0;
		$tglstock = date('Ymd');
		while($data=oci_fetch_array($query)){
			$idnya[]=$data['ID'];
			$KD_DISTR[]=$data['KD_DISTR'];
			$NM_DISTR[]=$data['NM_DISTR'];
			$KD_SHIPTO[]=$data['KD_SHIPTO'];
			$NM_SHIPTO[]=$data['NM_SHIPTO'];
			$ALAMAT[]=$data['ALAMAT'];
			$ORG[]=$data['ORG'];
			$DELETE_MARK[]=$data['DELETE_MARK'];
			$STATUS[]=$data['STATUS'];
			$CREATE_DATE[]=$data['CREATE_DATE'];
			$TOTAL[]=$data['TOTAL'];

				$or_username = "dev";
				$or_password = "semeru2";
				$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
				$conn = oci_connect($or_username, $or_password, $or_db);	
				$sEndDate   = date('Ymd',strtotime($CREATE_DATE[$s]));	 
				$q_stock ="INSERT INTO PT_RELEASE_CURAH1 (NMOR, KUNNR, NAMA_KUNNR, NO_TOKO, NAMA_TOKO,TOTAL_VALUME,ALAMAT,STATUS,DELETE_FLAX,TGL_RELEASE,TGL_CREATE) values ('".$ORG[$s]."','".$KD_DISTR[$s]."','".$NM_DISTR[$s]."','".$KD_SHIPTO[$s]."','".$NM_SHIPTO[$s]."','".$TOTAL[$s]."','".$ALAMAT[$s]."',0,0,'".$sEndDate."','".$tglstock."')";
				$query1= oci_parse($conn, $q_stock);
				oci_execute($query1);
				$s++;
			echo $q_stock;
			}
				
			
?> 