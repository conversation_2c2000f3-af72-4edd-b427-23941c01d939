var chartStatver;
var chartStatPemb;

$(document).ready(function() {
  $(".container-indie").fadeOut(2000);
  var ex_date1 = $('#ex_date1').val();
  var ex_date2 = $('#ex_date2').val();
  var company = $("#company").val();
  reqInvExp(ex_date1, ex_date2, company);
});

$("#formStatInvExp").on("submit", function(e){
  e.preventDefault();
  var ex_date1 = $('#ex_date1').val();
  var ex_date2 = $('#ex_date2').val();
  var company = $("#company").val();
  reqInvExp(ex_date1, ex_date2, company, "readyChart");

})

function reqInvExp(date1, date2, company, set = null)
{
  var result;
  if(set == "readyChart"){
    chartStatver.destroy();
    chartStatPemb.destroy();
  }
  $.ajax({
    url: 'xhr/model2.php?p=stat-inv-exp',
    type: 'GET',
    dataType: 'JSON',
    data: {
      tglAwal: date1,
      tglAkhir: date2,
      company: company
    },
    beforeSend: function(){
      $(".exLoading").fadeIn(1000);
      $("#statEx").hide();
      $("#PenVer").hide();
      $("#exBend").hide();
      $("#terBer").hide();
      $(".labelBend").hide();
      $(".labelVer").hide();
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    $(".exLoading").fadeOut(1000, function() {
      if(result){

        // Day Average
        $("#avgBend").text(data.avgbend+" Hari");
        $("#avgVer").text(data.avgver+" Hari");

        //parsing _callbackPeriodeStatInvExp
        $("#_callbackPeriodeStatInvExp").val(data._callbackPeriodeStatInvExp);
        // eksver
        $("#statEx").show(1000);
        $("#statEx .count").html((data.singleData.EKSVER === null) ? 0 : data.singleData.EKSVER);
        $("#statEx .count_bottom i").html(toPersen(data.singleData.JMLEKSVER, data.singleData.EKSVER)+"% ");
        $("#statEx .count_bottom i").css('color', warnaPersen(toPersen(data.singleData.JMLEKSVER, data.singleData.EKSVER)));
        $("#statEx .count_bottom span").html((data.singleData.JMLEKSVER === null) ? 0 : data.singleData.JMLEKSVER);

        // terver
        $("#PenVer").show(1000);
        $("#PenVer .count").html((data.singleData.TERVER === null) ? 0 :data.singleData.TERVER);
        $("#PenVer .count_bottom i").html(toPersen(data.singleData.JMLEKSVER, data.singleData.TERVER)+"% ");
        $("#PenVer .count_bottom i").css('color', warnaPersen(toPersen(data.singleData.JMLEKSVER, data.singleData.TERVER)));
        $("#PenVer .count_bottom span").html((data.singleData.JMLEKSVER === null) ? 0 : data.singleData.JMLEKSVER);

        // exBend
        $("#exBend").show(1000);
        $("#exBend .count").html((data.singleData.EKSBEND === null) ? 0 : data.singleData.EKSBEND);
        $("#exBend .count_bottom i").html(toPersen(data.singleData.JMLEKSVER, data.singleData.EKSBEND)+"% ");
        $("#exBend .count_bottom i").css('color', warnaPersen(toPersen(data.singleData.JMLEKSVER, data.singleData.EKSBEND)));
        $("#exBend .count_bottom span").html((data.singleData.JMLEKSVER === null) ? 0 : data.singleData.JMLEKSVER);

        // terBer
        $("#terBer").show(1000);
        $("#terBer .count").html((data.singleData.TERBER === null) ? 0 : data.singleData.TERBER);
        $("#terBer .count_bottom i").html(toPersen(data.singleData.JMLEKSVER, data.singleData.TERBER)+"% ");
        $("#terBer .count_bottom i").css('color', warnaPersen(toPersen(data.singleData.JMLEKSVER, data.singleData.TERBER)));
        $("#terBer .count_bottom span").html((data.singleData.JMLEKSVER === null) ? 0 : data.singleData.JMLEKSVER);

        // label donant statVer
        $(".labelVer").show(1000);
        $("#statVerTerima").html((data.singleData.VERYES === null) ? 0 : data.singleData.VERYES);
        $("#statVerTolak").html((data.singleData.VERNO === null) ? 0 :data.singleData.VERNO);

        // label donat statPemb
        $(".labelBend").show(1000);
        $("#statPembSiap").html((data.singleData.STATBAYSIAP === null) ? 0 : data.singleData.STATBAYSIAP);
        $("#statPembBayar").html((data.singleData.STATBAYPAID === null) ? 0 : data.singleData.STATBAYPAID);
        $("#statPembTagih").html((data.singleData.STATBAYCOL === null) ? 0 : data.singleData.STATBAYCOL);

        //donat statVer
        var config = {
          type: 'pie',
          data: data.chartStatver,
          options: {
            responsive: true,
            legend: {
              position: 'bottom'
            },
            maintainAspectRatio: false,
            title:{
              display: true,
              text: 'Status Verifikasi'
            },
            animation: {
              animateScale: true,
              animateRotate: true
            },
            tooltips:{
              callbacks:{
                label: function(tooltipItem, data) {
                  var dataset = data.datasets[tooltipItem.datasetIndex];
                  var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                    return previousValue + currentValue;
                  });
                  var currentValue = dataset.data[tooltipItem.index];
                  var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                  return precentage + "%";
                }
              }
            },
            elements: {
              arc: {
                borderWidth: 0
              }
            }
          }
        };
        var ctx = document.getElementById("statVer").getContext("2d");
        chartStatver = new Chart(ctx, config);

        // donat statPemb
        var config1 = {
          segmentShowStroke: false,
          type: 'pie',
          data: data.chartStatPemb,
          options: {
            responsive: true,
            legend: {
              position: 'bottom'
            },
            maintainAspectRatio: false,
            title:{
              display: true,
              text: 'Status Pembayaran'
            },
            animation: {
              animateScale: true,
              animateRotate: true
            },
            tooltips:{
              callbacks:{
                label: function(tooltipItem, data) {
                  var dataset = data.datasets[tooltipItem.datasetIndex];
                  var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                    return previousValue + currentValue;
                  });
                  var currentValue = dataset.data[tooltipItem.index];
                  var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                  return precentage + "%";
                }
              }
            },
            elements: {
              arc: {
                borderWidth: 0
              }
            }
          }
        };
        var ctx1 = document.getElementById("statPemb").getContext("2d");
        chartStatPemb = new Chart(ctx1, config1);
      }
    });
  });
}

$(".toDetailInvExp").on("click", function(e){
  e.preventDefault();
  var result = $(this).closest('.tile_stats_count').find('div.count').text();
  if(result == "0")
  {
    $(this).removeAttr('data-target');
    $(this).removeAttr('data-toggle');
    toastr.options = {
      "closeButton": false,
      "debug": false,
      "newestOnTop": false,
      "progressBar": false,
      "positionClass": "toast-top-right",
      "preventDuplicates": false,
      "onclick": null,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "1000",
      "extendedTimeOut": "1000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    }
    toastr["warning"]("Tidak ada data yg akan di tampilkan");
  } else {
    $(this).attr('data-target', '#modalDetailStatInvExp');
    $(this).attr('data-toggle', 'modal');
  }
})

$("#modalDetailStatInvExp").on("show.bs.modal", function(e){
  $(this).find('.modal-title').html("Detail "+$(e.relatedTarget).text());
  var msg = $(e.relatedTarget).data("message");
  var periode = $("#_callbackPeriodeStatInvExp").val().split("|");
  var tglAwal = periode[0];
  var tglAkhir = periode[1];
  var company = $("#company").val();
  $("#detailStatInvExp").find('tbody tr').remove();
  $.ajax({
    url: 'xhr/model2.php?p=stat-inv-exp-detail',
    type: 'GET',
    dataType: 'JSON',
    data: {
      p : 'stat-inv-exp-detail',
      proses : msg,
      tglAwal : tglAwal,
      tglAkhir : tglAkhir,
      company: company
    },
    beforeSend: function(){
      $("#loadingModalStatInvExp").show();
    }
  })
  .done(function() {
    console.log("success");
  })
  .fail(function() {
    alert("internal server error");
  })
  .always(function(data) {
    if(data.detail !== false){
      setTimeout(function () {
        $("#loadingModalStatInvExp").fadeOut('slow', function() {
          var i = 1;
          $.each(data.detail, function(index, detail){
            var content = "<tr style='text-align:center;'>"+
                             "<td>"+i+"</td>"+
                             "<td>"+detail.NO_INV_SAP+"</td>"+
                             "<td>"+detail.NAME1+"</td>"+
                             "<td>"+detail.TGL+"</td>"+
                          "<tr>";
            $("#detailStatInvExp").find('tbody').append(content);
           i++;
          })
        });
      }, 2000);
    }
  });

})


$(".btnClose").click(function(e) {
  e.preventDefault();
  $(this).closest('.panel-group').fadeOut('slow', function() {
    $(this).remove();
  });
});


function toPersen(jml, nilai)
{
  var result = (nilai/jml)*100;
  return isNaN(result) ? 0 : result.toFixed(2);
}

function warnaPersen(nilai)
{
  if(nilai < 30){
    return "#64DD17";
  } else if(nilai > 30 && nilai < 70) {
    return "#FFD600";
  } else if(nilai > 70){
    return "#F44336";
  } else{
    return "embuuh";
  }
}
