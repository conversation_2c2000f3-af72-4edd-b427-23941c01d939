<?
include ('../include/fungsi.php');
class crm_fungsi extends fungsi 
{
//	var $or_username = "APPSGG";
//	var $or_password = "semengresik";
//	var $or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';

	var $or_username = "APPSGG";
	var $or_password = "sgmerdeka99";
	var $or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = cmsdb.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';

	public function crm_koneksi()
	{
		$conn = oci_connect($this->or_username, $this->or_password, $this->or_db );
		if (!$conn)
			return false;
		else
		 return $conn;
	}
	function or_status_shipment($ting){
		$k=array(1 => '10', '20', '30', '40', '50', '70');
		$nama=array(1 => '10-Antri', '20-Entri SPPS', '30-Matching Kota','40-Matching Alamat','50-Timbang Masuk','70-Complete');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function or_order_type($ting){
		$k=array(1 => 'ZOR', 'ZFD', 'ZFC', 'ZEX','ZPR','ZOR');
		$nama=array(1 => 'Sales Standart', 'Sales Ganti Barang', 'Sales FOC','Sales Export','Sales Proyek', 'Sales Intercompany');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$nama[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}

	function or_jns_plant($ting){
		$k=array(1 => 'PABRIK', 'GUDANG');
		$nama=array(1 => 'PABRIK', 'GUDANG');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$nama[$x]</option>");
			}
	}
	function or_jenis_kirim($ting){
		$k=array(1 => 'CIF','CNF','FOB','FOT','FRC');
		$nama=array(1 => 'Cost, Insurance & Freight', 'Cost and Freight','Free on Board','Free on Truck','Franco');
		
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]' title='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x] - $nama[$x]</option>");
			}
	}
	function or_status($ting){
		$k=array(1 => 'OPEN','PROCESS','APPROVE','REJECTED');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}

	function or_bayar($ting){
		$k=array(1 => 'KREDIT','TUNAI');
		for($x=1;$x<=count($k);$x++)
			{
			echo("<option value='$k[$x]'");
			if($k[$x]==$ting){echo("selected");}echo(">$k[$x]</option>");
			}
	}

	function tgl_terima($tgl,$asal,$tujuan){
			$sql_jarak= "SELECT WAKTU FROM OR_LAMA_KIRIM WHERE ASAL='$asal' AND TUJUAN = '$tujuan' AND START_DATE <= To_date('$tgl','DD-MM-YYYY') AND (END_DATE >= To_date('$tgl','DD-MM-YYYY') OR END_DATE IS NULL) AND DELETE_MARK = '0' ORDER BY START_DATE ASC";			
				$conn = $this->or_koneksi();
				$query_jarak=oci_parse($conn,$sql_jarak);
				oci_execute($query_jarak);
				$row_jarak=oci_fetch_assoc($query_jarak);
				$jarak_up=$row_jarak[WAKTU]; 
				
				if ($jarak_up==0 or $jarak_up ==""){
					list($hari,$bln,$thn)=split("-",$tgl);
					$ubah_hari=$hari+7;
					$tgl_terima = date("d-m-Y", mktime (0,0,0,$bln,$ubah_hari,$thn)); 
				}else{
					
					list($hari,$bln,$thn)=split("-",$tgl);
					$ubah_hari=$hari+ceil($jarak_up/24);
					$tgl_terima = date("d-m-Y", mktime (0,0,0,$bln,$ubah_hari,$thn)); 
				}
		return $tgl_terima;
	}

	
	function shipto_gdg($gdg,$shipto){
		$conn = $this->crm_koneksi();
		$sql= "SELECT * FROM CRM_GDG_VS_SHIPTO WHERE KD_GDG='$gdg'";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$a =0;
		while ($row = oci_fetch_array($query)){
		$a += 1;
		$kode[$a]= $row['KD_SHIPTO'];
		$nama[$a]=$row['NM_SHIPTO'];
		$daftar[$a]=$kode[$a].'-'.$nama[$a];
		}
		$daftar[0]="--Pilih Shipto--";
		$kode[0]="";
		for($x=0;$x<=$a;$x++)
		{
		echo("<option value='$kode[$x]' title='$nama[$x]'");
		if($kode[$x] == $shipto){echo("selected");}
		echo(">$daftar[$x]</option>");
		}
	}
	
	public function or_clearsessi_all()
	{
		$this->or_clearsessi_waktu_kirim();
	}
	public function or_clearsessi_waktu_kirim()
	{
		unset($_SESSION['or_asal_waktu']);
		unset($_SESSION['or_nama_asal_waktu']);
		unset($_SESSION['or_tujuan_waktu']);
		unset($_SESSION['or_nama_tujuan_waktu']);
		unset($_SESSION['or_incoterm_waktu']);
		unset($_SESSION['or_nama_incoterm_waktu']);
		unset($_SESSION['or_vehicle_type_waktu']);
		unset($_SESSION['or_waktu_waktu']);
		unset($_SESSION['or_keterangan_waktu']);
		unset($_SESSION['or_tgl_mulai_waktu']);
		unset($_SESSION['or_tgl_selesai_waktu']);
	}
	function crm_new_so_number($conn)
	{
		$sql="SELECT CRM_SO_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
                if($panjang==6)$new_number_ok=$new_number;
		return $new_number_ok;

	}
	function crm_new_spj($conn)
	{
		$sql="SELECT CRM_SPJ_HDR_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
		if($panjang==6)$new_number_ok=$new_number;
		return $new_number_ok;
	}
	function crm_new_spj_dtl($conn)
	{
		$sql="SELECT CRM_SPJ_DTL_SEQ.NEXTVAL FROM SYS.DUAL";
		$result= oci_parse($conn, $sql);
		oci_execute($result);
		$data = oci_fetch_assoc ($result);
		$new_number = $data['NEXTVAL'];

		$panjang=strlen(strval($new_number));
		if($panjang==1)$new_number_ok='00000'.$new_number;
		if($panjang==2)$new_number_ok='0000'.$new_number;
		if($panjang==3)$new_number_ok='000'.$new_number;
		if($panjang==4)$new_number_ok='00'.$new_number;
		if($panjang==5)$new_number_ok='0'.$new_number;
		if($panjang==6)$new_number_ok=$new_number;
		return $new_number_ok;
	}
	
	function sapcode($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$sapcode='000000000'.$kode;
		if($panjang==2)$sapcode='00000000'.$kode;
		if($panjang==3)$sapcode='0000000'.$kode;
		if($panjang==4)$sapcode='000000'.$kode;
		if($panjang==5)$sapcode='00000'.$kode;
		if($panjang==6)$sapcode='0000'.$kode;
		if($panjang==7)$sapcode='000'.$kode;
		if($panjang==8)$sapcode='00'.$kode;
		if($panjang==9)$sapcode='0'.$kode;
		if($panjang==10)$sapcode=$kode;
		return $sapcode;
	}
	function linenum($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$linenum='00000'.$kode;
		if($panjang==2)$linenum='0000'.$kode;
		if($panjang==3)$linenum='000'.$kode;
		if($panjang==4)$linenum='00'.$kode;
		if($panjang==5)$linenum='0'.$kode;
		if($panjang==6)$linenum=$kode;
		return $linenum;
	}
	function gudang($kode)
	{
		$panjang=strlen(strval($kode));
		if($panjang==1)$gudang='00'.$kode;
		if($panjang==2)$gudang='0'.$kode;
		if($panjang==3)$gudang=$kode;
		return $gudang;
	}
    function getDurasi1($tgl1,$jam1,$tgl2,$jam2){
	if($tgl1!='' && $jam1!='' && $tgl2!='' && $jam2!=''){
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("SD_CALC_DURATION1");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$fce->I_DATE1=$tgl1;
		$fce->I_TIME1=$jam1;
    	$fce->I_DATE2=$tgl2;
		$fce->I_TIME2=$jam2;
	  
				
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {	
			return $COUNT = $fce->E_TDIFF;		
		}else

        $fce->PrintStatus();

		#$fce->Close();	
		#$sap->Close();	
	} else return 0;
}
}
?>