<?php
session_start();
require_once ('../../pgr_sanitizer.php');

$_REQUEST = sanitize_input($_REQUEST, TRUE);
$_POST= sanitize_input($_POST, TRUE);
$_GET= sanitize_input($_GET, TRUE);

  include ('../../include/ex_fungsi.php');
  $fungsi=new ex_fungsi();
  $conn=$fungsi->ex_koneksi();
  $user_id=trim($_SESSION['user_id']);
  $user_org=$_SESSION['user_org'];
  $user_name=$_SESSION['user_name'];
  
  $user_data=$fungsi->ex_find_user($conn,$user_id);
  $vendor=$user_data["vendor"];  
  
  if($vendor != ""){
      $filter .= " AND I.NO_VENDOR ='".$vendor."'"; // Filter tabel ex_invoice
      $filter1 .= " AND VENDOR ='".$vendor."'"; // Filter tabel ex_trans_hdr
      $filter2 .= " AND NO_VENDOR ='".$vendor."'"; // Filter tabel kpi_terima_invoice_vendor
  } else {
      $filter .= "";
      $filter1 .= "";
      $filter2 .= "";
  }
      
  if($user_id==''){
    echo json_encode(array("Auth"=>false));
  } else {
    $p = (!empty($_GET["p"]) ? $_GET["p"] : "");
    switch ($p) {
      case 'multiline-spj':

        $label = array();
        $tglAwal = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
        $tglAkhir = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $tgl1 = date("d", strtotime($tglAwal));
        $tgl2 = date("d", strtotime($tglAkhir));
        $label[] .= $tglAwal;
        while (strtotime($tglAwal) < strtotime($tglAkhir)) {
          $tglAwal = date ("Y-m-d", strtotime("+1 day", strtotime($tglAwal)));
          $label[] .= $tglAwal;
  	    }        
        //query1
        $data = array();
        
          foreach ($label as $date) {
            $q2 = "SELECT TO_CHAR(CREATE_DATE, 'YYYY-MM-DD') AS CREATE_DATE, SUM(TOTAL_DRAFT_OPEN) AS TOTAL_DRAFT_OPEN, SUM(TOTAL_OPEN_OPEN) AS TOTAL_OPEN_OPEN, SUM(TOTAL_PROGRESS_OPEN) AS TOTAL_PROGRESS_OPEN, SUM(TOTAL_INVOICED_OPEN) AS TOTAL_INVOICED_OPEN FROM (
                  	SELECT CREATE_DATE, COUNT (NO_SHP_TRN) AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'DRAFT' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                  UNION
                  SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, COUNT (NO_SHP_TRN) AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'OPEN' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                  UNION
                  SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'PROGRESS' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                  UNION
                  SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'INVOICED' AND (STATUS2 = 'OPEN' OR STATUS2='INVOICED' OR STATUS2='PARTIAL_INVOICED') AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                  ) A
                  WHERE CREATE_DATE = TO_DATE('".$date."', 'YYYY-MM-DD')
                  GROUP BY CREATE_DATE";
            $parse2 = oci_parse($conn, $q2);
            oci_execute($parse2);
            $arr2 = oci_fetch_assoc($parse2);
            $dataDraftOpen[] .= ($arr2["TOTAL_DRAFT_OPEN"] != "") ? (int)$arr2["TOTAL_DRAFT_OPEN"] : (int)0;
            $dataOpenOpen[] .= ($arr2["TOTAL_OPEN_OPEN"] != "") ? (int)$arr2["TOTAL_OPEN_OPEN"] : (int)0;
            $dataProgressOpen[] .= ($arr2["TOTAL_PROGRESS_OPEN"] != "") ? (int)$arr2["TOTAL_PROGRESS_OPEN"] : (int)0;
            $dataInvoicedOpen[] .= ($arr2["TOTAL_INVOICED_OPEN"] != "") ? (int)$arr2["TOTAL_INVOICED_OPEN"] : (int)0;

          }
//          echo "query".$q2;

        $datasets = array(
          array(
            "label" => "Outstanding Expediture",
            "backgroundColor" => "#F44336",
            "borderColor" => "#F44336",
            "data" => $dataDraftOpen,
            "fill" => false,
          ),
          array(
            "label" => " Outstanding Distributor",
            "backgroundColor" => "#FF9800",
            "borderColor" => "#FF9800",
            "data" => $dataOpenOpen,
            "fill" => false,
          ),
          array(
            "label" => "Create Invoice",
            "backgroundColor" => "#4CAF50",
            "borderColor" => "#4CAF50",
            "data" => $dataProgressOpen,
            "fill" => false,
          ),
          array(
            "label" => " Invoiced",
            "backgroundColor" => "#009688",
            "borderColor" => "#009688",
            "data" => $dataInvoicedOpen,
            "fill" => false,
          ),
        );

        $grap = array("labels" => $label, "datasets" => $datasets);

        $tglAwal1 = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
        $tglAkhir1 = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
        // all data
        $q3 = "SELECT COUNT(NO_SHP_TRN) AS TOTAL FROM EX_TRANS_HDR
        WHERE CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD') AND STATUS2 != 'DRAFT' AND ORG = '".$company."'".$filter1."";
        $parse3 = oci_parse($conn, $q3);
        oci_execute($parse3);
        $arr3 = oci_fetch_assoc($parse3);
        $allData = $arr3["TOTAL"];

        //counter presentase
        $q4 = "SELECT SUM(TOTAL_DRAFT_OPEN) AS TOTAL_DRAFT_OPEN, SUM(TOTAL_OPEN_OPEN) AS TOTAL_OPEN_OPEN, SUM(TOTAL_PROGRESS_OPEN) AS TOTAL_PROGRESS_OPEN, SUM(TOTAL_INVOICED_OPEN) AS TOTAL_INVOICED_OPEN FROM (
                	SELECT CREATE_DATE, COUNT (NO_SHP_TRN) AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'DRAFT' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, COUNT (NO_SHP_TRN) AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'OPEN' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'PROGRESS' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'INVOICED' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                ) A
                WHERE CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD')";
        $parse4 = oci_parse($conn, $q4);
        oci_execute($parse4);
        $arr4 = oci_fetch_assoc($parse4);
        error_reporting(0);
        $presentanse["presentase_draft_open"] = ($arr4["TOTAL_DRAFT_OPEN"]/$allData)*100;
        $presentanse["presentase_open_open"] = ($arr4["TOTAL_OPEN_OPEN"]/$allData)*100;
        $presentanse["presentase_progress_open"] = ($arr4["TOTAL_PROGRESS_OPEN"]/$allData)*100;
        $presentanse["presentase_invoiced_open"] = ($arr4["TOTAL_INVOICED_OPEN"]/$allData)*100;

        $qty["TOTAL_DRAFT_OPEN"] = (empty($arr4["TOTAL_DRAFT_OPEN"])) ? 0 : $arr4["TOTAL_DRAFT_OPEN"];
        $qty["TOTAL_OPEN_OPEN"] = (empty($arr4["TOTAL_OPEN_OPEN"]) ? 0 : $arr4["TOTAL_OPEN_OPEN"]);
        $qty["TOTAL_PROGRESS_OPEN"] = (empty($arr4["TOTAL_PROGRESS_OPEN"]) ? 0 : $arr4["TOTAL_PROGRESS_OPEN"]);
        $qty["TOTAL_INVOICED_OPEN"] = (empty($arr4["TOTAL_INVOICED_OPEN"]) ? 0 : $arr4["TOTAL_INVOICED_OPEN"]);

        //select last ten
        $baseQuery = "SELECT * FROM(
              	SELECT CREATE_DATE, NO_SHP_TRN, NAMA_VENDOR, TANGGAL_KIRIM, STATUS, STATUS2 FROM EX_TRANS_HDR WHERE ORG = '".$company."'".$filter1." ORDER BY TANGGAL_KIRIM DESC
              ) WHERE ROWNUM <=10 AND CREATE_DATE is not null
              AND CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD')  ";

        //draftopen;
        $q5 = $baseQuery." AND STATUS = 'DRAFT' AND STATUS2 = 'OPEN' ";
        $parse5 = oci_parse($conn, $q5);
        oci_execute($parse5);
        $dataDo = array();
        while($arr = oci_fetch_assoc($parse5)){
          $dataDo[] = $arr;
        }

        // openopen
        $q6 = $baseQuery." AND STATUS = 'OPEN' AND STATUS2 = 'OPEN' ";
        $parse6 = oci_parse($conn, $q6);
        oci_execute($parse6);
        $dataOo = array();
        while($arr5 = oci_fetch_assoc($parse6)){
          $dataOo[] = $arr5;
        }

        // PROGRESOPEN
        $q7 = $baseQuery." AND STATUS = 'PROGRESS' AND STATUS2 = 'OPEN' ";
        $parse7 = oci_parse($conn, $q7);
        oci_execute($parse7);
        $dataPo = array();
        while($arr6 = oci_fetch_assoc($parse7)){
          $dataPo[] = $arr6;
        }

        // INVOICEDOPEN
        $q8 = $baseQuery." AND STATUS = 'INVOICED' AND STATUS2 = 'OPEN' ";
        $parse8 = oci_parse($conn, $q8);
        oci_execute($parse8);
        $dataIo = array();
        while($arr7 = oci_fetch_assoc($parse8)){
          $dataIo[] = $arr7;
        }

        echo json_encode(array(
          "grap"=>$grap,
          "presentase"=>$presentanse,
          "qty" => $qty,
          "DataDo"=>$dataDo,
          "DataOo"=>$dataOo,
          "DataPo"=>$dataPo,
          "DataIo"=>$dataIo,
          "totalData"=>$allData,
        ));

      break;

      case 'donat-etelment':
        $from = (!empty($_GET["from_etelment"]) ? $_GET["from_etelment"] : "");
        $to = (!empty($_GET["to_etelment"]) ? $_GET["to_etelment"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        // CHART
        $q1 = "SELECT
              	SUM (SUKSES) AS SUKSES,
              	SUM (GAGAL) AS GAGAL
              FROM
              	(
              		SELECT
              			COUNT (KET_ERROR) AS SUKSES,
              			0 AS GAGAL
              		FROM
              			EX_TRANS_HDR
              		WHERE
              			KET_ERROR = 'OK'
              		AND CREATE_DATE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              		AND TO_DATE ('".$to."', 'YYYY-MM-DD')
                        AND ORG = '".$company."'".$filter1."
              		UNION ALL
              			SELECT
              				0 AS SUKSES,
              				COUNT (KET_ERROR) AS GAGAL
              			FROM
              				EX_TRANS_HDR
              			WHERE
              				KET_ERROR IS NOT NULL
              			AND KET_ERROR != 'OK'
              			AND CREATE_DATE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              			AND TO_DATE ('".$to."', 'YYYY-MM-DD')
                    AND ORG = '".$company."'".$filter1."
              	) A";
                $parse = oci_parse($conn, $q1);
                oci_execute($parse);
                $arr = oci_fetch_assoc($parse);
                $chart = array(
                  "datasets" => array(
                    array(
                      "data" => array(intval($arr["SUKSES"]), intval($arr["GAGAL"])),
                      "backgroundColor" => array("#009688", "#F44336"),
                      "label" => "Status Cost Etelment"
                    )
                  ),
                  "labels" => array("SUKSES", "GAGAL")
                );

                // DETAIL
                $q2 = "SELECT * FROM(
                        	SELECT
                        	NO_INVOICE,
                        	NAMA_VENDOR,
                        	CREATE_DATE,
                        	KET_ERROR
                        FROM
                        	EX_TRANS_HDR
                        WHERE
                        	KET_ERROR IS NOT NULL
                        AND KET_ERROR != 'OK'
                        AND CREATE_DATE BETWEEN TO_DATE('".$from."', 'YYYY-MM-DD') AND TO_DATE('".$to."', 'YYYY-MM-DD')
                        AND ORG = '".$company."'".$filter1."
                        ) WHERE ROWNUM <= 10";
                $parse2 = oci_parse($conn, $q2);
                oci_execute($parse2);
                $detail = array();
                while ($arr1 = oci_fetch_assoc($parse2)) {
                  $detail[] = $arr1;
                }
                echo json_encode(array("chart"=>$chart, "detail"=>$detail));
      break;

//      case 'vendor-request':
//        $statInvDate1 = (!empty($_GET["statInvDate1"]) ? $_GET["statInvDate1"] : "");
//        $statInvDate2 = (!empty($_GET["statInvDate2"]) ? $_GET["statInvDate2"] : "");
//        $q1 = "SELECT DISTINCT
//                	(NO_VENDOR),
//                	NAMA_VENDOR
//                FROM
//                	KPI_TERIMA_INV_VENDOR
//                WHERE
//                	NO_VENDOR IS NOT NULL
//                AND NAMA_VENDOR IS NOT NULL
//                AND TRUNC(TGL_TERIMA) BETWEEN TO_DATE('".$statInvDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statInvDate2."', 'YYYY-MM-DD')";
//         $parse = oci_parse($conn, $q1);
//         oci_execute($parse);
//         $result = array();
//         while ($arr = oci_fetch_assoc($parse)) {
//           $result[] = $arr;
//         }
//         echo json_encode($result);
//      break;
      
      case 'doghnut-stat-inv':
        // GRAFIK DONAT
        $statInvDate1 = (!empty($_GET["statInvDate1"]) ? $_GET["statInvDate1"] : "");
        $statInvDate2 = (!empty($_GET["statInvDate2"]) ? $_GET["statInvDate2"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $vendor = (!empty($_GET["vendor"]) ? " AND NO_VENDOR = '".$_GET["vendor"]."' " : "");
        $q1 = "SELECT SUM(TOTAL_SUDAH) AS TOTAL_SUDAH, SUM(TOTAL_BELUM) AS TOTAL_BELUM FROM (
              	SELECT COUNT(NO_INVOICE) AS TOTAL_SUDAH, 0 AS TOTAL_BELUM FROM KPI_TERIMA_INV_VENDOR WHERE DATE_PRINTTERM IS NOT NULL
              	AND TRUNC(TGL_TERIMA) BETWEEN TO_DATE('".$statInvDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statInvDate2."', 'YYYY-MM-DD')
                ".$vendor."
                AND ORG = '".$company."'".$filter2."
              	UNION ALL
              	SELECT 0 AS TOTAL_SUDAH, COUNT(NO_INVOICE) FROM KPI_TERIMA_INV_VENDOR  WHERE DATE_PRINTTERM IS NULL
              	AND TRUNC(TGL_TERIMA) BETWEEN TO_DATE('".$statInvDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statInvDate2."', 'YYYY-MM-DD')
                ".$vendor."
                AND ORG = '".$company."'".$filter2."
              ) A";
        $parse9 = oci_parse($conn, $q1);
        oci_execute($parse9);
        $arr8 = oci_fetch_assoc($parse9);
        $data = array(
          "datasets" => array(
            array(
              "data"=> array(intval($arr8["TOTAL_SUDAH"]), intval($arr8["TOTAL_BELUM"])),
              "backgroundColor" => array("#009688", "#F44336"),
              "label" => "Status Cetak Invoice"
            )
          ),
          "labels" => array("SUDAH", "BELUM")
        );

        // DATADETAIL
        $dataDet = array();
        $q2 = "SELECT * FROM(
                	SELECT NO_INVOICE, NAMA_VENDOR, TGL_TERIMA FROM KPI_TERIMA_INV_VENDOR  WHERE DATE_PRINTTERM IS NULL
                	AND TRUNC(TGL_TERIMA) BETWEEN TO_DATE('".$statInvDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statInvDate2."', 'YYYY-MM-DD')
                  ".$vendor."
                  AND ORG = '".$company."'".$filter2."
                  ORDER BY TGL_TERIMA ASC, NO_INVOICE ASC
                ) WHERE ROWNUM <=10";
        $parse10 = oci_parse($conn, $q2);
        oci_execute($parse10);
        while($arr9 = oci_fetch_assoc($parse10)){
          $dataDet[] = $arr9;
        }
        echo json_encode(array("grap" => $data, "dataDetail"=>$dataDet));
      break;

      case 'doghnut-stat-tag':
        // GRAFIK DONAT STATUS INVOICE
        $statTagDate1 = (!empty($_GET["statTagDate1"]) ? $_GET["statTagDate1"] : "");
        $statTagDate2 = (!empty($_GET["statTagDate2"]) ? $_GET["statTagDate2"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $q1 = "SELECT COUNT(V.NO_INVOICE) as INVYGSUDAHDITERIMA,COUNT(I.NO_INVOICE) as JMLINV,COUNT(I.NO_INVOICE)-COUNT(V.NO_INVOICE) as INVYGBLMDITERIMA FROM EX_INVOICE I
              LEFT JOIN KPI_TERIMA_INV_VENDOR V ON V.NO_INVOICE=I.NO_INVOICE
              WHERE I.DELETE_MARK=0 AND TRUNC(TGL_INVOICE) BETWEEN TO_DATE('".$statTagDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statTagDate2."', 'YYYY-MM-DD')
              AND I.ORG = '".$company."'".$filter."";
        $parse11 = oci_parse($conn, $q1);
        oci_execute($parse11);
        $arr10 = oci_fetch_assoc($parse11);
        $data = array(
          "datasets" => array(
            array(
              "data" => array(intval($arr10["INVYGSUDAHDITERIMA"]), intval($arr10["INVYGBLMDITERIMA"])),
              "backgroundColor" => array("#009688", "#F44336"),
              "label" => "Status Penerimaan Tagihan"
            )
          ),
          "labels" => array("DITERIMA", "BELUM")
        );
        // DATA DETAIL
        $q2 = "SELECT * FROM (
              	SELECT
              	I.NO_INVOICE, I.NAMA_VENDOR, I.TGL_INVOICE
              	FROM
              		EX_INVOICE I
              	LEFT JOIN KPI_TERIMA_INV_VENDOR V ON V.NO_INVOICE = I.NO_INVOICE
              	WHERE
              		I.DELETE_MARK = 0
              	AND V.NO_INVOICE IS NULL
              	AND TRUNC(TGL_INVOICE) BETWEEN TO_DATE ('".$statTagDate1."', 'YYYY-MM-DD')
              	AND TO_DATE ('".$statTagDate2."', 'YYYY-MM-DD')
                AND I.ORG = '".$company."'".$filter."
              	ORDER BY TGL_INVOICE ASC, NO_INVOICE ASC
              ) WHERE ROWNUM <= 10";
        $parse12 = oci_parse($conn, $q2);
        oci_execute($parse12);
        $dataDet = array();
        while($arr10 = oci_fetch_assoc($parse12)){
          $dataDet[] = $arr10;
        }
        echo json_encode(array("grap"=>$data, "tenDetail"=>$dataDet));
      break;

      case 'banner-approve':
        $approveDate1 = (!empty($_GET["approveDate1"]) ? $_GET["approveDate1"] : "");
        $approveDate2 = (!empty($_GET["approveDate2"]) ? $_GET["approveDate2"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $q1 = "SELECT
              	SUM (BLMAPPROVE) AS BLMAPPROVE,
              	SUM (KASIAPPROVE) AS KASIAPPROVE,
              	SUM (KABIROAPPROVE) AS KABIROAPPROVE,
              	SUM (KASIKABIROAPPROVE) AS KASIKABIROAPPROVE
              FROM
              	(
              		SELECT
              			COUNT (NO_INVOICE) AS BLMAPPROVE,
              			0 AS KASIAPPROVE,
              			0 AS KABIROAPPROVE,
              			0 AS KASIKABIROAPPROVE
              		FROM
              			EX_INVOICE I
              		WHERE
              			NVL (APPROVE_KASI, 0) = 0
              		AND NVL (APPROVE_KABIRO, 0) = 0
              		AND I.DELETE_MARK = 0
              		AND TGL_INVOICE BETWEEN TO_DATE ('".$approveDate1."', 'YYYY-MM-DD')
              		AND TO_DATE ('".$approveDate2."', 'YYYY-MM-DD')
                  AND ORG = '".$company."'".$filter."
              		UNION
              			SELECT
              				0 AS BLMAPPROVE,
              				COUNT (NO_INVOICE) AS KASIAPPROVE,
              				0 AS KABIROAPPROVE,
              				0 AS KASIKABIROAPPROVE
              			FROM
              				EX_INVOICE I
              			WHERE
              				NVL (APPROVE_KASI, 0) = 1
              			AND NVL (APPROVE_KABIRO, 0) = 0
              			AND I.DELETE_MARK = 0
              			AND TGL_INVOICE BETWEEN TO_DATE ('".$approveDate1."', 'YYYY-MM-DD')
              			AND TO_DATE ('".$approveDate2."', 'YYYY-MM-DD')
                    AND ORG = '".$company."'".$filter."
              			UNION
              				SELECT
              					0 AS BLMAPPROVE,
              					0 AS KASIAPPROVE,
              					COUNT (NO_INVOICE) AS KABIROAPPROVE,
              					0 AS KASIKABIROAPPROVE
              				FROM
              					EX_INVOICE I
              				WHERE
              					NVL (APPROVE_KASI, 0) = 0
              				AND NVL (APPROVE_KABIRO, 0) = 1
              				AND I.DELETE_MARK = 0
              				AND TGL_INVOICE BETWEEN TO_DATE ('".$approveDate1."', 'YYYY-MM-DD')
              				AND TO_DATE ('".$approveDate2."', 'YYYY-MM-DD')
                      AND ORG = '".$company."'".$filter."
              				UNION
              					SELECT
              						0 AS BLMAPPROVE,
              						0 AS KASIAPPROVE,
              						0 AS KABIROAPPROVE,
              						COUNT (NO_INVOICE) AS KASIKABIROAPPROVE
              					FROM
              						EX_INVOICE I
              					WHERE
              						NVL (APPROVE_KASI, 0) = 1
              					AND NVL (APPROVE_KABIRO, 0) = 1
              					AND I.DELETE_MARK = 0
              					AND TGL_INVOICE BETWEEN TO_DATE ('".$approveDate1."', 'YYYY-MM-DD')
              					AND TO_DATE ('".$approveDate2."', 'YYYY-MM-DD')
                                                AND ORG = '".$company."'".$filter."
              	) A";
        $parse13 = oci_parse($conn, $q1);
        oci_execute($parse13);
        $out = array();
        while ($arr11 = oci_fetch_assoc($parse13)) {
          $out = $arr11;
        }
        echo json_encode(array("tglPeriode"=>$approveDate1."|".$approveDate2, "data"=>$out, "tes"=>$q1));
      break;

      case 'blmApprove':
        $periode= (!empty($_GET["periode"]) ? $_GET["periode"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        list($from, $to) = split('[|]', $periode);
        $q1 = "SELECT
              	NO_INVOICE, TGL_INVOICE, NAMA_VENDOR
              FROM
              	EX_INVOICE I
              WHERE
              	NVL (APPROVE_KASI, 0) = 0
              AND NVL (APPROVE_KABIRO, 0) = 0
              AND I.DELETE_MARK = 0
              AND TGL_INVOICE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              AND TO_DATE ('".$to."', 'YYYY-MM-DD')
              AND ORG = '".$company."'".$filter."";
         $parse14 = oci_parse($conn, $q1);
         oci_execute($parse14);
         $out = array();
         while($arr12 = oci_fetch_assoc($parse14)){
           $out[] = $arr12;
         }
         echo json_encode($out);
      break;

      case 'approveKasi':
        $periode= (!empty($_GET["periode"]) ? $_GET["periode"] : "");
        list($from, $to) = split('[|]', $periode);
        $q1 = "SELECT
              	NO_INVOICE, TGL_INVOICE, NAMA_VENDOR
              FROM
              	EX_INVOICE I
              WHERE
              	NVL (APPROVE_KASI, 0) = 1
              AND NVL (APPROVE_KABIRO, 0) = 0
              AND I.DELETE_MARK = 0
              AND TGL_INVOICE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              AND TO_DATE ('".$to."', 'YYYY-MM-DD')
              AND ORG = '".$company."'".$filter."";
        $parse15 = oci_parse($conn, $q1);
        oci_execute($parse15);
        $out = array();
        while($arr13 = oci_fetch_assoc($parse15)){
          $out[] = $arr13;
        }
        echo json_encode($out);
      break;

      case 'approveKabiro':
        $periode= (!empty($_GET["periode"]) ? $_GET["periode"] : "");
        list($from, $to) = split('[|]', $periode);
        $q1 = "SELECT
              	NO_INVOICE, TGL_INVOICE, NAMA_VENDOR
              FROM
              	EX_INVOICE I
              WHERE
              	NVL (APPROVE_KASI, 0) = 0
              AND NVL (APPROVE_KABIRO, 0) = 1
              AND I.DELETE_MARK = 0
              AND TGL_INVOICE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              AND TO_DATE ('".$to."', 'YYYY-MM-DD')
              AND ORG = '".$company."'".$filter."";
        $parse16 = oci_parse($conn, $q1);
        oci_execute($parse16);
        $out = array();
        while($arr14 = oci_fetch_assoc($parse16)){
          $out[] = $arr14;
        }
        echo json_encode($out);
      break;

      case 'approved':
        $periode= (!empty($_GET["periode"]) ? $_GET["periode"] : "");
        list($from, $to) = split('[|]', $periode);
        $q1 = "SELECT
              	*
              FROM
              	EX_INVOICE I
              WHERE
              	NVL (APPROVE_KASI, 0) = 1
              AND NVL (APPROVE_KABIRO, 0) = 1
              AND I.DELETE_MARK = 0
              AND TGL_INVOICE BETWEEN TO_DATE ('".$from."', 'YYYY-MM-DD')
              AND TO_DATE ('".$to."', 'YYYY-MM-DD')
              AND ORG = '".$company."'".$filter."";
        $parse17 = oci_parse($conn, $q1);
        oci_execute($parse17);
        $out = array();
        while($arr15 = oci_fetch_assoc($parse17)){
          $out[] = $arr15;
        }
        echo json_encode($out);
      break;
      
      default:
        echo json_encode(array("success" => false));
    }
  }

 ?>
