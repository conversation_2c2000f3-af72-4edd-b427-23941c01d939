<? 
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

//function getOraHdr($org, $no_shp_sap){

$tgl_kini= date('Ymd');
mysql_connect("10.15.5.71","sgg","sggroup"); //koneksi database
mysql_select_db("eis");
$tgl_kini= date('Ymd');
$perintah="SELECT TGL, WERKS, NAME1, MATNR, MAKTX, END_STOCK FROM sap_total_stock_gd_penyangga WHERE  tgl='$tgl_kini' group by WERKS, MATNR";
	$hasil=mysql_query($perintah);
	if (mysql_num_rows($hasil) < 1) {
		echo (" Tabel Masih Kosong "); }
		else {
	$hasil=mysql_query($perintah);
	while ($data=mysql_fetch_array($hasil)) {
        
		$id=$data['id_stock'];
		$tgl[]=$data['tgl'];
	    $VKORG[]=$data['VKORG'];
		$WERKS[]=$data['WERKS'];
	    $NAME1[]=$data['NAME1'];
		$MATNR[]=$data['MATNR'];
		$MAKTX[]=$data['MAKTX'];
		$END_STOCK[]=$data['END_STOCK'];
		
			
}}
  $total=count($VKORG);
	//var_dump($data);

/*
      	$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_dataprod.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_MM_VALSTOCK");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		
         $fce->I_BUKRS ='2000';
		//$fce->I_WERKS ='2403';
		 //$fce->I_MATNR =$kod_mtr;
			$fce->I_DATE ='20110923';
			$fce->I_DATE_TO ='20110923';
			$fce->I_FLAG ='2';
			$fce->I_SUM ='X';
			//$fce->I_INV ='X';
			$fce->Call();
			
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->T_RETURNDATA->Reset();
			$s=0;
			while ( $fce->T_RETURNDATA->Next() ){
				$WERKS[$s] = $fce->T_RETURNDATA->row["WERKS"];
				$NAME1[$s] = $fce->T_RETURNDATA->row["NAME1"];
				$MATNR[$s] = $fce->T_RETURNDATA->row["MATNR"];
				$MAKTX[$s] = $fce->T_RETURNDATA->row["MAKTX"];
				$REC_TOTAL[$s] = $fce->T_RETURNDATA->row["REC_TOTAL"];
				$REC_TOTALX[$s] = $fce->T_RETURNDATA->row["REC_TOTALX"];
				$ISSUE_TOTAL[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTAL"];
				$ISSUE_TOTAL[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTAL"];
				$ISSUE_TOTALX[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTALX"];
				$END_STOCK[$s] = $fce->T_RETURNDATA->row["END_STOCK"];
				$END_STOCKX[$s] = $fce->T_RETURNDATA->row["END_STOCKX"];
				$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($WERKS);
			
		//var_dump($END_STOCK);
		

*/	  
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" align='center'>DAFTAR LOGISTIC & TOKO BANGUNAN ONLINE (LOKO GP)
 </th>
</tr></table></div>
<?
	
	if($total>0) {
?>

	<div align="center">
	<table width="500" align="center" class="adminlist" border='0'>
	  <tr class="quote">
	    <td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >Nama Plant</strong></td>
		<td align="center"><strong >Nama Material</strong></td>
		<td align="center"><strong >Jumlah Stock</strong></td>
		
	</tr>
	 <tr class="quote">
		
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$TOTAL_QTY[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	


	?>
		<td align="center"><? echo $b."."?></td>
		<td align="left"><? echo  $NAME1[$i]; ?></td>
		<td align="left"><? echo $MAKTX[$i]; ?></td>
		<td align="right"><? echo number_format($END_STOCK[$i],0,".",","); ?></td>
		
		
		</tr>
	  <? } ?><!--
                <tr>
                    <td></td>
                    <td colspan="6"><strong >TOTAL</strong ></td>
                    <td align="right"><strong ><? echo number_format($totaldo,3,".",","); ?></strong ></td>
                                      
                </tr>-->
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
