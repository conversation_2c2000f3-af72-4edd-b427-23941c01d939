<?
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
// hell0
$halaman_id=2043;
$user_id=trim($_SESSION['user_id']);
$user_org=$_SESSION['user_org'];
$user_name=$_SESSION['user_name'];
$page="penerimaan_inv_vendor.php";

$user_data=$fungsi->ex_find_user($conn,$user_id);

$vendor=$user_data["vendor"];
$nama_vendor=$user_data["nama_vendor"];
//echo $vendor;

//vendor
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
unset($orgIn);$orgcounter=0;
foreach ($mp_coics as $keyOrg => $valorgm){
      $inorg .="'".$keyOrg."',";
      $orgcounter++;
}
    $orgIn= rtrim($inorg, ',');
}else{
    $orgIn= $user_org;
}
$dirr = $_SERVER['PHP_SELF'];
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);

//$page="penerimaan_inv_vendor.php";
//$vendor=$fungsi->ex_find_vendor($conn,$user_id);
//$hanya_baca = $fungsi->ex_hanya_baca($vendor);
//
if($user_id==''){
    ?>
    <SCRIPT LANGUAGE="JavaScript">
    <!--
    alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
    //-->
    </SCRIPT>
    <a href="../index.php">Login....</a>
    <?
    exit;
}
$statusDok=0;
$count = count($t_return);
if ($total <= 0) $komen = "Tidak Ada Data Yang Ditemukan";
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>

    <link rel="stylesheet" href="../Templates/plugins/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../Templates/plugins/font/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../Templates/css/animate.css">
    <link rel="stylesheet" href="../Templates/plugins/toastr/toastr.min.css">
    <link rel="stylesheet" href="../Templates/css/dashboard.css">
    <style media="screen">
      canvas{
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
      }
      .loader {
        border: 16px solid #f3f3f3;
        border-radius: 50%;
        border-top: 16px solid #009688;
        width: 80px;
        height: 80px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
        margin: auto auto;
        margin-top: 4px;
        position: relative;
        margin-bottom: 100px;
        /* position: absolute; */
        padding: 10px;
      }

      @-webkit-keyframes spin {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .back-loading{
        background: white;
        position: absolute;
        z-index: 9999;
        width: 99%;
        height: 100%;
        margin: 0 auto;
        left: 1;
        display: none;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }

      a:focus, a:hover {
        color: #fff;
        text-decoration: underline;
      }


      .small-box {
          border-radius: 2px;
          position: relative;
          display: block;
          margin-bottom: 20px;
          box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px;
      }

      .small-box>.inner {
        padding: 10px;
        color: #fff;
       }

       .small-box h3, .small-box p {
          z-index: 5;
        }

        .small-box h3 {
            font-size: 38px;
            font-weight: bold;
            margin: 0 0 10px 0;
            white-space: nowrap;
            padding: 0;
        }

        .small-box h3, .small-box p {
            z-index: 5;
          }
        .small-box p {
            font-size: 15px;
        }

        .small-box .icon {
            -webkit-transition: all .3s linear;
            -o-transition: all .3s linear;
            transition: all .3s linear;
            position: absolute;
            top: -10px;
            right: 10px;
            z-index: 0;
            font-size: 50px;
            padding-right: 10px;
            color: rgba(0,0,0,0.15);
            padding: 20px 0px;
        }

        .small-box>.small-box-footer {
            position: relative;
            text-align: center;
            padding: 3px 0;
            color: #fff;
            color: rgba(255,255,255,0.8);
            display: block;
            z-index: 10;
            background: rgba(0,0,0,0.1);
            text-decoration: none;
        }

        .small-box>.small-box-footer:hover{
            color:#fff;
            background:rgba(0,0,0,0.15)
        }
        .small-box:hover{
            text-decoration:none;
            color:#f9f9f9
        }
        .small-box:hover .icon{
            font-size:80px
        }

        /* -------------- loader6 -------------- */

        .loader6{
        	position: relative;
        	width: 12px;
        	height: 12px;

        	top: 46%;
        	top: -webkit-calc(50% - 6px);
        	top: calc(50% - 6px);
        	left: 46%;
        	left: -webkit-calc(50% - 6px);
        	left: calc(50% - 6px);

        	border-radius: 12px;
        	background-color: #fff;
        	-webkit-transform-origin:  50% 50%;
        			transform-origin:  50% 50% ;
        	-webkit-animation: loader6 1s ease-in-out infinite;
        			animation: loader6 1s ease-in-out infinite;
        }

        .loader6:before{
        	content: "";
        	position: absolute;
        	background-color: rgba(255, 255, 255, .5);
        	top: 0px;
        	left: -25px;
        	height: 12px;
        	width: 12px;
        	border-radius: 12px;
        }

        .loader6:after{
        	content: "";
        	position: absolute;
        	background-color: rgba(255, 255 ,255 ,.5);
        	top: 0px;
        	left: 25px;
        	height: 12px;
        	width: 12px;
        	border-radius: 12px;
        }


        @-webkit-keyframes loader6{
            0%{-webkit-transform:rotate(0deg);}
            50%{-webkit-transform:rotate(180deg);}
            100%{-webkit-transform:rotate(180deg);}
        }

        @keyframes loader6{
            0%{transform:rotate(0deg);}
            50%{transform:rotate(180deg);}
            100%{transform:rotate(180deg);}
        }

        .tile_count {
            margin-top: 20px
        }
        .tile_count .tile_stats_count {
            border-bottom: 1px solid #D9DEE4;
            padding: 0 10px 0 20px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            position: relative;
            color: #73879C;
            margin-bottom: 40px;
        }
        @media (min-width: 992px) {
            footer {
                margin-left: 230px
            }
        }
        @media (min-width: 992px) {
            .tile_count .tile_stats_count {
                margin-bottom: 10px;
                border-bottom: 0;
                padding-bottom: 10px
            }
        }
        .tile_count .tile_stats_count:before {
            content: "";
            position: absolute;
            left: 0;
            height: 65px;
            border-left: 2px solid #ADB2B5;
            margin-top: 10px
        }
        @media (min-width: 992px) {
            .tile_count .tile_stats_count:first-child:before {
                border-left: 0
            }
        }
        .tile_count .tile_stats_count .count {
            font-size: 30px;
            line-height: 47px;
            font-weight: 600;
            color: #424141;
        }
        @media (min-width: 768px) {
            .tile_count .tile_stats_count .count {
                font-size: 40px
            }
        }
        @media (min-width: 992px) and (max-width: 1100px) {
            .tile_count .tile_stats_count .count {
                font-size: 30px
            }
        }
        .tile_count .tile_stats_count span {
            font-size: 12px
        }
        @media (min-width: 768px) {
            .tile_count .tile_stats_count span {
                font-size: 13px
            }
        }
        .tile_count .tile_stats_count .count_bottom i {
            width: 12px
        }

        .count_bottom i.red {
          color: #F44336;
        }
        .count_bottom i.yellow {
          color: #FFD600;
        }
        .count_bottom i.green {
          color: #64DD17;
        }

        /* LOADING DEPAN */
        #loader {
          /* Uncomment this to make it run! */
          /*
             animation: loader 5s linear infinite;
          */

          position: absolute;
          top: calc(50% - 20px);
          left: calc(50% - 20px);
          z-index: 999;
        }
        @keyframes loader {
          0% { left: -100px }
          100% { left: 110%; }
        }
        #box {
          width: 50px;
          height: 50px;
          background: #009688;
          animation: animate .5s linear infinite;
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 3px;
        }
        @keyframes animate {
          17% { border-bottom-right-radius: 3px; }
          25% { transform: translateY(9px) rotate(22.5deg); }
          50% {
            transform: translateY(18px) scale(1,.9) rotate(45deg) ;
            border-bottom-right-radius: 40px;
          }
          75% { transform: translateY(9px) rotate(67.5deg); }
          100% { transform: translateY(0) rotate(90deg); }
        }
        #shadow {
          width: 50px;
          height: 5px;
          background: #000;
          opacity: 0.1;
          position: absolute;
          top: 59px;
          left: 0;
          border-radius: 50%;
          animation: shadow .5s linear infinite;
        }
        @keyframes shadow {
          50% {
            transform: scale(1.2,1);
          }
        }


        .container-indie {
          background: #fff;
          overflow: hidden;
          z-index: 99999;
          width: 100%;
          height: 100%;
          position: fixed;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
        }
        .container-indie h4 {
          position: absolute;
          bottom: 20px;
          left: 20px;
          margin: 0;
          font-weight: 100;
          opacity: .5;
          font-size: 100px;
          text-align: center;
        	font-family: sans-serif;
          color: #111;
        }

        .detail-stat-inv{
          color: #73879C;
          text-decoration: none;
          -webkit-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -moz-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -o-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -ms-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          transition: background-color 100ms ease-out, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
        }

        .detail-stat-inv:hover, .detail-stat-inv:focus{
          color: #424141;
          text-decoration: none ;
        }


    </style>
  </head>
  <body>

    <body>

      <div class="container-indie">
        <div id="loader">
          <div id="shadow"></div>
          <div id="box"></div>
        </div>
      </div>
      <div class="content-dashboard">
        <h1>DASHBOARD <span>Umum</span></h1>
        <hr>
        <div class="row">
          <div class="col-md-2 pull-right">
            <div class="form-group">
              <label for="">Company</label>
              <select class="form-control" id="company" name="company">
                <option value="7000">7000</option>
                <option value="5000">5000</option>
              </select>
            </div>
          </div>
        </div>

        <!-- STATUS APPROVAL -->
        <h3 style="">Status Approval</h3>
        <div class="row">
          <form id="reloadApprove" action="index.html" method="post">
            <div class="col-md-2">
              <div class="form-group">
                <label>From</label>
                <input type="date" class="form-control" name="approveDate1" id="approveDate1" value="<?= date("Y-m")."-01" ?>">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label>To</label>
                <input type="date" class="form-control" name="approveDate2" id="approveDate2" value="<?= date("Y-m-d") ?>">
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                  Filter
                </button>
              </div>
            </div>
          </form>
          <input type="hidden" id="_callbackPeriode" value="">
        </div>
        <div class="row">
          <div class="col-md-3 col-xs-6">
            <div class="approve small-box" style="background-color: #F44336; display:none;">
              <div id="blmapprove" class="inner">
                <p>Belum Approve</p>
                <h3>0</h3>
              </div>
              <div class="icon">
                <i class="fa fa-file-text-o"></i>
              </div>
              <a href="#" data-toggle="modal" data-target="#modalDetailBanner" data-icon="fa fa-file-text-o" data-title="blmApprove" class="toDetail detbanner small-box-footer">More info <i class="fa fa-arrow-circle-right"></i></a>
            </div>
            <div class="banner-approve" style="position: relative; margin-bottom:10px; background: #009688; width: 95%; height: 100px; color: #888; z-index: 22; display: none; left:10; bottom:0; top:0;">
                <div class="loader6"></div>
            </div>
          </div>
          <div class="col-md-3 col-xs-6">
            <div class="approve small-box" style="background-color: #FF9800; display:none;">
              <div id="kasiapprove" class="inner">
                <p>Approve Kasi</p>
                <h3>0</h3>
              </div>
              <div class="icon">
                <i class="fa fa-user"></i>
              </div>
              <a href="#" data-toggle="modal" data-target="#modalDetailBanner" data-icon="fa fa-user" data-title="approveKasi" class="toDetail detbanner small-box-footer">More info <i class="fa fa-arrow-circle-right"></i></a>
            </div>
            <div class="banner-approve" style="position: relative; margin-bottom:10px; background: #009688; width: 95%; height: 100px; color: #888; z-index: 22; display: none; left:10; bottom:0; top:0;">
                <div class="loader6"></div>
            </div>
          </div>
          <div class="col-md-3 col-xs-6">
            <div class="approve small-box" style="background-color: #FFC107; display:none;">
              <div id="kabiroapprove" class="inner">
                <p>Approve Kabiro</p>
                <h3>0</h3>
              </div>
              <div class="icon">
                <i class="fa fa-user-secret"></i>
              </div>
              <a href="#" data-toggle="modal" data-target="#modalDetailBanner" data-icon="fa fa-user-secret" data-title="approveKabiro" class="toDetail detbanner small-box-footer">More info <i class="fa fa-arrow-circle-right"></i></a>
            </div>
            <div class="banner-approve" style="position: relative; margin-bottom:10px; background: #009688; width: 95%; height: 100px; color: #888; z-index: 22; display: none; left:10; bottom:0; top:0;">
                <div class="loader6"></div>
            </div>
          </div>
          <div class="col-md-3 col-xs-6">
            <div class="approve small-box" style="background-color: #4CAF50; display:none;">
              <div id="approved" class="inner">
                <p>Approved</p>
                <h3>0</h3>
              </div>
              <div class="icon">
                <i class="fa fa-flag-checkered"></i>
              </div>
              <a href="#" data-toggle="modal" data-target="#modalDetailBanner" data-icon="fa fa-flag-checkered" data-title="approved" class="toDetail detbanner small-box-footer">More info <i class="fa fa-arrow-circle-right"></i></a>
            </div>
            <div class="banner-approve" style="position: relative; margin-bottom:10px; background: #009688; width: 95%; height: 100px; color: #888; z-index: 22; display: none; left:10; bottom:0; top:0;">
                <div class="loader6"></div>
            </div>
          </div>
            
        </div>
        <!-- END STATUS APPROVAL -->

        <div class="row">

          <!-- SURAT PERINTAH JALAN -->
          <div class="col-md-7">
            <div class="row">
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Surat Perintah Jalan <span>(SPJ)</span>
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div class="row">
                      <form id="reloadChart" action="index.html" method="post">
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="spjdate1" id="spjdate1" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="spjdate2" id="spjdate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">Filter</button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div style="display:none;" id="spj">
                      <div style="width:100%;">
                          <canvas id="lineChartSpj"></canvas>
                      </div>
                      <table id="presentase" class="table" style="margin-top:10px;">
                        <tbody>
                        </tbody>
                      </table>
                    </div>
                    <div id="loadingSpj" class="back-loading">
                      <div class="loader"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- STATUS COST ETELMENT -->
            <div class="row">
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Status Cost Etelment / Ongkos Angkut
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div id="loadingStatEtel" class="back-loading">
                      <div class="loader"></div>
                    </div>
                    <div class="row">
                      <form id="reloadStatEtel" action="index.html" method="post">
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="from_etelment" id="from_etelment" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="to_etelment" id="to_etelment" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              <i class="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div id="statEtel" style="display:none;" class="row">
                      <div class="col-lg-12">
                        <div style="width: 100%;">
                            <canvas height="250px;" style="margin-right:20px;" id="chartStatEtel"></canvas>
                        </div>
                      </div>
                      <div class="col-lg-12">
                        <div style="width:200px;">
                          <h1 style="font-size: 15px;font-weight: bold;border-bottom: 2px solid #dddddd;padding-bottom: 6px;color: #111;">Top 10 Failed Recalculate</h1>
                        </div>
                        <table id="tabelStatEtel" class="table table-hover">
                          <thead>
                            <tr>
                              <th style="font-size:13px;">No. Inv</th>
                              <th style="font-size:13px;">Vendor</th>
                              <th style="font-size:13px;">Tanggal</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- END STATUS COST ETELMENT -->
          </div>
          <!-- END SURAT PERINTAH JALAN -->


          <div class="col-md-5">
            <div class="row">

              <!-- STATUS CETAK INVOICE -->
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Status Cetak Invoice
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div id="loadingStatInv" class="back-loading">
                      <div class="loader"></div>
                    </div>
                    <div class="row">
                      <form id="reloadStatInv" action="index.html" method="post">
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="statInvDate1" id="statInvDate1" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="statInvDate2" id="statInvDate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>Chose Expediture</label>
                            <select class="form-control" id="vendor" name="vendor">
                              <option value="">All</option>
                            </select>
                          </div>
                        </div>
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              <i class="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div id="cetInv" style="display:none;" class="row">
                      <div class="col-lg-6">
                        <div style="width: 100%;">
                            <canvas height="250px;" style="margin-right:20px;" id="doghnutStatInv"></canvas>
                        </div>
                      </div>
                      <div class="col-lg-6">
                        <div style="width:150px;">
                          <h1 style="font-size: 15px;font-weight: bold;border-bottom: 2px solid #dddddd;padding-bottom: 6px;color: #111;">Top 10 Belum Cetak</h1>
                        </div>
                        <table id="detailStatInv" class="table" style="margin-top:10px;">
                          <thead>
                            <tr>
                              <th style="font-size:13px;">No. Inv</th>
                              <th style="font-size:13px;">Vendor</th>
                              <th style="font-size:13px;">Tanggal</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- END STATUS CETAK INVOICE -->

              <!-- STATUS PENERIMAAN TAGIHAN -->
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Status Penerimaan Penagihan
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div id="loadingStatTag" class="back-loading">
                      <div class="loader"></div>
                    </div>
                    <div class="row">
                      <form id="reloadStatTag" action="pixxxo.html" method="post">
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="statTagDate1" id="statTagDate1" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="statTagDate2" id="statTagDate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              <i class="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div id="terTag" style="display:none;" class="row">
                      <div class="col-lg-6">
                        <div style="width: 100%;">
                            <canvas height="250px;" style="margin-right:20px;" id="doghnutStatTag"></canvas>
                        </div>
                      </div>
                      <div class="col-lg-6">
                        <div style="width:150px;">
                          <h1 style="font-size: 15px;font-weight: bold;border-bottom: 2px solid #dddddd;padding-bottom: 6px;color: #111;">Top 10 Belum Terima</h1>
                        </div>
                        <table id="detailStatTag" class="table" style="margin-top:10px;">
                          <thead>
                            <tr>
                              <th style="font-size:13px;">No. Inv</th>
                              <th style="font-size:13px;">Vendor</th>
                              <th style="font-size:13px;">Tanggal</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- END STATUS PENERIMAAN TAGIHAN -->

            </div>
          </div>

        </div>

        <!-- INVOICE EXPEDITURE -->
        <div class="row">
          <div class="col-md-12">
            <div class="panel-group" id="statInvEx">
              <div class="panel panel-default">
                <div class="panel-heading">
                   <a class="panel-title collapsed"  style="" >Status Invoice Expediture</a>
                   <a type="button" href="#" class="close btnClose panel-title collapsed pull-right" style="font-size:12px;"><i style="font-size:16px;" class="pull-right fa fa-times"></i></a>
                   <a type="button" oninvalid=""class="close panel-title collapsed pull-right" data-toggle="collapse" style="font-size: 12px;" data-parent="#statInvEx" href="#anakeEmboh"><i style="font-size:16px;" class="pull-right fa fa-minus"></i></a>
                </div>
                <div id="anakeEmboh" class="panel-collapse collapse in">
                  <div class="panel-body">
                    <div class="row">
                      <form id="formStatInvExp" action="index.html" method="post">
                        <div class="col-md-3">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" name="ex_date1" id="ex_date1" class="form-control" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" name="ex_date2" id="ex_date2" class="form-control" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <input type="hidden" name="_callbackPeriodeStatInvExp" id="_callbackPeriodeStatInvExp" value="">
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              Filter
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="row tile_count">
                        <div id="statEx" class="col-md-3 tile_stats_count" style="display:none;">
                          <span class="count_top">
                             <a href="#" data-toggle="modal" data-message="EKSVER" data-target="#modalDetailStatInvExp" class="detail-stat-inv toDetailInvExp"> <i class="fa fa-user"></i> Outstanding Ekspedisi ke Verifikasi </a>
                           </span>
                          <div class="count">0</div>
                          <span class="count_bottom"> / <span>0</span> (<i class="">0% </i>)</span>
                        </div>
                        <div id="PenVer" class="col-md-3 tile_stats_count">
                          <span class="count_top">
                            <a href="#" data-toggle="modal" data-message="TERVER" data-target="#modalDetailStatInvExp" class="detail-stat-inv toDetailInvExp"> <i class="fa fa-user"></i> Outstanding Penerimaan Verifikasi </a>
                          </span>
                          <div class="count">0</div>
                          <p class="count_bottom"> / <span>0</span> (<i class="">0% </i>)</p>
                        </div>
                        <div id="exBend" class="col-md-3 tile_stats_count">
                          <span class="count_top">
                            <a href="#" data-toggle="modal" data-message="EKSBEND" data-target="#modalDetailStatInvExp" class="detail-stat-inv toDetailInvExp"> <i class="fa fa-user"></i> Outstanding Ekspedisi ke Bendahara </a>
                          </span>
                          <div class="count">0</div>
                          <span class="count_bottom"> / <span>0</span> (<i class="">0% </i>)</span>
                        </div>
                        <div id="terBer" class="col-md-3 tile_stats_count">
                          <span class="count_top">
                            <a href="#" data-toggle="modal" data-message="TERBER" data-target="#modalDetailStatInvExp" class="detail-stat-inv toDetailInvExp"> <i class="fa fa-user"></i> Outstanding Penerimaan Bendahara </a>
                          </span>
                          <div class="count">0</div>
                          <span class="count_bottom"> / <span>0</span> (<i class="">0% </i>)</span>
                        </div>
                      </div>

                      <div style="padding:10px;" class="row">
                        <div style="border: 1px solid #dddddd;padding:10px;margin: 0 auto;" class="col-md-6 stat-ver">
                          <div class="row">
                            <div class="col-md-12">
                              <div style="margin: 0 auto; text-align: center; border: 1px solid #dddddd; margin-bottom: 20px; padding: 10px;">
                                <h4>Rata - rata proses Verifikasi</h4>
                                <span id="avgVer" style="font-size: 30px; font-weight: bold;">1 Hari</span>
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-8">
                              <div class="labelVer" style="width: 100%; display:none;">
                                  <canvas height="250px;" style="" id="statVer"></canvas>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <table class="table table-hover labelVer" style="box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px; display:none;">
                                <thead style="background: #009688;color: #fff;">
                                  <tr>
                                    <th style="text-align: center;">Labels</th>
                                    <th style="text-align: center;">Jumlah</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #009688;border-radius:  50%;margin:  0 auto;"></div></td>
                                    <td style="text-align:center;" id="statVerTerima"></td>
                                  </tr>
                                  <tr>
                                    <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #f44336;border-radius:  50%;margin:  0 auto;"></div></td>
                                    <td style="text-align: center;" id="statVerTolak"></td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>

                        <div style="border: 1px solid #dddddd;padding:10px;margin: 0 auto; border-left:0px;" class="col-md-6 stat-pemb">
                          <div class="row">
                            <div class="col-md-12">
                              <div style="margin: 0 auto; text-align: center; border: 1px solid #dddddd; margin-bottom: 20px; padding: 10px;">
                                <h4>Rata - rata proses Bendahara</h4>
                                <span id="avgBend" style="font-size: 30px; font-weight: bold;">15 Hari</span>
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-8">
                              <div class="labelBend" style="width: 100%; display:none;">
                                  <canvas height="250px;" style="" id="statPemb"></canvas>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <table class="table table-hover labelBend" style="box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px; display:none;">
                                <thead style="background: #009688;color: #fff;">
                                  <tr>
                                    <th style="text-align: center;">Labels</th>
                                    <th style="text-align: center;">Jumlah</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #FFC107;border-radius:  50%;margin:  0 auto;"></div></td>
                                    <td style="text-align:center;" id="statPembSiap"></td>
                                  </tr>
                                  <tr>
                                    <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #009688;border-radius:  50%;margin:  0 auto;"></div></td>
                                    <td style="text-align: center;" id="statPembBayar"></td>
                                  </tr>
                                  <tr>
                                    <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #F44336;border-radius:  50%;margin:  0 auto;"></div></td>
                                    <td style="text-align: center;" id="statPembTagih"></td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="exLoading" style="position: absolute; top:0; bottom:0; left:0; right:0; background: #009688; width: 100%; height: 100%; color: #888; z-index: 22; display: none;">
                <div class="loader6"></div>
            </div>

          </div>
        </div>
        <!-- END INVOICE EXPEDITURE -->

        <!-- CHART ASSIGNMENT -->
        <div class="row">
          <div class="col-md-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">
                  Data Monitoring Assignment
                </h3>
              </div>
              <div class="panel-body">
                <div id="loadingChartAss" class="back-loading">
                  <div class="loader"></div>
                </div>
                <div class="row">
                  <form id="reloadChartAssigment" action="pixxxo.html" method="post">
                    <div class="col-md-5">
                      <div class="form-group">
                        <label>From</label>
                        <input type="date" class="form-control" name="from_date_assigment" id="from_date_assigment" value="<?= date("Y-m")."-01" ?>">
                      </div>
                    </div>
                    <div class="col-md-5">
                      <div class="form-group">
                        <label>To</label>
                        <input type="date" class="form-control" name="to_date_assigment" id="to_date_assigment" value="<?= date("Y-m-d") ?>">
                      </div>
                    </div>
                    <div class="col-md-2">
                      <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                          <i class="fa fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="row">
                  <div class="col-md-8">
                    <div style="width: 100%">
                        <canvas id="assigmentChart"></canvas>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <!-- <h4>List Expediture</h4> -->
                    <table id="listExpediture" class="table table-hover">
                      <thead>
                        <tr>
                          <th>No</th>
                          <th>Operator Distran</th>
                          <th>#</th>
                        </tr>
                      </thead>
                      <tbody>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- END CHART ASSIGNMENT -->

      </div>

      <!-- modal -->
      <div id="modalDetailBanner" class="modal animated slideInUp" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="margin-top:0px;">
          <div class="modal-content">
            <div class="modal-header">
              <h1 style="font-size: 20px;" id="myModalLabel" class="modal-title">
                Banner Detail
              </h1>
            </div>
            <div class="modal-body">
              <div id="loadingModal" class="back-loading" style="display: none;">
                <div class="loader"></div>
              </div>
              <table style="font-size: 13px;" id="detailTable" class="table table-hover table-bordered">
                <thead style="background:#009688; color:#fff;">
                  <tr>
                    <th class="text-center">NO</th>
                    <th class="text-center">NO INVOICE</th>
                    <th class="text-center">TANGGAL INVOICE</th>
                    <th class="text-center">VENDOR</th>
                  </tr>
                </thead>
                <tbody>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button class="btn btn-danger btn-aeng form-control" style="width:100px;" data-dismiss="modal">
                Tutup
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade slideInUp" id="modalDetailStatInvExp" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h4 class="modal-title" id=""></h4>
            </div>
            <div class="modal-body">
              <div id="loadingModalStatInvExp" class="back-loading" style="display: none;">
                <div class="loader"></div>
              </div>
              <table style="font-size: 13px;" id="detailStatInvExp" class="table table-hover table-bordered">
                <thead style="background:#009688; color:#fff;">
                  <tr>
                    <th class="text-center">NO</th>
                    <th class="text-center">NO INVOICE</th>
                    <th class="text-center">VENDOR</th>
                    <th class="text-center">TGL INVOICE</th>
                  </tr>
                </thead>
                <tbody>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button class="btn btn-danger btn-aeng form-control" style="width:100px;" data-dismiss="modal">
                Tutup
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal fade slideInUp" id="modalDetailAssigment" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
              <h4 class="modal-title" id=""></h4>
            </div>
            <div class="modal-body">
              <div id="loadingModalAssigment" class="back-loading" style="display: none;">
                <div class="loader"></div>
              </div>
              <table id="tblDetailAssigment" class="table table-hover">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>No Invoice</th>
                    <th>Nama Vendor</th>
                    <th>No Group Vendor</th>
                    <th>Tgl Terima</th>
                    <th>User Acc</th>
                    <th>Lama Proses</th>
                  </tr>
                </thead>
                <tbody>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button class="btn btn-danger btn-aeng form-control" style="width:100px;" data-dismiss="modal">
                Tutup
              </button>
            </div>
          </div>
        </div>
      </div>

      <script src="../Templates/plugins/jquery/jQuery-2.1.4.min.js"></script>
      <script src="../Templates/plugins/bootstrap/js/bootstrap.min.js"></script>
      <script src="../Templates/plugins/chartjs/Chart.bundle.min.js" charset="utf-8"></script>
      <script src="../Templates/plugins/chartjs/utils.js" charset="utf-8"></script>
      <script src="../Templates/plugins/toastr/toastr.min.js" charset="utf-8"></script>
      <script src="jsControll/spj.js" charset="utf-8"></script>
      <script src="jsControll/status_invoice.js" charset="utf-8"></script>
      <script src="jsControll/status_penerimaan.js" charset="utf-8"></script>
      <script src="jsControll/status_inv_exp.js" charset="utf-8"></script>
      <script src="jsControll/banner.js" charset="utf-8"></script>
      <script src="jsControll/assigment.js" charset="utf-8"></script>
      <script src="jsControll/status_cost_etelment.js" charset="utf-8"></script>
      <script type="text/javascript">
        $("#company").on("change", function(){
          $("#reloadApprove").trigger('submit');
          $("#reloadChart").trigger('submit');
          $("#reloadStatInv").trigger('submit');
          $("#reloadStatTag").trigger('submit');
          $("#reloadStatEtel").trigger('submit');
          $("#formStatInvExp").trigger('submit');
          $("#reloadChartAssigment").trigger('submit');
        })
      </script>
  </body>
</html>
