<html>
<head>
<title>Mime Example 1</title>
<style type="text/css">
input.submit {
 	background-color: #ffffe0; 
	font-weight: bold; 
}
</style>
</head>
<body>
<?php
 /* --------------------------------------------------
    This example shows how to use the class to get an
    attachment from a user and send it via email.
    e.g., a job site where a prospect is sending 
    resume.
 -------------------------------------------------- */
 include "MIME.class";
 define('TO', '<EMAIL>');  # CHANGE THIS TO A REAL ADDRESS (yours?)

// Has there been a form submission?  If yes, go on and
// process...
 if (is_array($HTTP_POST_VARS)) {
	
	if ($resume != 'none') {
	$fname = './'.$resume_name;  // make a real filename
	// Get the content-type of the uploaded file

	if (preg_match("!/x\-.+!i", $resume_type)) 
		$type = OCTET;
	else
		$type = $resume_type;

	$from = sprintf("'%s' <%s>", $name, $email) ;
	copy($resume, $fname); //do error checking if need 	
	$mime = new MIME_mail($from, TO, 'Resume', "Please find attached my resume", "Cc: $email");
	$mime->fattach($fname, "Resume of $name", $type);
	$mime->send_mail();
	echo "Dear $name<p>Your resume has been emailed and a copy sent to you<br>";
	unlink($resume); 	// remove the uploaded file
	} else {
	echo "Dear $name<p>You have not submitted your resume.  Please use the browse button to attach it and click send!<br>";
	}
}
?>

<form name="upload" action="<?php echo $PHP_SELF;?>" method="post" enctype="multipart/form-data">
<table cellpadding=0 cellspacing=0 summary="upload table">
<tr>
  <td>Name:</td><td><input name="name" size=30 maxlength=60>
</tr>
<tr>
  <td>E-mail:</td><td><input name="email" size=30 maxlength=60>
</tr>
<tr>
  <td>Resume:</td><td><input type=file name="resume"></td>
</tr>
<tr>
  <td colspan=2 align="right"><input type=submit class='submit' name='action' value='Send'></td>
</tr>
</table>
</form>
