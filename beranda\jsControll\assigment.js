//chart assigment;
var chartAssigment;
$(document).ready(function() {
  var date1 = $("#from_date_assigment").val();
  var date2 = $("#to_date_assigment").val();
  var company = $("#company").val();
  requestBackend(date1, date2, company);
});

$("#reloadChartAssigment").on("submit", function(e){
  e.preventDefault();
  var date1 = $("#from_date_assigment").val();
  var date2 = $("#to_date_assigment").val();
  var company = $("#company").val();
  requestBackend(date1, date2, company, "chartIsReady");
})

$("#modalDetailAssigment").on("show.bs.modal", function(e){
  $("#tblDetailAssigment").find('tbody tr').remove();
  var name = $(e.relatedTarget).data("name");
  var _dateResponse = $("#_dateResponse").val();
  var company = $("#company").val();
  $(this).find('.modal-title').text("DETAIL "+name);
  var result;
  $.ajax({
    url: 'xhr/model2.php?p=detail-assigment',
    type: 'GET',
    dataType: 'JSON',
    data: {
      name: name,
      company: company,
      _dateResponse: _dateResponse
    },
    beforeSend: function(){
      $("#loadingModalAssigment").fadeIn(1000);
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    $("#loadingModalAssigment").fadeOut(1000, function() {
      if(result){
        var i = 1;
        $.each(data.dataDetail, function(index, data) {
          var content = "<tr>"+
                            "<td>"+i+"</td>"+
                            "<td>"+data.NO_INVOICE+"</td>"+
                            "<td>"+data.NAMA_VENDOR+"</td>"+
                            "<td>"+data.NOGROUP_VENDOR+"</td>"+
                            "<td>"+data.TGL_TERIMA+"</td>"+
                            "<td>"+data.ASSING_TO+"</td>"+
                            "<td>"+data.LAMA_PROSES+" Hari</td>"+
                        "</tr>";
          i++;
          $("#tblDetailAssigment").find('tbody').append(content);
        });
      } else{
        alert("500, INTERNAL SERVER ERROR");
      }
    });
  });

})

function requestBackend(date1, date2, company, set = null)
{
  $("#listExpediture").find('tbody tr').remove();
  var result;
  if(set == "chartIsReady"){
    chartAssigment.destroy();
  }
  $.ajax({
    url: 'xhr/model2.php?p=chart-assigment',
    type: 'GET',
    dataType: 'JSON',
    data: {
      from: date1,
      to: date2,
      company: company
    },
    beforeSend: function(){
      $("#loadingChartAss").fadeIn(1000);
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    $("#loadingChartAss").fadeOut(1000, function() {
      if(result){
            var ctx = document.getElementById("assigmentChart").getContext("2d");
            chartAssigment = new Chart(ctx, {
                type: 'bar',
                data: data.chart,
                options: {
                    responsive: true,
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Total semua assigment tiap Operator Distran'
                    },
                    scales: {
                      yAxes: [{
                         ticks: {
                           beginAtZero: true,
                           callback: function(value) {
                             if (value % 1 === 0) {
                               return value;
                             }
                           }
                         }
                       }]
                    }
                }
            });
            // _dateResponse
            $("#_dateResponse").val(data.tglPeriode);

            // table
            var i = 1;
            $.each(data.chart.labels, function(index, data){
              var content = "<tr>"+
                              "<td>"+i+"</td>"+
                              "<td>"+data+"</td>"+
                              "<td><a href='#' class='text-primary' data-name='"+data+"' data-toggle='modal' data-target='#modalDetailAssigment'>Detail</a></td>"+
                            "</tr>";
              $("#listExpediture").find('tbody').append(content);
              i++;
            })
      }
    });
  });
}
