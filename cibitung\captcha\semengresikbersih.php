<?
session_start();
require_once "Mail.php"; 

if($_POST['captcha'] == $_SESSION['captcha']){
	   'echo alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...")';
        $or_username = "DEVSD";
		$or_password = "gresik45";
		$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = dev-sggdata3.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))';				
		$conn = oci_connect($or_username, $or_password, $or_db);

	if(isset($_POST['simpan'])){  
		   
			$nama = $_POST['nama'];
		    $email = $_POST['email'];
			$telp = $_POST['telp'];
			$comment = $_POST['comment'];
			$capca = $_POST['capca'];						
		
			$update1= "INSERT INTO semen_gresik_bersih (nama,email,no_telpon,keterangan)values('".$nama."','".$email."','".$telp."','".$comment."')";
			$query_update1= oci_parse($conn, $update1); 
			$hasil_update1 = oci_execute($query_update1);

		   
					$from =$email;
					$to ="<EMAIL>";
					$subject = "Bersih";
					$body =$comment;

					$host = "sg2.sggrp.com";
					// $username = "";
					//$password = "";

					$headers = array ('From' => $from,
					'To' => $to,
					'Subject' => $subject);
					$smtp = Mail::factory('smtp',array ('host' => $host,'auth' => false));
					// 'username' => $username,
					// 'password' => $password));

					$mail = $smtp->send($to, $headers, $body);

					if (PEAR::isError($mail)) {
					echo("<p>" . $mail->getMessage() . "</p>");
					} else {
				("<p>Message successfully sent!</p>");
					}
		   
	}
}

    
?> 

<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
</head>
<body>

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Semen  Gresik Bersih </th>
</tr></table></div> 
	<div align="center">
    <table width="600" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Semen Gresik Bersih</span></th>
	</tr>
	</div>
	</table>
    <form  action="semengresikbersih.php" method="post" name="temukan" id="temukan" onsubmit="return cek_input()">
    <table width="600" align="center" class="adminform" >
	  <tr>
		<td align="left"><strong>NAMA</strong></td>
		<td><b>:</b></td>
		<td align="left"><input type="text" name="nama" size="40" value=" "  /></td>
	 </tr>
	 <tr>
		 <td align="left"><strong>EMAIL</strong></td>
		 <td><b>:</b></td>
	    <td>
		<div id="plantdiv">
	    <input name="email" type="text" class="inputlabel" id="email" value=" " size="50"/>
		</div>
	    </td>
	 </tr>
	  <tr>
		 <td align="left"><strong>NO TELPON</strong></td>
		 <td><b>:</b></td>
	    <td align="left"><input type="text" name="telp"  size="20"value=" "/></td>

	 </tr>

	 <tr>
		 <td align="left"><strong>COMMEN</strong></td>
		 <td><b>:</b></td>
	    <td align="left"><textarea name="comment" cols="50" rows="10"></textarea><br></td>

	 </tr>
     <tr>
	   <td align="left"><strong >CAPCA</strong></td>
	   <td><b>:</b></td>
	   <td><input type="text" name="captcha" />
			<br />
			<img src="captcha.php" />
	  </td>
	  </tr>
	  <tr>
		<td></td>
		<td></td>
		<td></td>		
     </tr >
	 <tr>
		<td></td>
		<td></td>
		<td></td>		
     </tr >
    <tr>
		<td colspan="3"><center>		
		<input name="simpan" type="submit" class="button" id="simpan" value="KIRIM" />&nbsp;&nbsp;
		<input class="button" type=button value='Cancel' onClick="self.history.back();">
		</center></td>
	</tr>

	</table>	
	<p>&nbsp;</p>
	</div>

</table>
</body>
</html>