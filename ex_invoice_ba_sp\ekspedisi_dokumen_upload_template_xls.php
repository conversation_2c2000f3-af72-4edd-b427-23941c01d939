<?php

session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
    header("Pragma: public");
}

    HeaderingExcel('template_upload_posting_ppl.xls');

    // Creating a workbook
    $workbook = new Workbook("-");
    // Adding format
    $format_bold = $workbook->add_format();
    $format_bold->set_bold();
    // $format_bold->set_align('center');
    // $format_bold->set_align('vcenter');
    // Creating the first worksheet
    $worksheet1 = $workbook->add_worksheet('Data Upload');
    //$worksheet1->set_column(1, 1, 40);
    //$worksheet1->set_row(1, 20);

    $worksheet1->write(1, 0, 'E_BELNR', $format_bold);

    for($i=1; $i<=1;$i++) {
        $worksheet1->write_string($i, 0, '');
    }

    for ($i = 0; $i <= 30; $i++) {
        $worksheet1->set_column($i, $i, 10);
    }

    $workbook->close();