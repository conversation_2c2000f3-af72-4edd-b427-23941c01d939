<? 
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=903;
//$user_id=$_SESSION['user_id'];
//$user_org=$_SESSION['user_org'];
//$distr=$_SESSION['distr_id'];


$user_id='401';
$user_org='2000';
$distr_id='139';
//$distr='0000000'.'$distr_id';
//$distr = sprintf("%010d", $distr_id);


$distr=$fungsi->sapcode($distr);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");

$page="jaminan1.php";
$currentPage="jaminan1.php";
$komen="";

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}

if(isset($_POST['cari'])){
		$distr= $_POST['distr'];
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

		$tgla = $_POST['tgla'];
		list($day,$month,$year)=split("-",$tgla);
		$tgla=$year.$month.$day;
		$tglt = $_POST['tglt'];
		list($day1,$month1,$year1)=split("-",$tglt);
		$tglt=$year1.$month1.$day1;

		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPFI_MTS_JMN_MURNI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
      //	$fce->LR_CODE = 
		$fce->LR_DIST->row["SIGN"]='|';
		$fce->LR_DIST->row["OPTION"]='EQ';
		$fce->LR_DIST->row["LOW"]=$distr;
		//$fce->LR_DIST->append(array("SIGN"=>"I","OPTION"=>"BT","LOW"=>$distr));
		

		$fce->LR_DATE->row["SIGN"]='|';
		$fce->LR_DATE->row["OPTION"]='BT';
		$fce->LR_DATE->row["LOW"]=$tglm;
		$fce->LR_DATE->row["HIGH"]=$tgls;
		$fce->LR_DATE->append(array("SIGN"=>"I","OPTION"=>"BT","LOW"=>$tglm,"HIGH"=>$tgls));

		$fce->LR_DATE1->row["SIGN"]='|';
		$fce->LR_DATE1->row["OPTION"]='BT';
		$fce->LR_DATE1->row["LOW"]=$tgla;
		$fce->LR_DATE1->row["HIGH"]=$tglt;
		$fce->LR_DATE1->append(array("SIGN"=>"I","OPTION"=>"BT","LOW"=>$tgla,"HIGH"=>$tglt));
	
		$face->I_FLAG='X';


		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			$s=0;
			while ( $fce->RETURN_DATA->Next() ){
				$number[$s] = $fce->RETURN_DATA->row["LCNUM"];
				$date1[$s] = $fce->RETURN_DATA->row["AADAT"];
				$customer[$s] = $fce->RETURN_DATA->row["KUNNR"];
				$type[$s] = $fce->RETURN_DATA->row["AKART"];
				$date2[$s] = $fce->RETURN_DATA->row["AXDAT"];
				$fdv[$s] = $fce->RETURN_DATA->row["WRTAK"];
				$currency[$s] = $fce->RETURN_DATA->row["WAERS"];
				$company[$s] = $fce->RETURN_DATA->row["BUKRS"];
				$bank_key[$s] = $fce->RETURN_DATA->row["BANKL"];
				$ext_financial[$s] = $fce->RETURN_DATA->row["BAANR"];
				$no_surat[$s] = $fce->RETURN_DATA->row["NO_SURAT"];
				$original_exp[$s] = $fce->RETURN_DATA->row["EDOAZ"];
				$name1[$s] = $fce->RETURN_DATA->row["NAME1"];
				$nama_bank[$s] = $fce->RETURN_DATA->row["BANKA"];
				$bank_branch[$s] = $fce->RETURN_DATA->row["BRNCH"];
				$total_nilai[$s] = $fce->RETURN_DATA->row["TOT_NILAI"];
				$catatan[$s] = $fce->RETURN_DATA->row["CATATAN"];
				$sigen[$s] = $fce->RETURN_DATA->row["TTD"];
				$jabatan[$s] = $fce->RETURN_DATA->row["JABATAN"];
				$nama[$s] = $fce->RETURN_DATA->row["NAMA"];
				$type_nama[$s] = $fce->RETURN_DATA->row["TYPE_NAME"];
				$tanggal1[$s] = $fce->RETURN_DATA->row["DATE_LOW"];
				$tanggal2[$s] = $fce->RETURN_DATA->row["DATE_HIGH"];
				$flax[$s] = $fce->RETURN_DATA->row["SIGN"];
				
				$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($customer);
	
}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Jaminan Murni Yang Akan jatuh Tempo </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Jaminan Murni Yang Akan jatuh Tempo </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('tgl1','','R','tgl2','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Distributor </td>
      <td  class="puso">:</td>
      <td ><input name="distr" type="text" id="distr" value="" size="10" maxlength="10"/></td></tr>
    <tr>
      <td  class="puso">Tanggal Mulai</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value=" " onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="" onClick="return showCalendar('tgl2');"/></td>
    </tr>
	<tr>
      <td  class="puso">Tanggal Jatuh Tempo</td>
      <td  class="puso">:</td>
      <td ><input name="tgla" type="texa" id="tgla" size=12 value=" " onClick="return showCalendar('tgla');"/>&nbsp; s.d &nbsp;
	<input name="tglt" type="texa" id="tglt" size=12 value=" " onClick="return showCalendar('tglt');"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="1000" align="center" class="adminlist" border='0'>
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		 <td align="center"><strong>Distributor kode</strong></td>
		<td align="center"><strong >Type</strong></td>
		<td align="center"><strong >No surat</strong></td>
		<td align="center"><strong >No.Entry</strong></td>
		<td align="center"><strong >Bukti Pendukung</strong></td>
		<td align="center"><strong >Keterangan</strong></td>
		<td align="center"><strong >Nilai</strong></td>
		<td ><table border='0'><tr align='center'><td colspan='2'><strong >Berlaku</strong ></td></tr><tr>
		<td align="center"><strong >Mulai&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></td>
		<td align="center"><strong >jatuh Tempo</strong></td></tr></table></td>
		
	</tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$fdv[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $customer[$i]; ?></td>
		<td align="left"><? echo $type[$i]; ?></td>
		<td align="left"><? echo $no_surat[$i]; ?></td>
		<td align="left"><? echo $number[$i]; ?></td>
		<td align="left"><? echo $ext_financial[$i]; ?></td>		
		<td align="left"><? echo $nama_bank[$i]; ?></td>
		<td align="left"><? echo $fdv[$i]; ?></td>		
		<td ><table><tr align='center'><td colspan='2'></td></tr><tr>
		<td align="center"><? echo $date1[$i]; ?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
		<td align="center"><? echo $date2[$i]; ?></td></tr></table></td>
		
	
		

		</tr>
	  <? } ?>
                <tr>
                    <td></td>
                    <td colspan="6"><strong >TOTAL</strong ></td>
                    <td align="right"><strong ><? echo number_format($totaldo,3,".",","); ?></strong ></td>
                                      
                </tr>
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
