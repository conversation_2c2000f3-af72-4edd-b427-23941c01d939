<?
switch ($action) {


case "harga":

		echo $sql ="INSERT INTO HARGA_TEBUS( NO_KONTRAK, SHIP_TO, KD_DISTRIK, H_TEBUS_AFT, H_TEBUS_AFG, H_TEBUS_FRC) VALUES ('".$NO_KONTRAK."', '".$SHIP_TO."','".$KD_DISTRIK."','".$H_TEBUS_AFT."','".$H_TEBUS_AFG."','".$H_TEBUS_FRC."')";
		$query= oci_parse($conn,$sql);
		oci_execute($query,OCI_DEFAULT);
		oci_commit($conn);
		if ($query) {
			 $show_ket="<br>Input data berhasil";
			} else {
			 $show_ket="<br>Maaf input data gagal";
		}
			$habis = "tebus.php";
			break;
	

	
//============================================================================================================================
   
		case "kontraktor":
		echo $sql ="INSERT INTO M_KONTRAKTOR(NM_KONTRAKTOR, ALMT_KONTRAKTOR, CONTACT_P, TELEPON) VALUES ('".$NM_KONTRAKTOR."','".$ALMT_KONTRAKTOR."', '".$CONTACT_P."','".$TELEPON."')";
		$query= oci_parse($conn,$sql);
		oci_execute($query,OCI_DEFAULT);
		oci_commit($conn);
		if ($query) {
			$show_ket="<br>Input data berhasil";
			} else {
			$show_ket="<br>Maaf input data gagal";
		}
			$habis = "jualan.php";
			break;


//==============================================================================================================================


case "verifikasi":
		echo $sql= "UPDATE EVALUASI SET STATUS_EV='$STATUS_EVA' where ID= $NO_EV ";
		$query= oci_parse($conn,$sql);
		oci_execute($query,OCI_DEFAULT);
		oci_commit($conn);
		if ($query) {
			$show_ket="<br>Input data berhasil";
			} else {
			$show_ket="<br>Maaf input data gagal";
		}
			$habis = "evaluasi_app.php";
			break;



			
}

?>
