

***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 10:16:40
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 11:17:18
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 12:32:15
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 13:40:41
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 15-DEC-2022 13:55:13
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 06:34:48
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 07:00:39
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 08:19:10
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 08:34:31
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 08:36:51
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 16-DEC-2022 14:56:20
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 19-DEC-2022 13:01:39
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 19-DEC-2022 14:25:10
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 04:13:05
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 05:16:53
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 07:27:06
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 07:37:03
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 07:43:34
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 07:51:15
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 07:54:16
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 08:21:25
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 08:41:23
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 08:43:14
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 08:45:59
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 11:02:09
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 20-DEC-2022 12:28:58
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 07:24:21
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 07:49:39
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:07:50
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:38:47
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:41:14
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:42:30
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:43:56
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 08:45:09
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:11:41
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:19:45
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:33:50
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:38:05
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
    nt OS err code: 0
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:42:05
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 26-DEC-2022 10:47:48
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 28-DEC-2022 10:07:26
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 28-DEC-2022 10:17:49
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 02-JAN-2023 10:47:07
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 11-JAN-2023 21:42:29
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=DEVSGG)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 01:01:34
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 06:51:49
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 07:08:52
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 07:56:57
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 12-JAN-2023 09:36:47
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 24-JAN-2023 11:22:17
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 24-JAN-2023 15:23:15
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 27-JAN-2023 10:09:38
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 03-FEB-2023 13:06:24
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 03-FEB-2023 13:15:08
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 03-FEB-2023 14:32:29
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 03-FEB-2023 14:48:42
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 03-FEB-2023 15:20:13
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0


***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SID=INO)(CID=(PROGRAM=httpd)(HOST=localhost.localdomain)(USER=nobody))))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 04-FEB-2023 14:08:03
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
