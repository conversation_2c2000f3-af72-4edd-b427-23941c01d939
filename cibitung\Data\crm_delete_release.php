<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$user_id=379;
$user_org=2000;
$page="crm_delete_release.php";
$distr_id=$fungsi->sapcode(260);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");
$nm_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"NAMA_PLANT");

if(isset($_POST['cari'])){
	    $nopol = $_POST['nopol'];
		$kddistr = $_POST['kddistr'];
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

       
		$sql="select a.NO_TRANSAKSI,b.NO_TRANSAKSI,a.NO_SPJ,a.KD_DISTR,a.NM_DISTR,a.KD_SHIPTO,a.NM_SHIPTO,a.ALAMAT,a.DELETE_MARK,a.CREATE_DATE,a.QTY_DO,a.KD_DISTRIK,a.NM_DISTRIK,a.KD_MATERIAL, a.NM_MATERIAL from crm_spj_dtl a,crm_spj_hdr b where a.no_transaksi=b.no_transaksi and a.delete_mark='0'and b.kd_distr='".$kddistr."' and b.nopol='".$nopol."' and   a.create_date BETWEEN TO_Date('$tglm 00:00:00', 'YYYYMMDD HH24:MI:SS')AND TO_Date('$tgls 23:59:59', 'YYYYMMDD HH24:MI:SS') ";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$s=0;
		$tglstock = date('Ymd');
		while($data=oci_fetch_array($query)){
		 	$idnya[$s]=$data['ID'];
			$NO_TRANSAKSI[$s]=$data['NO_TRANSAKSI'];
			$NO_SPJ[$s]=$data['NO_SPJ'];
			$KD_DISTR[$s]=$data['KD_DISTR'];
			$NM_DISTR[$s]=$data['NM_DISTR'];
			$KD_SHIPTO[$s]=$data['KD_SHIPTO'];
			$NM_SHIPTO[$s]=$data['NM_SHIPTO'];
			$ALAMAT[$s]=$data['ALAMAT'];
			$DELETE_MARK[$s]=$data['DELETE_MARK'];
			$CREATE_DATE[$s]=$data['CREATE_DATE'];
			$QTY_DO[$s]=$data['QTY_DO'];
			$DISTRIK[$s] = $data["KD_DISTRIK"];
			$NAMA_KOTA[$s] =$data["NM_DISTRIK"];
			$KD_MATERIAL[$s] = $data["KD_MATERIAL"];
			$NM_MATERIAL[$s] =$data["NM_MATERIAL"];		

		
		$s++;	
			}
			$total=count($KD_SHIPTO);	
	}
	if(isset($_POST['simpan'])){  
		echo	$FLAX = $_POST['FLAX'];
		echo	$NO_TRANSAKSI = $_POST['NO_TRANSAKSI'];
		echo    $QTY_DO = $_POST['qty'];
		echo	$KD_MATERIAL = $_POST['kd_material'];

			 
		echo $update1= "UPDATE CRM_SPJ_DTL  SET DELETE_MARK='$FLAX' WHERE NO_TRANSAKSI='$NO_TRANSAKSI'";
				$query_update1= oci_parse($conn, $update1);
				$hasil_update1 = oci_execute($query_update1);

		echo '<br>'. $update3= "UPDATE CRM_SPJ_HDR  SET DELETE_MARK='$FLAX' WHERE NO_TRANSAKSI='$NO_TRANSAKSI'";
				$query_update3= oci_parse($conn, $update3);
				$hasil_update3 = oci_execute($query_update3);

		echo'<br>'.	$update2= "UPDATE CRM_STOK_GDG SET STOK=STOK+$QTY_DO WHERE KD_GDG='261001' AND KD_MATERIAL='$KD_MATERIAL'";
				$query_update2= oci_parse($conn, $update2);
				$hasil_update2 = oci_execute($query_update2);
			   
	}	

?> 
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>
<body>

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Delete Release </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Delete </th>
</tr>
</table>
</div>

<form id="tambah"  method="post" action="<? echo $page; ?>" onSubmit="tes()">
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>    
	<tr>
      <td  class="puso">Kode Distributor</td>
      <td  class="puso">:</td>
      <td ><input name="kddistr" type="text" id="kddistr" size=12 value=" "/></td>
    </tr>
	<tr>
      <td  class="puso">No Polisi</td>
      <td  class="puso">:</td>
      <td ><input name="nopol" type="text" id="nopol" size=12 value=" "/></td>
    </tr>
	<tr>
      <td  class="puso">Priode</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Cari" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
<div align="center">
    <table width="900" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Edit Release</span></th>
	</tr>
	</div>
<form  action="crm_delete_release.php" method="post" name="temukan" id="temukan" onsubmit="return cek_input()">
<table width="900" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong>No SPJ</strong></td>
		 <td align="center"><strong>Nama Distributor</strong></td>
		<td align="center"><strong >Kode Toko</strong></td>
		<td align="center"><strong >Nama Toko</strong></td>
		<td align="center"><strong >Alamat</strong></td>
		<td align="center"><strong >Tgl release</strong></td>
		<td align="center"><strong >Kota</strong></td>
		<td align="center"><strong >QTY</strong></td>
		<td align="center"><strong >Status</strong></td>
		<td align="center"><strong >Action</strong></td>
      </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
 
		?>   
		
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $NO_SPJ[$i]; ?></td>
		<td align="left"><? echo $NM_DISTR[$i]; ?></td>
		<td align="left"><? echo $KD_SHIPTO[$i]; ?></td>
		<td align="left"><? echo $NM_SHIPTO[$i]; ?></td>
		<td align="left"><? echo $ALAMAT[$i]; ?></td>
		<td align="left"><? echo $CREATE_DATE[$i]; ?></td>
		<td align="left"><? echo $NAMA_KOTA[$i]; ?></td>
		<td align="left"><? echo $QTY_DO[$i]; ?></td>
		<td align="left"><? echo $DELETE_MARK[$i]; ?></td>
		<td align="left"><?echo "<input type='checkbox' name='FLAX' id='FLAX'  value='1' />";?></td>	   
		</tr>
		<td align="right" colspan="20"><center>		
		<input name="simpan" type="submit" class="button" id="simpan" value="DELETE" />&nbsp;&nbsp;
		<input class="button" type=button value='Cancel' onClick="self.history.back();">
		<input type="hidden" name="NO_TRANSAKSI"  value="<? echo $NO_TRANSAKSI[$i]; ?>" readonly="true" />
		<input type="hidden" name="qty"  value="<? echo $QTY_DO[$i]; ?>" readonly="true" />
		<input type="hidden" name="kd_material"  value="<? echo $KD_MATERIAL[$i]; ?>" readonly="true" /></center>
	   </td>
	  
	  <? } ?>
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
</table>
</body>
</html>