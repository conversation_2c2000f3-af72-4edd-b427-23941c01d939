<?php
require_once ("potonganoa_formula.php");
require_once('phpmailer.php');
require_once('class.smtp.php');

$base_url      = "https://dev-app.sig.id/dev/sd/sdonline/"; //dev
// $base_url      = "https://csms.sig.id/sdonline/"; //prod
define("BASE_URL", $base_url );

if (!function_exists('get_base_url')) {
    function get_base_url() {
        return BASE_URL;
    }
}

if (!function_exists('get_base_home')) {
    function get_base_home() {
        return BASE_URL . 'login.php';
    }
}

if (!function_exists('sendMail')){
    function sendMail($emailTO, $emailTOCC, $subject, $no_transaksi, $content, $contentTable="",$approveLink=""){
      // $emailTOCC = "<EMAIL>";
      $mail = new PHPMailer();
      $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
      $base_url .= str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
      $link = $base_url;
      $message1 = "<html><head></head>";
      
      $message1 .= "<thead>
      <tr>
      <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
      <h2><b>".$subject." ".$no_transaksi."</b></h2>
      <br/>
      <p>".$content."</p>
      </td>
  </tr></thead>";

      $message1 .= $contentTable;
      
      $message1 .= "</tbody></table>";
      if($approveLink){
          $message1 .= "<p>Untuk menindaklanjuti pengajuan tersebut bisa dilakukan dengan menekan tombol Approve berikut</p>
              <a href=\"$approveLink\" style=\"display: block; margin: 10px auto; padding: 10px 15px; background-color: green; color: white; text-align: center; text-decoration: none; border-radius: 5px; width: 50%;\">Approve</a>
              ";
      }
      $message1 .= "<p>untuk melihat detailnya bisa dilakukan di aplikasi CSMS dengan:</p>
                <a href=".get_base_home().">Klik Link berikut</a>";  

      $message1 .= "</div>";
      $message1 .= "</html>";

      $mail->IsSMTP(); 
      $mail->SMTPDebug  = 1;                   
      $mail->Host       = "relay.sig.id";     
      $mail->Port       = 25;                
      $mail->SetFrom('<EMAIL>', $subject, $no_transaksi); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
      $mail->Subject   = $subject;//masukkan subject
      $mail->MsgHTML($message1);//masukkan isi dari email
      //$mail->AddCC($emailcc);
      // $mail->AddAddress('<EMAIL>');
      
      if($emailTO != ''){
          $explodeEmailTo = explode(',',$emailTO);
          $j = count($explodeEmailTo);
          for ($i=0; $i < $j ; $i++) { 
              # code...
              //var_dump($explodeEmailTo[$i]);
              $mail->AddAddress($explodeEmailTo[$i]);//masukkan penerima
          }
      }
      
      if($emailTOCC != ''){
          $explodeEmailCC = explode(',',$emailTOCC);
          $j = count($explodeEmailCC);
          for ($i=0; $i < $j ; $i++) {
              # code...
              //var_dump($explodeEmailCC[$i]);
              $mail->AddCC($explodeEmailCC[$i]);//masukkan penerima CC
          }
      }
      $mail->AddBCC('<EMAIL>');
      
      if(!$mail->Send()) {
          echo "Mailer Error bro: " . $mail->ErrorInfo; // jika pesan tidak terkirim
      }else {
          //  echo "sukses bro ";
      }
  }
}

if (!function_exists('sendMailApprove')){
  function sendMailApprove($NO_BA_in,$org_in,$no_vendor_in,$nama_vendor_in,$total_semen_in,$total_pdpks_in,$total_shp_in,$emailTO,$emailTOCC,$approve_link = ''){

    // $emailTOCC = "<EMAIL>";
    $mail             = new PHPMailer();
    $message1  = "<html><head></head>";
  
    $message1 .= "<thead>
    <tr>
    <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
      <h2><b>Notification Approval BASTP ".$NO_BA_in."</b></h2>
      <br/>
      <p>Mohon untuk ditindaklanjuti pengajuan BASTP dengan detail di bawah ini :</p>
    </td>
  </tr>
                 </thead>";
    $message1 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
    $message1 .= "
        <div align=\"center\">
        
        <thead>
          <tr class=\"quote\">
            <td ><strong>&nbsp;&nbsp;No.</strong></td>
            <td align=\"center\"><strong >ORG</strong></td>
            <td align=\"center\"><strong >BASTP REKAPITULASI</strong></td>
            <td align=\"center\"><strong >EKSPEDITUR</strong></td>
            <td align=\"center\"><strong >NAMA EKSPEDITUR</strong></td>
             <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
             <td align=\"center\"><strong>PDPKS</strong></td>
             <td align=\"center\"><strong>TOTAL</strong></td>
             <td align=\"center\"><strong>STATUS</strong></td>
          </tr >
          </thead>
          <tbody>
                 ";
    
          
    
    $message1.= " 
    <td align=\"center\">1</td>
    <td align=\"center\">".$org_in."</td>       
    <td align=\"center\">".$NO_BA_in."</td>
    <td align=\"center\">".$no_vendor_in."</td>
    <td align=\"center\">".$nama_vendor_in."</td>
    <td align=\"center\">".number_format($total_semen_in,0,",",".")."</td>
    <td align=\"center\">".number_format($total_pdpks_in,0,",",".")."</td>
    <td align=\"center\">".number_format($total_shp_in,2,",",".")."</td>
    <td align=\"center\">Open</td>
    </tr>";
    
           
    $message1 .= "</tbody></table> ";
    if($approve_link){
      $message1 .= "<p>Untuk menindaklanjuti pengajuan tersebut bisa dilakukan dengan menekan tombol Approve berikut</p>
      <a href=\"$approve_link\" style=\"display: block; margin: 10px auto; padding: 10px 15px; background-color: green; color: white; text-align: center; text-decoration: none; border-radius: 5px; width: 50%;\">Approve</a>";
    }

    $message1 .= "<p>untuk melihat detailnya bisa dilakukan di aplikasi CSMS dengan:</p>
                <a href=".get_base_home().">Klik Link berikut</a>";  

    $message1 .= "</div>";
    $message1 .= "</html>";
                    $mail->IsSMTP(); 
                    $mail->SMTPDebug  = 1;                   
                    $mail->Host       = "relay.sig.id";     
                    $mail->Port       = 25;                
                    $mail->SetFrom('<EMAIL>', 'Notification Approval BASTP ',$NO_BA_in); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
                    //masukkan subject
                    if($approve_link){
                      $mail->Subject   = "Otomatis - Notification Approval BASTP";
                    }else{
                      $mail->Subject   = "Notification Approval BASTP";
                    }
                    
                    $mail->MsgHTML($message1);//masukkan isi dari email
                    //$mail->AddCC($emailcc);
                    // $mail->AddAddress('<EMAIL>');
                    
                    $explodeEmailTo = explode(',',$emailTO);
                    $j = count($explodeEmailTo);
                    for ($i=0; $i < $j ; $i++) { 
                        # code...
                        //var_dump($explodeEmailTo[$i]);
                        $mail->AddAddress($explodeEmailTo[$i]);//masukkan penerima
                    }
                    
                if($emailTOCC != ''){
                    $explodeEmailCC = explode(',',$emailTOCC);
                    $j = count($explodeEmailCC);
                    for ($i=0; $i < $j ; $i++) { 
                        # code...
                        //var_dump($explodeEmailCC[$i]);
                        $mail->AddCC($explodeEmailCC[$i]);//masukkan penerima CC
                    }
                }
                    $mail->AddBCC('<EMAIL>');//masukkan penerima BCC
                  
                    if(!$mail->Send()) {
                        echo "Mailer Error bro: " . $mail->ErrorInfo; // jika pesan tidak terkirim
                    }else {
                        //  echo "sukses bro ";
                    }
    }
}

if (!function_exists('sendMailReject')){
  function sendMailReject($NO_BA_in,$org_in,$no_vendor_in,$nama_vendor_in,$total_semen_in,$total_pdpks_in,$total_shp_in,$emailTO,$emailTOCC){


    $mail             = new PHPMailer();
    $link       = "http://***********/dev/sd/sdonline/login.php";
    $message1  = "<html><head></head>";
    
    $message1 .= "<thead>
    <tr>
    <td valign=\"top\" style=\"padding-bottom:20px; background-color:#ffffff;\" colspan=\"2\">
      <h2><b>Notification reject BASTP ".$NO_BA_in."</b></h2>
      <br/>
      <p>No BASTP ".$NO_BA_in." Telah di Reject</p>
    </td>
  </tr>
                 </thead>";
    $message1 .= "<table border=1 style='width:100%;font-family: tahoma; font-size: 12; border-collapse:collapse;' cellspacing='0' cellpadding='2' bordercolor='#000000'>";
    $message1 .= "
        <div align=\"center\">
        
        <thead>
          <tr class=\"quote\">
            <td ><strong>&nbsp;&nbsp;No.</strong></td>
            <td align=\"center\"><strong >ORG</strong></td>
            <td align=\"center\"><strong >BASTP REKAPITULASI</strong></td>
            <td align=\"center\"><strong >EXPEDITUR</strong></td>
            <td align=\"center\"><strong >NAMA EXPEDITUR</strong></td>
             <td align=\"center\"><strong>KLAIM SEMEN</strong></td>
             <td align=\"center\"><strong>PDPKS</strong></td>
             <td align=\"center\"><strong>TOTAL</strong></td>
             <td align=\"center\"><strong>STATUS</strong></td>
          </tr >
          </thead>
          <tbody>
                 ";
    
          
    
            $message1.= " 
            <td align=\"center\">1</td>
            <td align=\"center\">".$org_in."</td>       
            <td align=\"center\">".$NO_BA_in."</td>
            <td align=\"center\">".$no_vendor_in."</td>
            <td align=\"center\">".$nama_vendor_in."</td>
            <td align=\"center\">".number_format($total_semen_in,0,",",".")."</td>
            <td align=\"center\">".number_format($total_pdpks_in,0,",",".")."</td>
            <td align=\"center\">".number_format($total_shp_in,2,",",".")."</td>
            <td align=\"center\">Open</td>
            </tr>";
    
           
    
    $message1 .= "</tbody></table>
            <p>Untuk meihat detail reject tersebut bisa di check di aplikasi CSMS berikut</p>
            <a href=".get_base_home().">Klik Link berikut</a>
        </div>";
    $message1 .= "</html>";
                    $mail->IsSMTP(); 
                    $mail->SMTPDebug  = 1;                   
                    $mail->Host       = "relay.sig.id";     
                    $mail->Port       = 25;                
                    $mail->SetFrom('<EMAIL>', 'Notification reject BASTP ',$NO_BA_in); // masukkan alamat pengririm dan nama pengirim jika alamat email tidak sama, maka yang digunakan alamat email untuk username
                    $mail->Subject   = "Notification reject BASTP";//masukkan subject
                    $mail->MsgHTML($message1);//masukkan isi dari email
                    //$mail->AddCC($emailcc);
                    // $mail->AddAddress('<EMAIL>');
                    
                    $explodeEmailTo = explode(',',$emailTO);
                    $j = count($explodeEmailTo);
                    for ($i=0; $i < $j ; $i++) { 
                        # code...
                        //var_dump($explodeEmailTo[$i]);
                        $mail->AddAddress($explodeEmailTo[$i]);//masukkan penerima
                    }
                    
                   $explodeEmailCC = explode(',',$emailTOCC);
                    $j = count($explodeEmailCC);
                    for ($i=0; $i < $j ; $i++) { 
                        # code...
                        //var_dump($explodeEmailCC[$i]);
                        $mail->AddCC($explodeEmailCC[$i]);//masukkan penerima CC
                    }
                    $mail->AddBCC('<EMAIL>');//masukkan penerima BCC
                  
                    if(!$mail->Send()) {
                        echo "Mailer Error bro: " . $mail->ErrorInfo; // jika pesan tidak terkirim
                    }else {
                        //  echo "sukses bro ";
                    }
    }
}


function get_user_approval($conn, $vendor){
  $query = "
  SELECT
    TUB.*,
    EUP.STATUS_AKTIF
  FROM
    TB_USER_BOOKING TUB
  LEFT JOIN EX_BA_USER_APPROVAL EUP ON
    EUP.ID_USER = TUB.ID
  WHERE
    TUB.DELETE_MARK = 0
    AND EUP.STATUS_AKTIF = 1
    AND ( TUB.VENDOR_NAME LIKE '$vendor'
      OR TUB.VENDOR LIKE '".str_pad($vendor,10,"0",STR_PAD_LEFT)."' )
    AND ROWNUM = 1
  ORDER BY
    TUB.NAMA ASC
  ";

$query = oci_parse($conn, $query);
oci_execute($query);

return oci_fetch_array($query);
}

function getDistanceMeter($lat1, $lon1, $lat2, $lon2) {
  $earth_radius = 6371000; // radius bumi dalam meter

  $dLat = deg2rad($lat2 - $lat1);
  $dLon = deg2rad($lon2 - $lon1);

  $a = sin($dLat/2) * sin($dLat/2) +
       cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
       sin($dLon/2) * sin($dLon/2);
       
  $c = 2 * atan2(sqrt($a), sqrt(1-$a));

  return $earth_radius * $c;
}

function ex_cari_vendor_sp($vendor)
	{
	$sap = new SAPConnection();
	$sap->Connect("../include/sapclasses/logon_data.conf");
	if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   echo $sap->PrintStatus();
	   exit;
	}
	
		$fce = $sap->NewFunction ("Z_ZCSD_VENDOR");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$ktokk="4100";

    $panjang=strlen(strval($vendor));
		if($panjang==1)$vendor='000000000'.$vendor;
		if($panjang==2)$vendor='00000000'.$vendor;
		if($panjang==3)$vendor='0000000'.$vendor;
		if($panjang==4)$vendor='000000'.$vendor;
		if($panjang==5)$vendor='00000'.$vendor;
		if($panjang==6)$vendor='0000'.$vendor;
		if($panjang==7)$vendor='000'.$vendor;
		if($panjang==8)$vendor='00'.$vendor;
		if($panjang==9)$vendor='0'.$vendor;

		$fce->XKTOKK = $ktokk;
		$fce->XDLGRP = "";
		$fce->XLIFNR = $vendor;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			while ( $fce->RETURN_DATA->Next() ){
				$lifnr= $fce->RETURN_DATA->row["LIFNR"];
				$nama= str_replace('"','',$fce->RETURN_DATA->row["NAME1"]);
				$kota=$fce->RETURN_DATA->row["ORT01"];
				$alamat=$fce->RETURN_DATA->row["STRAS"];

		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		return $kota;
	}

  function get_batas_jarak_geofence($conn){
    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Batas Jarak Geofence(Meter)'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

  $query = oci_parse($conn, $query);
  oci_execute($query);
  $data = oci_fetch_array($query);
  return $data['VALUE'];
  }

  function get_batas_waktu_approval($conn){
    $query = "
        SELECT NAMA, \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA LIKE '%Batas Waktu Approval(Jam)%'
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      if (strpos($row['NAMA'], 'Approval Ekspeditur') !== false) {
          $data['LEVEL1'] = $row['VALUE'];
      } else if(strpos($row['NAMA'], 'Approval Admin Transportasi') !== false){
          $data['LEVEL2'] = $row['VALUE'];
      } else if(strpos($row['NAMA'], 'Approval Pejabat Transportasi 1') !== false){
          $data['LEVEL3'] = $row['VALUE'];
      }else if(strpos($row['NAMA'], 'Approval Pejabat Transportasi 2') !== false){
          $data['LEVEL4'] = $row['VALUE'];
      }
    }

    return $data;
  }

class User_SP{
  private $username_admin_portal = 'ADMIN_PORTAL_SP';
  private $org = '';
  private $conn;

  public function __construct($org = '') {
    if (file_exists('../include/ex_fungsi.php')) {
      include_once '../include/ex_fungsi.php';
    } elseif (file_exists('../../include/ex_fungsi.php')) {
      include_once '../../include/ex_fungsi.php';
    }

    $fungsi=new ex_fungsi();
    $this->conn=$fungsi->ex_koneksi();

    if (isset($_SESSION['user_org']) && !empty($_SESSION['user_org']) && !$org) {
        $this->org = $_SESSION['user_org'];
    }else{
        $this->org = $org;
    }
  }

  function get_admin_portal(){
    $query = "
    SELECT
      ID,
      NAMA,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND UPPER(NAMA) = UPPER('" . $this->username_admin_portal . "')
      AND ROWNUM = 1
    ORDER BY
      NAMA ASC
    ";

  $query = oci_parse($this->conn, $query);
  oci_execute($query);

  return oci_fetch_array($query);
  }

  function get_pejabat_eks_manual(){
    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Ekspeditur - Manual BASTP'
        AND ORG = '$this->org'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

  function get_pejabat_eks_auto(){
    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Ekspeditur - Auto BASTP'
        AND ORG = '$this->org'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

  function get_admin_trans(){
    $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Admin Transportasi'
        AND ORG = '$this->org'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

function get_kasie(){
  $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Pejabat Transportasi 1'
        AND ORG = '$this->org'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
}

function get_kabiro(){
  $query = "
        SELECT \"VALUE\"
        FROM EX_MASTER_CONFIG
        WHERE NAMA = 'Approver Pejabat Transportasi 2'
        AND ORG = '$this->org'
        AND ROWNUM = 1
        ORDER BY LAST_UPDATED_DATE DESC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);
    $data = oci_fetch_array($query);
    $ids = $data['VALUE'];

    $query = "
    SELECT
      ID,
      NAMA,
      NAMA_LENGKAP,
      ALAMAT_EMAIL
    FROM
      TB_USER_BOOKING
    WHERE
      DELETE_MARK = 0
      AND ID IN ($ids)
    ORDER BY
      NAMA ASC
    ";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }

  function get_users(){
    $query = "SELECT tub.ID, tub.ALAMAT_EMAIL, tub.NAMA_LENGKAP, tub.NAMA
              FROM EX_USER_APPROVER eua 
              LEFT JOIN TB_USER_BOOKING tub 
              ON eua.USER_ID = tub.ID
              WHERE eua.ORG = '$this->org'
              ORDER BY NAMA_LENGKAP";

    $query = oci_parse($this->conn, $query);
    oci_execute($query);

    $data = array();
    while($row=oci_fetch_array($query)){
      array_push($data, $row);
    }

    return $data;
  }
}
class PDFExporter
{
    public function beritaAcara($no_ba)
    {
        $urlPrint = 'https://10.4.194.150/dev/sd/sdonline/ex_ba_sp/print_ba_new.php' . '?no_ba=' . $no_ba; // dev
        // $urlPrint = 'https://csms.sig.id/sdonline/ex_ba/print_ba_new.php' . '?no_ba=' . $no_ba; // prod
        //$urlPrint = 'http://***********/dev/sd/sdonline/ex_ba/print_ba_new.php' . '?no_ba=' . $no_ba;
        // $urlPrint = 'http://**********/sdonline/ex_ba/print_ba_new.php' . '?no_ba=' . $no_ba;
        $urlConverter = "https://skedul.sig.id/bi/skedul/e_invoice/print_ba.php";

        // Mendapatkan struktur html yang akan dikirim saat render pdf
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $urlPrint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);
        $response = curl_exec($ch);

        // Jika terdapat error saat request
        if (curl_errno($ch)) {
            echo "URL: " . $urlPrint . "<br />";
            echo curl_errno($ch) . " " . curl_error($ch);
            curl_close($ch);
            die();
        }

        curl_close($ch);

        $headers = array(
            'Content-Type: application/json',
        );

        $dataRenderPdf = array(
            'content' => $response,
        );

        // Merender html menjadi pdf
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $urlConverter);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($dataRenderPdf));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSLVERSION,3);

        $response = curl_exec($ch);

        // Jika terdapat error saat request
        if (curl_errno($ch)) {
            echo "URL: " . $urlRenderPdf . "<br />";
            echo curl_errno($ch) . " " . curl_error($ch);
            curl_close($ch);
            die();
        }

        curl_close($ch);

        return $response;
    }
}

class Customer
{
    private $url = 'https://integrasi-api.sig.id/csms/sdonline/service/list_customer';
    private $token = '0W1BBNICFA';
    private $v_udate2 = '9999-12-28';

    public function getList($ship_to, $org)
    {
        $response = array(
          'success' => false,
          'msg' => '',
          'data' => array()
        );

        if(!$ship_to){
          $response['msg'] = 'ship_to is required!';
          return $response;
        };

        if(!$org){
          $response['msg'] = 'org is required!';
          return $response;
        };

        $fields = array(
            'token'     => $this->token,
            'V_KORG'    => $org,
            'V_KUNNR'   => $ship_to,
            'V_UDATE2'  => $this->v_udate2
        );

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Optional: for SSL issues

        $data = curl_exec($ch);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
          $response['msg'] = 'Error: ' . $error;
          return $response;
        }

        $data = json_decode($data, true);

        if($data['status'] === false){
          $response['msg'] = $data['keterangan'];
          return $response;
        }

        if (is_array($data)) {
          foreach ($data as $item) {
              if ($item['LAT'] && $item['XLONG']) {
                $response['data']['LAT'] = $item['LAT'];
                $response['data']['XLONG'] = $item['XLONG'];
              }
          }
        }
        $response['success'] = true;
        $response['msg'] = 'Success Get Data';

        return $response;
    }
}

?>