<?php
  session_start();
  include ('../../include/ex_fungsi.php');
  $fungsi=new ex_fungsi();
  $conn=$fungsi->ex_koneksi();
  $user_id=trim($_SESSION['user_id']);
  $user_org=$_SESSION['user_org'];
  $user_name=$_SESSION['user_name'];
  $distr=$_SESSION['distr_id'];
  $distr=$fungsi->sapcode($distr);
  
  $user_data=$fungsi->ex_find_user($conn,$user_id);
  $vendor=$user_data["vendor"];  
  
  if($vendor != ""){
      $filter .= " AND I.NO_VENDOR ='".$vendor."'"; // Filter tabel ex_invoice
      $filter1 .= " AND VENDOR ='".$vendor."'"; // Filter tabel ex_trans_hdr
      $filter2 .= " AND NO_VENDOR ='".$vendor."'"; // Filter tabel kpi_terima_invoice_vendor
  } else {
      $filter .= "";
      $filter1 .= "";
      $filter2 .= "";
  }
      
  if($user_id==''){
    echo json_encode(array("Auth"=>false));
  } else {
    $p = (!empty($_GET["p"]) ? $_GET["p"] : "");
    switch ($p) {
      case 'multiline-spj':

        $label = array();
        $tglAwal = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
        $tglAkhir = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $tgl1 = date("d", strtotime($tglAwal));
        $tgl2 = date("d", strtotime($tglAkhir));
        $label[] .= $tglAwal;
        while (strtotime($tglAwal) < strtotime($tglAkhir)) {
          $tglAwal = date ("Y-m-d", strtotime("+1 day", strtotime($tglAwal)));
          $label[] .= $tglAwal;
  	    }        
        //query1
        $data = array();
        
          foreach ($label as $date) {
            $q2 = "select TO_CHAR(CREATE_DATE, 'YYYY-MM-DD') AS CREATE_DATE, count(id) as TOTAL from EX_TRANS_HDR where ORG = '".$company."' and delete_mark = '0'
                    and CREATE_DATE = TO_DATE('".$date."', 'YYYY-MM-DD') and SOLD_TO = '$distr'
                    GROUP BY CREATE_DATE";
            $parse2 = oci_parse($conn, $q2);
            oci_execute($parse2);
            $arr2 = oci_fetch_assoc($parse2);
            $dataSPJ[] .= ($arr2["TOTAL"] != "") ? (int)$arr2["TOTAL"] : (int)0;

          }
//          echo "query".$q2;

        $datasets = array(
          array(
            "label" => "Jumlah SPJ",
            "backgroundColor" => "#F44336",
            "borderColor" => "#F44336",
            "data" => $dataSPJ,
            "fill" => false,
          )
        );

        $grap = array("labels" => $label, "datasets" => $datasets);

        $tglAwal1 = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
        $tglAkhir1 = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
        // all data
        $q3 = "SELECT COUNT(NO_SHP_TRN) AS TOTAL FROM EX_TRANS_HDR
        WHERE CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD') AND STATUS2 != 'DRAFT' AND ORG = '".$company."'".$filter1."";
        $parse3 = oci_parse($conn, $q3);
        oci_execute($parse3);
        $arr3 = oci_fetch_assoc($parse3);
        $allData = $arr3["TOTAL"];

        //counter presentase
        $q4 = "SELECT SUM(TOTAL_DRAFT_OPEN) AS TOTAL_DRAFT_OPEN, SUM(TOTAL_OPEN_OPEN) AS TOTAL_OPEN_OPEN, SUM(TOTAL_PROGRESS_OPEN) AS TOTAL_PROGRESS_OPEN, SUM(TOTAL_INVOICED_OPEN) AS TOTAL_INVOICED_OPEN FROM (
                	SELECT CREATE_DATE, COUNT (NO_SHP_TRN) AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'DRAFT' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, COUNT (NO_SHP_TRN) AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'OPEN' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_PROGRESS_OPEN, 0 AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'PROGRESS' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                UNION
                SELECT CREATE_DATE, 0 AS TOTAL_DRAFT_OPEN, 0 AS TOTAL_OPEN_OPEN, 0 AS TOTAL_PROGRESS_OPEN, COUNT(NO_SHP_TRN) AS TOTAL_INVOICED_OPEN FROM EX_TRANS_HDR WHERE STATUS = 'INVOICED' AND STATUS2 = 'OPEN' AND CREATE_DATE IS NOT NULL AND ORG = '".$company."'".$filter1." GROUP BY CREATE_DATE
                ) A
                WHERE CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD')";
        $parse4 = oci_parse($conn, $q4);
        oci_execute($parse4);
        $arr4 = oci_fetch_assoc($parse4);
        error_reporting(0);
        $presentanse["presentase_draft_open"] = ($arr4["TOTAL_DRAFT_OPEN"]/$allData)*100;
        $presentanse["presentase_open_open"] = ($arr4["TOTAL_OPEN_OPEN"]/$allData)*100;
        $presentanse["presentase_progress_open"] = ($arr4["TOTAL_PROGRESS_OPEN"]/$allData)*100;
        $presentanse["presentase_invoiced_open"] = ($arr4["TOTAL_INVOICED_OPEN"]/$allData)*100;

        $qty["TOTAL_DRAFT_OPEN"] = (empty($arr4["TOTAL_DRAFT_OPEN"])) ? 0 : $arr4["TOTAL_DRAFT_OPEN"];
        $qty["TOTAL_OPEN_OPEN"] = (empty($arr4["TOTAL_OPEN_OPEN"]) ? 0 : $arr4["TOTAL_OPEN_OPEN"]);
        $qty["TOTAL_PROGRESS_OPEN"] = (empty($arr4["TOTAL_PROGRESS_OPEN"]) ? 0 : $arr4["TOTAL_PROGRESS_OPEN"]);
        $qty["TOTAL_INVOICED_OPEN"] = (empty($arr4["TOTAL_INVOICED_OPEN"]) ? 0 : $arr4["TOTAL_INVOICED_OPEN"]);

        //select last ten
        $baseQuery = "SELECT * FROM(
              	SELECT CREATE_DATE, NO_SHP_TRN, NAMA_VENDOR, TANGGAL_KIRIM, STATUS, STATUS2 FROM EX_TRANS_HDR WHERE ORG = '".$company."'".$filter1." ORDER BY TANGGAL_KIRIM DESC
              ) WHERE ROWNUM <=10 AND CREATE_DATE is not null
              AND CREATE_DATE BETWEEN TO_DATE('".$tglAwal1."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir1."', 'YYYY-MM-DD')  ";

        //draftopen;
        $q5 = $baseQuery." AND STATUS = 'DRAFT' AND STATUS2 = 'OPEN' ";
        $parse5 = oci_parse($conn, $q5);
        oci_execute($parse5);
        $dataDo = array();
        while($arr = oci_fetch_assoc($parse5)){
          $dataDo[] = $arr;
        }

        // openopen
        $q6 = $baseQuery." AND STATUS = 'OPEN' AND STATUS2 = 'OPEN' ";
        $parse6 = oci_parse($conn, $q6);
        oci_execute($parse6);
        $dataOo = array();
        while($arr5 = oci_fetch_assoc($parse6)){
          $dataOo[] = $arr5;
        }

        // PROGRESOPEN
        $q7 = $baseQuery." AND STATUS = 'PROGRESS' AND STATUS2 = 'OPEN' ";
        $parse7 = oci_parse($conn, $q7);
        oci_execute($parse7);
        $dataPo = array();
        while($arr6 = oci_fetch_assoc($parse7)){
          $dataPo[] = $arr6;
        }

        // INVOICEDOPEN
        $q8 = $baseQuery." AND STATUS = 'INVOICED' AND STATUS2 = 'OPEN' ";
        $parse8 = oci_parse($conn, $q8);
        oci_execute($parse8);
        $dataIo = array();
        while($arr7 = oci_fetch_assoc($parse8)){
          $dataIo[] = $arr7;
        }

        echo json_encode(array(
          "grap"=>$grap,
          "presentase"=>$presentanse,
          "qty" => $qty,
          "DataDo"=>$dataDo,
          "DataOo"=>$dataOo,
          "DataPo"=>$dataPo,
          "DataIo"=>$dataIo,
          "totalData"=>$allData,
        ));

      break;

      case 'doghnut-stat-inv':
        // GRAFIK DONAT TARGET HARIAN
        $statInvDate1 = (!empty($_GET["statInvDate1"]) ? $_GET["statInvDate1"] : "");
        $statInvDate2 = (!empty($_GET["statInvDate2"]) ? $_GET["statInvDate2"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $vendor = (!empty($_GET["vendor"]) ? " AND NO_VENDOR = '".$_GET["vendor"]."' " : "");

        $q1 = "SELECT tb6.org, TB6.tipe, sum(tb6.target) as target, sum(tb6.real) as real FROM(
		SELECT tbu.*, TO_CHAR (tbu.TANGGAL_TARGET,'DD-MM-YYYY') AS TANGGAL_TARGETF,tbchild. REAL FROM(
		SELECT ORG,TIPE,DISTRIK,TANGGAL_TARGET,PLANTSET,SUM (TARGET) AS TARGET,STATUS FROM(
		SELECT tb1.ORG,tb1.TIPE,tb1.DISTRIK,tb1.TANGGAL_TARGET,CASE WHEN PLANT IS NULL THEN(SELECT PLANT FROM ZSD_MAPPING_PLANTKOTA WHERE DELETE_MARK = 0
		AND DISTRIK = tb1.DISTRIK AND ORG = tb1.ORG AND ROWNUM = 1) ELSE PLANT END AS PLANTSET,tb1.TARGET,STATUS FROM ZSD_TARGET_HARIAN_NEW tb1
		LEFT JOIN ZSD_TARGET_HARIAN_DET bb ON tb1. ID = bb.ID_HDR AND STATUS = 1 WHERE AKTIF_MARK = 0 AND ORG IN ('$company') AND TIPE = '121-301' AND DISTRIBUTOR = '$distr'
		AND TO_CHAR (TANGGAL_TARGET, 'YYYY-MM-DD') BETWEEN '$statInvDate1' AND '$statInvDate2') WHERE ORG IN ('$company') AND TARGET > 0
                GROUP BY ORG,TIPE,DISTRIK,TANGGAL_TARGET,PLANTSET,STATUS) tbu
		LEFT JOIN (SELECT PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP,SUM (QTY) AS REAL FROM( SELECT tb3.*, tb2.*, CASE WHEN KODE_PRODUK IN ('121-301-0110','121-301-0050',
		'121-301-0240','121-301-0180') THEN QTY_PP * 40 / 1000 WHEN KODE_PRODUK IN ('121-301-0020','121-301-0060','121-301-0056') THEN QTY_PP * 50 / 1000 ELSE QTY_PP
		END AS QTY FROM (SELECT NO_PP,PLANT_ASAL FROM OR_TRANS_HDR WHERE DELETE_MARK = 0 AND ORG IN ('2000', '5000', '7000') AND PLANT_ASAL IS NOT NULL AND NO_SO_OLD IS NULL
                AND SOLD_TO = '$distr'
		AND (TIPEPP <> 'NEW PROYEK' OR TIPEPP IS NULL OR (TIPEPP = 'NEW PROYEK' AND SO_TYPE = 'ZPR' AND STATUS = 'APPROVE')) AND (FLAG_LELANG IS NULL OR (
		FLAG_LELANG IS NOT NULL AND STATUS = 'APPROVE'))) tb3 INNER JOIN (SELECT NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM (QTY_PP) AS QTY_PP FROM(
		SELECT NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,CASE WHEN QTY_APPROVE IS NOT NULL THEN QTY_APPROVE ELSE QTY_PP END AS QTY_PP FROM OR_TRANS_DTL WHERE
		DELETE_MARK = 0 AND STATUS_LINE <> 'REJECTED' AND TO_CHAR (TGL_KIRIM_PP, 'YYYY-MM-DD') BETWEEN '$statInvDate1' AND '$statInvDate2' AND KODE_PRODUK LIKE '121-301%')
		GROUP BY NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP) tb2 ON (tb3.NO_PP = tb2.NO_PP)) tb3
		GROUP BY PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP) tbchild ON (
		tbu.DISTRIK = tbchild.KODE_TUJUAN AND tbu.PLANTSET = tbchild.PLANT_ASAL AND tbu.TANGGAL_TARGET = tbchild.TGL_KIRIM_PP)) tb6
                GROUP BY TB6.ORG,TB6.TIPE";
        $parse9 = oci_parse($conn, $q1);
        oci_execute($parse9);
        $arr8 = oci_fetch_assoc($parse9);
        $data = array(
          "datasets" => array(
            array(
              "data"=> array(intval($arr8["REAL"]),intval($arr8["TARGET"]-$arr8["REAL"])),
              "backgroundColor" => array("#009688", "#F44336"),
              "label" => "Status Target Distributor"
            )
          ),
          "labels" => array("TERBENTUK SO", "SISA TARGET")
        );

        // DATADETAIL
        $dataDet = array();
        $q2 = "SELECT * FROM(
                	SELECT NO_INVOICE, NAMA_VENDOR, TGL_TERIMA FROM KPI_TERIMA_INV_VENDOR  WHERE DATE_PRINTTERM IS NULL
                	AND TRUNC(TGL_TERIMA) BETWEEN TO_DATE('".$statInvDate1."', 'YYYY-MM-DD') AND TO_DATE('".$statInvDate2."', 'YYYY-MM-DD')
                  ".$vendor."
                  AND ORG = '".$company."'".$filter2."
                  ORDER BY TGL_TERIMA ASC, NO_INVOICE ASC
                ) WHERE ROWNUM <=10";
        $parse10 = oci_parse($conn, $q2);
        oci_execute($parse10);
        while($arr9 = oci_fetch_assoc($parse10)){
          $dataDet[] = $arr9;
        }
        echo json_encode(array("grap" => $data, "dataDetail"=>$dataDet));
      break;

      case 'doghnut-stat-tag':
        // GRAFIK DONAT TARGET BULANAN
        $statTagDate1 = (!empty($_GET["statTagDate1"]) ? $_GET["statTagDate1"] : "");
        $statTagDate2 = (!empty($_GET["statTagDate2"]) ? $_GET["statTagDate2"] : "");
        $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
        $q1 = "SELECT tb6.org, TB6.tipe, sum(tb6.target) as target, sum(tb6.real) as real FROM(
		SELECT tbu.*, TO_CHAR (tbu.TANGGAL_TARGET,'DD-MM-YYYY') AS TANGGAL_TARGETF,tbchild. REAL FROM(
		SELECT ORG,TIPE,DISTRIK,TANGGAL_TARGET,PLANTSET,SUM (TARGET) AS TARGET,STATUS FROM(
		SELECT tb1.ORG,tb1.TIPE,tb1.DISTRIK,tb1.TANGGAL_TARGET,CASE WHEN PLANT IS NULL THEN(SELECT PLANT FROM ZSD_MAPPING_PLANTKOTA WHERE DELETE_MARK = 0
		AND DISTRIK = tb1.DISTRIK AND ORG = tb1.ORG AND ROWNUM = 1) ELSE PLANT END AS PLANTSET,tb1.TARGET,STATUS FROM ZSD_TARGET_HARIAN_NEW tb1
		LEFT JOIN ZSD_TARGET_HARIAN_DET bb ON tb1. ID = bb.ID_HDR AND STATUS = 1 WHERE AKTIF_MARK = 0 AND ORG IN ('$company') AND TIPE = '121-301' AND DISTRIBUTOR = '$distr'
		AND TO_CHAR (TANGGAL_TARGET, 'YYYY-MM-DD') BETWEEN '$statTagDate1' AND '$statTagDate2') WHERE ORG IN ('$company') AND TARGET > 0
                GROUP BY ORG,TIPE,DISTRIK,TANGGAL_TARGET,PLANTSET,STATUS) tbu
		LEFT JOIN (SELECT PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP,SUM (QTY) AS REAL FROM( SELECT tb3.*, tb2.*, CASE WHEN KODE_PRODUK IN ('121-301-0110','121-301-0050',
		'121-301-0240','121-301-0180') THEN QTY_PP * 40 / 1000 WHEN KODE_PRODUK IN ('121-301-0020','121-301-0060','121-301-0056') THEN QTY_PP * 50 / 1000 ELSE QTY_PP
		END AS QTY FROM (SELECT NO_PP,PLANT_ASAL FROM OR_TRANS_HDR WHERE DELETE_MARK = 0 AND ORG IN ('2000', '5000', '7000') AND PLANT_ASAL IS NOT NULL AND NO_SO_OLD IS NULL
                AND SOLD_TO = '$distr'
		AND (TIPEPP <> 'NEW PROYEK' OR TIPEPP IS NULL OR (TIPEPP = 'NEW PROYEK' AND SO_TYPE = 'ZPR' AND STATUS = 'APPROVE')) AND (FLAG_LELANG IS NULL OR (
		FLAG_LELANG IS NOT NULL AND STATUS = 'APPROVE'))) tb3 INNER JOIN (SELECT NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,SUM (QTY_PP) AS QTY_PP FROM(
		SELECT NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP,CASE WHEN QTY_APPROVE IS NOT NULL THEN QTY_APPROVE ELSE QTY_PP END AS QTY_PP FROM OR_TRANS_DTL WHERE
		DELETE_MARK = 0 AND STATUS_LINE <> 'REJECTED' AND TO_CHAR (TGL_KIRIM_PP, 'YYYY-MM-DD') BETWEEN '$statTagDate1' AND '$statTagDate2' AND KODE_PRODUK LIKE '121-301%')
		GROUP BY NO_PP,KODE_PRODUK,KODE_TUJUAN,TGL_KIRIM_PP) tb2 ON (tb3.NO_PP = tb2.NO_PP)) tb3
		GROUP BY PLANT_ASAL,KODE_TUJUAN,TGL_KIRIM_PP) tbchild ON (
		tbu.DISTRIK = tbchild.KODE_TUJUAN AND tbu.PLANTSET = tbchild.PLANT_ASAL AND tbu.TANGGAL_TARGET = tbchild.TGL_KIRIM_PP)) tb6
                GROUP BY TB6.ORG,TB6.TIPE";
                
        $parse11 = oci_parse($conn, $q1);
        oci_execute($parse11);
        $arr10 = oci_fetch_assoc($parse11);
        $data = array(
          "datasets" => array(
            array(
              "data"=> array(intval($arr10["REAL"]),intval($arr10["TARGET"]-$arr10["REAL"])),
              "backgroundColor" => array("#009688", "#F44336"),
              "label" => "Status Target Distributor"
            )
          ),
          "labels" => array("TERBENTUK SO", "SISA TARGET")
        );
        // DATA DETAIL
        $q2 = "SELECT * FROM (
              	SELECT
              	I.NO_INVOICE, I.NAMA_VENDOR, I.TGL_INVOICE
              	FROM
              		EX_INVOICE I
              	LEFT JOIN KPI_TERIMA_INV_VENDOR V ON V.NO_INVOICE = I.NO_INVOICE
              	WHERE
              		I.DELETE_MARK = 0
              	AND V.NO_INVOICE IS NULL
              	AND TRUNC(TGL_INVOICE) BETWEEN TO_DATE ('".$statTagDate1."', 'YYYY-MM-DD')
              	AND TO_DATE ('".$statTagDate2."', 'YYYY-MM-DD')
                AND I.ORG = '".$company."'".$filter."
              	ORDER BY TGL_INVOICE ASC, NO_INVOICE ASC
              ) WHERE ROWNUM <= 10";
        $parse12 = oci_parse($conn, $q2);
        oci_execute($parse12);
        $dataDet = array();
        while($arr10 = oci_fetch_assoc($parse12)){
          $dataDet[] = $arr10;
        }
        echo json_encode(array("grap"=>$data, "tenDetail"=>$dataDet));
      break;

      
      
      default:
        echo json_encode(array("success" => false));
    }
  }

 ?>
