<?

$or_username = "DEVSD";
$or_password = "gresik45";
$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = dev-sggdata3.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))';				
$conn = oci_connect($or_username, $or_password, $or_db);

function getNum($nilai){
	if($nilai=='') $nilai='0';
	return $nilai;
}

if($_POST["tampil"]){
echo"data di tampilkan";

$today = date("m/d/Y"); 
$sg='2000';
$realsg = $_POST["realsg"];
$rkapsg = $_POST["rkapsg"];
$psg = $_POST["psg"];
$sp='3000';
$realsp = $_POST["realsp"];
$rkapsp = $_POST["rkapsp"];
$psp = $_POST["psp"];
$st='4000';
$realst = $_POST["realst"];
$rkapst = $_POST["rkapst"];
$pst = $_POST["pst"];
$sgg='1000';
$realsgg = $_POST["realsgg"];
$rkapsgg = $_POST["rkapsgg"];
$psgg = $_POST["psgg"];
$total_sgg = $_POST["total_sgg"];
$pallsgg = $_POST["pallsgg"];
};

if($_POST["simpan"]){

$today = date("m/d/Y"); 
 
$sg='2000';
$realsg = $_POST["realsg"];
$rkapsg = $_POST["rkapsg"];
$psg = $_POST["psg"];
$sp='3000';
$realsp = $_POST["realsp"];
$rkapsp = $_POST["rkapsp"];
$psp = $_POST["psp"];
$st='4000';
$realst = $_POST["realst"];
$rkapst = $_POST["rkapst"];
$pst = $_POST["pst"];
$sgg='1000';
$realsgg = $_POST["realsgg"];
$rkapsgg = $_POST["rkapsgg"];
$psgg = $_POST["psgg"];
$total_sgg = $_POST["total_sgg"];
$pallsgg = $_POST["pallsgg"];


foreach($realsg as $key => $isi){
//	echo "<br><br>";
//	echo $key.'-'.$isi;
//	echo " | ".$rkapsg[$key];

$draf1=getNum($draf[$key]);
$nm_draf1=getNum($nm_draf[$key]);
$type1=getNum($type[$key]);
$field1=getNum($field[$key]);
$sg1=getNum($sg='2000');
$realsg1=getNum($realsg[$key]);
$rkapsg1=getNum($rkapsg[$key]);
$psg1=getNum($psg[$key]);
$sp1=getNum($sp='3000');
$realsp1=getNum($realsp[$key]);
$rkapsp1=getNum($rkapsp[$key]);
$psp1=getNum($psp [$key]);
$st1=getNum($st='4000');
$realst1=getNum($realst[$key]);
$rkapst1=getNum($rkapst[$key]);
$pst1=getNum($pst [$key]);
$sgg1=getNum($sgg='1000');
$realsgg1=getNum($realsgg[$key]);
$rkapsgg1=getNum($rkapsgg[$key]);
$psgg1=getNum($psgg[$key]);
$total_sgg1=getNum($total_sgg[$key]);
$pallsgg1=getNum($pallsgg[$key]);


           $q_stock= "INSERT INTO SGG_SALES_REVIEW (TGL_CREATE,DRAF,NM_DRAF,TYPE_LAPORAN,NM_LAPORAN,SG,REAL,RKAP,PROSENTASE,SP,REAL_SP,RKAP_SP,PROSENTASE_SP,ST,REAL_ST,RKAP_ST,
           PROSENTASE_ST,SGG,REAL_SGG,RKAP_SGG,PROSENTASE_SGG,TAHUN,PROSENTASE_TH)           VALUES(TO_DATE('".$today."','dd/mm/YYYY'),$draf1,'$nm_draf1','$type1','$field1',$sg1,$realsg1,$rkapsg1,$psg1,$sp1,$realsp1,$rkapsp1,$psp1,$st1,$realst1,$rkapst1,$pst1,$sgg1,$realsgg1,$rkapsgg1,$psgg1,$total_sgg1,$pallsgg1)";
		$query = oci_parse($conn,$q_stock);
		$status = oci_execute($query);
		          oci_commit($conn);
       
}
}
   if($status){
		echo" data sukses disimpan";
		}
?>

<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=ProgId content=Excel.Sheet>
<meta name=Generator content="Microsoft Excel 12">
<link rel=File-List href="Page_files/filelist.xml">
</head>

<body>
<!--[if !excel]>&nbsp;&nbsp;<![endif]-->
<!--The following information was generated by Microsoft Office Excel's Publish
as Web Page wizard.-->
<!--If the same item is republished from Excel, all information between the DIV
tags will be replaced.-->
<!----------------------------->
<!--START OF OUTPUT FROM EXCEL PUBLISH AS WEB PAGE WIZARD -->
<!----------------------------->
<FORM METHOD=POST ACTION="index1.php">


<div id="Sales Review 2012_1480" align=center x:publishsource="Excel">

<table border=1 cellpadding=0 cellspacing=0 width=1300  >
 
  <td height=26 class=xl2291480 width=100 style='height:20.1pt;width:20pt'></td>
  <td class=xl2291480 width=110 style='width:83pt'></td>
  <td colspan=12 class=xl2721480 width=779 align=center>SALES REVIEW FEB
  2011</td>
  <td class=xl2721480 width=77 style='width:58pt'></td>
  <td class=xl2721480 width=44 style='width:33pt'></td>
 </tr>
 <tr height=26>
  <td colspan=2 rowspan=2 height=52 class=xl2751480 width=136 >&nbsp;</td>
  <td colspan=3 align='center'>SG</td>
  <td colspan=3 align='center'>SP</td>
  <td colspan=3 align='center'>ST</td>
  <td colspan=5 align='center'>SGG</td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 align='center'>Real</td>
  <td class=xl2651480 align='center''>RKAP</td>
  <td class=xl2661480 align='center'>%</td>
  <td class=xl2641480 align='center'>Real</td>
  <td class=xl2651480 align='center'>RKAP</td>
  <td class=xl2661480 align='center'>%</td>
  <td class=xl2641480 align='center'>Real</td>
  <td class=xl2651480 align='center'>RKAP</td>
  <td class=xl2661480 align='center'>%</td>
  <td class=xl2641480 align='center'>Real</td>
  <td class=xl2651480 align='center'>RKAP</td>
  <td class=xl2661480 align='center'>%</td>
  <td class=xl2731480 align='center'><? echo $heder = date("Y");;?></td>
  <td class=xl2661480 align='center'>%</td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td rowspan=4 height=104 class=xl2931480 width=100 style=''>Domestic</td>
  <td class=xl2301480 style=''>Vol (ton)
  <input type="hidden" name="draf[1]" size="8" value="1"/>
  <input type="hidden" name="nm_draf[1]" size="8" value="Domestic"/>
  <input type="hidden" name="type[1]" size="8" value="V"/>
  <input type="hidden" name="field[1]" size="8" value="Vol (ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[1]" size="8" value="<?=$realsg[1];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[1]" size="8" value="<?=$rkapsg[1];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[1] = $realsg[1] /$rkapsg[1] * 100 ;?><input type="hidden" name="psg[1]" size="2" value="<?=$psg[1];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[1]" size="8" value="<?=$realsp[1];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[1]" size="8" value="<?=$rkapsp[1];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[1] = $realsp[1] /$rkapsp[1] * 100 ;?><input type="hidden" name="psp[1]" size="2" value="<?=$psp[1];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[1]" size="8" value="<?=$realst[1];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[1]" size="8" value="<?=$rkapst[1];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[1] = $realst[1] /$rkapst[1] * 100 ;?><input type="hidden" name="pst[1]" size="2" value="<?=$pst[1];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[1] = $realsg[1] + $realsp[1] + $realst[1];?><input type="hidden" name="realsgg[1]" size="8" value="<?=$realsgg[1];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[1] = $rkapsg[1] + $rkapsp[1] + $rkapst[1];?><input type="hidden" name="rkapsgg[1]" size="8" value="<?=$rkapsgg[1];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[1] = $realsgg[1] /$rkapsgg[1]  * 100 ;?><input type="hidden" name="psgg[1]" size="2" value="<?=$psgg[1];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[1]" size="8" value="<?=$total_sgg[1];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[1] = $realsgg[1] / $total_sgg[1] * 100 ;?><input type="hidden" name="pallsgg[1]" size="2" value="<?=$pallsgg[1];?>"/></td>
 
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2311480 style='height:20.1pt;border-top:none;
  border-left:none'>Price (Rp/ton) 
  <input type="hidden" name="draf[2]" size="8" value="1"/>
  <input type="hidden" name="nm_draf[2]" size="8" value="Domestic"/>
  <input type="hidden" name="type[2]" size="8" value="P"/>
  <input type="hidden" name="field[2]" size="8" value="Price (Rp/ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[2]" size="8" value="<?=$realsg[2];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[2]" size="8" value="<?=$rkapsg[2];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[2] = $realsg[2] /$rkapsg[2] * 100 ;?><input type="hidden" name="psg[2]" size="2" value="<?=$psg[2];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[2]" size="8" value="<?=$realsp[2];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[2]" size="8" value="<?=$rkapsp[2];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[2] = $realsp[2] /$rkapsp[2] * 100 ;?><input type="hidden" name="psp[2]" size="2" value="<?=$psp[2];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[2]" size="8" value="<?=$realst[2];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[2]" size="8" value="<?=$rkapst[2];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[2] = $realst[2] /$rkapst[2] * 100 ;?><input type="hidden" name="pst[2]" size="2" value="<?=$pst[2];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[2] = $realsg[2] + $realsp[2] + $realst[2];?><input type="hidden" name="realsgg[2]" size="8" value="<?=$realsgg[2];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[2] = $rkapsg[2] + $rkapsp[2] + $rkapst[2];?><input type="hidden" name="rkapsgg[2]" size="8" value="<?=$rkapsgg[2];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[2] = $realsgg[2] /$rkapsgg[2]  * 100 ;?><input type="hidden" name="psgg[2]" size="2" value="<?=$psgg[2];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[2]" size="8" value="<?=$total_sgg[2];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[2] = $realsgg[2] /$total_sgg[2] * 100 ;?><input type="hidden" name="pallsgg[2]" size="2" value="<?=$pallsgg[2];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2341480 style='height:20.1pt;border-left:none'>Revenue (RpM)
  <input type="hidden" name="draf[3]" size="8" value="1"/>
  <input type="hidden" name="nm_draf[3]" size="8" value="Domestic"/>
  <input type="hidden" name="type[3]" size="8" value="R"/>
  <input type="hidden" name="field[3]" size="8" value="Revenue (RpM)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[3]" size="8" value="<?=$realsg[3];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[3]" size="8" value="<?=$rkapsg[3];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[3] = $realsg[3] /$rkapsg[3] * 100 ;?><input type="hidden" name="psg[3]" size="2" value="<?=$psg[3];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[3]" size="8" value="<?=$realsp[3];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[3]" size="8" value="<?=$rkapsp[3];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[3] = $realsp[3] /$rkapsp[3] * 100 ;?><input type="hidden" name="psp[3]" size="2" value="<?=$psp[3];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[3]" size="8" value="<?=$realst[3];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[3]" size="8" value="<?=$rkapst[3];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[3] = $realst[3] /$rkapst[3] * 100 ;?><input type="hidden" name="pst[3]" size="2" value="<?=$pst[3];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[3] = $realsg[3] + $realsp[3] + $realst[3];?><input type="hidden" name="realsgg[3]" size="8" value="<?=$realsgg[3];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[3] = $rkapsg[3] + $rkapsp[3] + $rkapst[3];?><input type="hidden" name="rkapsgg[3]" size="8" value="<?=$rkapsgg[3];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[3] = $realsgg[3] /$rkapsgg[3]  * 100 ;?><input type="hidden" name="psgg[3]" size="2" value="<?=$psgg[3];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[3]" size="8" value="<?=$total_sgg[3];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[3] = $realsgg[3] /$total_sgg[3] * 100 ;?><input type="hidden" name="pallsgg[3]" size="2" value="<?=$pallsgg[3];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2631480 style='height:20.1pt;border-left:none'>Market Share (%)
  <input type="hidden" name="draf[4]" size="8" value="1"/>
  <input type="hidden" name="nm_draf[4]" size="8" value="Domestic"/>
  <input type="hidden" name="type[4]" size="8" value="M"/>
  <input type="hidden" name="field[4]" size="8" value="Market Share (%)"/></td>
 <td class=xl2491480 style='' align='center'><input type="text" name="realsg[4]" size="8" value="<?=$realsg[4];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[4]" size="8" value="<?=$rkapsg[4];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[4] = $realsg[4] /$rkapsg[4] * 100 ;?><input type="hidden" name="psg[4]" size="2" value="<?=$psg[4];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[4]" size="8" value="<?=$realsp[4];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[4]" size="8" value="<?=$rkapsp[4];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[4] = $realsp[4] /$rkapsp[4] * 100 ;?><input type="hidden" name="psp[4]" size="2" value="<?=$psp[4];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[4]" size="8" value="<?=$realst[4];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[4]" size="8" value="<?=$rkapst[4];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[4] = $realst[4] /$rkapst[4] * 100 ;?><input type="hidden" name="pst[4]" size="2" value="<?=$pst[4];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[4] = $realsg[4] + $realsp[4] + $realst[4];?><input type="hidden" name="realsgg[4]" size="8" value="<?=$realsgg[4];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[4] = $rkapsg[4] + $rkapsp[4] + $rkapst[4];?><input type="hidden" name="rkapsgg[4]" size="8" value="<?=$rkapsgg[4];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[4] = $realsgg[4] /$rkapsgg[4]  * 100 ;?><input type="hidden" name="psgg[4]" size="2" value="<?=$psgg[4];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[4]" size="8" value="<?=$total_sgg[4];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[4] = $realsgg[4] /$total_sgg[4] * 100 ;?><input type="hidden" name="pallsgg[4]" size="2" value="<?=$pallsgg[4];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td rowspan=3 height=78 class=xl2811480 width=100 style='border-bottom:1.0pt solid black;
  height:60.3pt;border-top:none;width:20pt'>Intercoy</td>
    <td class=xl2411480 style=''>Vol (ton)
  <input type="hidden" name="draf[5]" size="8" value="2"/>
  <input type="hidden" name="nm_draf[5]" size="8" value="Intercoy"/>
  <input type="hidden" name="type[5]" size="8" value="V"/>
  <input type="hidden" name="field[5]" size="8" value="Vol (ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[5]" size="8" value="<?=$realsg[5];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[5]" size="8" value="<?=$rkapsg[5];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[5] = $realsg[5] /$rkapsg[5] * 100 ;?><input type="hidden" name="psg[5]" size="2" value="<?=$psg[5];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[5]" size="8" value="<?=$realsp[5];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[5]" size="8" value="<?=$rkapsp[5];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[5] = $realsp[5] /$rkapsp[5] * 100 ;?><input type="hidden" name="psp[5]" size="2" value="<?=$psp[5];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[5]" size="8" value="<?=$realst[5];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[5]" size="8" value="<?=$rkapst[5];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[5] = $realst[5] /$rkapst[5] * 100 ;?><input type="hidden" name="pst[5]" size="2" value="<?=$pst[5];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[5] = $realsg[5] + $realsp[5] + $realst[5];?><input type="hidden" name="realsgg[5]" size="8" value="<?=$realsgg[5];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[5] = $rkapsg[5] + $rkapsp[5] + $rkapst[5];?><input type="hidden" name="rkapsgg[5]" size="8" value="<?=$rkapsgg[5];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[5] = $realsgg[5] /$rkapsgg[5]  * 100 ;?><input type="hidden" name="psgg[5]" size="2" value="<?=$psgg[5];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[5]" size="8" value="<?=$total_sgg[5];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[5] = $realsgg[5] /$total_sgg[5] * 100 ;?><input type="hidden" name="pallsgg[5]" size="2" value="<?=$pallsgg[5];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2311480 style='height:20.1pt;border-top:none;
  border-left:none'>Price (Rp/ton)
  <input type="hidden" name="draf[6]" size="8" value="2"/>
  <input type="hidden" name="nm_draf[6]" size="8" value="Intercoy"/>
  <input type="hidden" name="type[6]" size="8" value="P"/>
  <input type="hidden" name="field[6]" size="8" value="Price (Rp/ton)"/></td>
   <td class=xl2491480 style='' align='center'><input type="text" name="realsg[6]" size="8" value="<?=$realsg[6];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[6]" size="8" value="<?=$rkapsg[6];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[6] = $realsg[6] /$rkapsg[6] * 100 ;?><input type="hidden" name="psg[6]" size="2" value="<?=$psg[6];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[6]" size="8" value="<?=$realsp[6];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[6]" size="8" value="<?=$rkapsp[6];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[6] = $realsp[6] /$rkapsp[6] * 100 ;?><input type="hidden" name="psp[6]" size="2" value="<?=$psp[6];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[6]" size="8" value="<?=$realst[6];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[6]" size="8" value="<?=$rkapst[6];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[6] = $realst[6] /$rkapst[6] * 100 ;?><input type="hidden" name="pst[6]" size="2" value="<?=$pst[6];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[6] = $realsg[6] + $realsp[6] + $realst[6];?><input type="hidden" name="realsgg[6]" size="8" value="<?=$realsgg[6];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[6] = $rkapsg[6] + $rkapsp[6] + $rkapst[6];?><input type="hidden" name="rkapsgg[6]" size="8" value="<?=$rkapsgg[6];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[6] = $realsgg[6] /$rkapsgg[6]  * 100 ;?><input type="hidden" name="psgg[6]" size="2" value="<?=$psgg[6];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[6]" size="8" value="<?=$total_sgg[6];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[6] = $realsgg[6] /$total_sgg[6] * 100 ;?><input type="hidden" name="pallsgg[6]" size="2" value="<?=$pallsgg[6];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2341480 style='height:20.1pt;border-left:none'>Revenue (RpM)
  <input type="hidden" name="draf[7]" size="8" value="2"/>
  <input type="hidden" name="nm_draf[7]" size="8" value="Intercoy"/>
  <input type="hidden" name="type[7]" size="8" value="R"/>
  <input type="hidden" name="field[7]" size="8" value="Revenue (RpM)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[7]" size="8" value="<?=$realsg[7];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[7]" size="8" value="<?=$rkapsg[7];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[7] = $realsg[7] /$rkapsg[7] * 100 ;?><input type="hidden" name="psg[7]" size="2" value="<?=$psg[7];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[7]" size="8" value="<?=$realsp[7];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[7]" size="8" value="<?=$rkapsp[7];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[7] = $realsp[7] /$rkapsp[7] * 100 ;?><input type="hidden" name="psp[7]" size="2" value="<?=$psp[7];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[7]" size="8" value="<?=$realst[7];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[7]" size="8" value="<?=$rkapst[7];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[7] = $realst[7] /$rkapst[7] * 100 ;?><input type="hidden" name="pst[7]" size="2" value="<?=$pst[7];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[7] = $realsg[7] + $realsp[7] + $realst[7];?><input type="hidden" name="realsgg[7]" size="8" value="<?=$realsgg[7];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[7] = $rkapsg[7] + $rkapsp[7] + $rkapst[7];?><input type="hidden" name="rkapsgg[7]" size="8" value="<?=$rkapsgg[7];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[7] = $realsgg[7] /$rkapsgg[7]  * 100 ;?><input type="hidden" name="psgg[7]" size="2" value="<?=$psgg[7];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[7]" size="8" value="<?=$total_sgg[7];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[7] = $realsgg[7] /$total_sgg[7] * 100 ;?><input type="hidden" name="pallsgg[7]" size="2" value="<?=$pallsgg[7];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td rowspan=3 height=78 class=xl2811480 width=100 style='border-bottom:1.0pt solid black;
  height:60.3pt;border-top:none;width:20pt'>Export</td>
  <td class=xl2371480 style='border-left:none'>Vol (ton)
  <input type="hidden" name="draf[8]" size="8" value="3"/>
  <input type="hidden" name="nm_draf[8]" size="8" value="Export"/>
  <input type="hidden" name="type[8]" size="8" value="V"/>
  <input type="hidden" name="field[8]" size="8" value="Vol (ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[8]" size="8" value="<?=$realsg[8];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[8]" size="8" value="<?=$rkapsg[8];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[8] = $realsg[8] /$rkapsg[8] * 100 ;?><input type="hidden" name="psg[8]" size="2" value="<?=$psg[8];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[8]" size="8" value="<?=$realsp[8];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[8]" size="8" value="<?=$rkapsp[8];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[8] = $realsp[8] /$rkapsp[8] * 100 ;?><input type="hidden" name="psp[8]" size="2" value="<?=$psp[8];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[8]" size="8" value="<?=$realst[8];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[8]" size="8" value="<?=$rkapst[8];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[8] = $realst[8] /$rkapst[8] * 100 ;?><input type="hidden" name="pst[8]" size="2" value="<?=$pst[8];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[8] = $realsg[8] + $realsp[8] + $realst[8];?><input type="hidden" name="realsgg[8]" size="8" value="<?=$realsgg[8];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[8] = $rkapsg[8] + $rkapsp[8] + $rkapst[8];?><input type="hidden" name="rkapsgg[8]" size="8" value="<?=$rkapsgg[8];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[8] = $realsgg[8] /$rkapsgg[8]  * 100 ;?><input type="hidden" name="psgg[8]" size="2" value="<?=$psgg[8];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[8]" size="8" value="<?=$total_sgg[8];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[8] = $realsgg[8] /$total_sgg[8] * 100 ;?><input type="hidden" name="pallsgg[8]" size="2" value="<?=$pallsgg[8];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2311480 style='height:20.1pt;border-top:none;
  border-left:none'>Price (Rp/ton)
  <input type="hidden" name="draf[9]" size="8" value="3"/>
  <input type="hidden" name="nm_draf[9]" size="8" value="Export"/>
  <input type="hidden" name="type[9]" size="8" value="P"/>
  <input type="hidden" name="field[9]" size="8" value="Price (Rp/ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[9]" size="8" value="<?=$realsg[9];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[9]" size="8" value="<?=$rkapsg[9];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[9] = $realsg[9] /$rkapsg[9] * 100 ;?><input type="hidden" name="psg[9]" size="2" value="<?=$psg[9];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[9]" size="8" value="<?=$realsp[9];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[9]" size="8" value="<?=$rkapsp[9];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[9] = $realsp[9] /$rkapsp[9] * 100 ;?><input type="hidden" name="psp[9]" size="2" value="<?=$psp[9];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[9]" size="8" value="<?=$realst[9];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[9]" size="8" value="<?=$rkapst[9];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[9] = $realst[9] /$rkapst[9] * 100 ;?><input type="hidden" name="pst[9]" size="2" value="<?=$pst[9];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[9] = $realsg[9] + $realsp[9] + $realst[9];?><input type="hidden" name="realsgg[9]" size="8" value="<?=$realsgg[9];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[9] = $rkapsg[9] + $rkapsp[9] + $rkapst[9];?><input type="hidden" name="rkapsgg[9]" size="8" value="<?=$rkapsgg[9];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[9] = $realsgg[9] /$rkapsgg[9]  * 100 ;?><input type="hidden" name="psgg[9]" size="2" value="<?=$psgg[9];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[9]" size="8" value="<?=$total_sgg[9];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[9] = $realsgg[9] /$total_sgg[9] * 100 ;?><input type="hidden" name="pallsgg[9]" size="2" value="<?=$pallsgg[9];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td height=26 class=xl2341480 style='height:20.1pt;border-left:none'>Revenue (RpM)
  <input type="hidden" name="draf[10]" size="8" value="3"/>
  <input type="hidden" name="nm_draf[10]" size="8" value="Export"/>
  <input type="hidden" name="type[10]" size="8" value="R"/>
  <input type="hidden" name="field[10]" size="8" value="Revenue(RpM)"/></td>
 <td class=xl2491480 style='' align='center'><input type="text" name="realsg[10]" size="8" value="<?=$realsg[10];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[10]" size="8" value="<?=$rkapsg[10];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[10] = $realsg[10] /$rkapsg[10] * 100 ;?><input type="hidden" name="psg[10]" size="2" value="<?=$psg[10];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[10]" size="8" value="<?=$realsp[10];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[10]" size="8" value="<?=$rkapsp[10];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[10] = $realsp[10] /$rkapsp[10] * 100 ;?><input type="hidden" name="psp[10]" size="2" value="<?=$psp[10];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[10]" size="8" value="<?=$realst[10];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[10]" size="8" value="<?=$rkapst[10];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[10] = $realst[10] /$rkapst[10] * 100 ;?><input type="hidden" name="pst[10]" size="2" value="<?=$pst[10];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[10] = $realsg[10] + $realsp[10] + $realst[10];?><input type="hidden" name="realsgg[10]" size="8" value="<?=$realsgg[10];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[10] = $rkapsg[10] + $rkapsp[10] + $rkapst[10];?><input type="hidden" name="rkapsgg[10]" size="8" value="<?=$rkapsgg[10];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[10] = $realsgg[10] /$rkapsgg[10]  * 100 ;?><input type="hidden" name="psgg[10]" size="2" value="<?=$psgg[10];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[10]" size="8" value="<?=$total_sgg[10];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[10] = $realsgg[10] /$total_sgg[10] * 100 ;?><input type="hidden" name="pallsgg[10]" size="2" value="<?=$pallsgg[10];?>"/></td>
 </tr>
 <tr height=26 style='mso-height-source:userset;height:20.1pt'>
  <td rowspan=3 height=78 class=xl2821480 width=100 style='border-bottom:1.0pt solid black;
  height:60.3pt;border-top:none;width:50pt'>S</td>
  <td class=xl2411480 style=''>Vol (ton)
  <input type="hidden" name="draf[11]" size="8" value="4"/>
  <input type="hidden" name="nm_draf[11]" size="8" value="S"/>
  <input type="hidden" name="type[11]" size="8" value="V"/>
  <input type="hidden" name="field[11]" size="8" value="Vol (ton)"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsg[11]" size="8" value="<?=$realsg[11];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[11]" size="8" value="<?=$rkapsg[11];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[11] = $realsg[11] /$rkapsg[11] * 100 ;?><input type="hidden" name="psg[11]" size="2" value="<?=$psg[11];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[11]" size="8" value="<?=$realsp[11];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[11]" size="8" value="<?=$rkapsp[11];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[11] = $realsp[11] /$rkapsp[11] * 100 ;?><input type="hidden" name="psp[11]" size="2" value="<?=$psp[11];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[11]" size="8" value="<?=$realst[11];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[11]" size="8" value="<?=$rkapst[11];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[11] = $realst[11] /$rkapst[11] * 100 ;?><input type="hidden" name="pst[11]" size="2" value="<?=$pst[11];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[11] = $realsg[11] + $realsp[11] + $realst[11];?><input type="hidden" name="realsgg[11]" size="8" value="<?=$realsgg[11];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[11] = $rkapsg[11] + $rkapsp[11] + $rkapst[11];?><input type="hidden" name="rkapsgg[11]" size="8" value="<?=$rkapsgg[11];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[11] = $realsgg[11] /$rkapsgg[11]  * 100 ;?><input type="hidden" name="psgg[11]" size="2" value="<?=$psgg[11];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[11]" size="8" value="<?=$total_sgg[11];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[11] = $realsgg[11] /$total_sgg[11] * 100 ;?><input type="hidden" name="pallsgg[11]" size="2" value="<?=$pallsgg[11];?>"/></td>
 </tr>
 <tr height=26 >
  <td height=26 >Price (Rp/ton)
   <input type="hidden" name="draf[12]" size="8" value="4"/>
  <input type="hidden" name="nm_draf[12]" size="8" value="S"/>
  <input type="hidden" name="type[12]" size="8" value="P"/>
  <input type="hidden" name="field[12]" size="8" value="Price (Rp/ton)"/></td>
   <td class=xl2491480 style='' align='center'><input type="text" name="realsg[12]" size="8" value="<?=$realsg[12];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[12]" size="8" value="<?=$rkapsg[12];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psg[12] = $realsg[12] /$rkapsg[12] * 100 ;?><input type="hidden" name="psg[12]" size="2" value="<?=$psg[12];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[12]" size="8" value="<?=$realsp[12];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[12]" size="8" value="<?=$rkapsp[12];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[12] = $realsp[12] /$rkapsp[12] * 100 ;?><input type="hidden" name="psp[12]" size="2" value="<?=$psp[12];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[12]" size="8" value="<?=$realst[12];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[12]" size="8" value="<?=$rkapst[12];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[12] = $realst[12] /$rkapst[12] * 100 ;?><input type="hidden" name="pst[12]" size="2" value="<?=$pst[12];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[12] = $realsg[12] + $realsp[12] + $realst[12];?><input type="hidden" name="realsgg[12]" size="8" value="<?=$realsgg[12];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[12] = $rkapsg[12] + $rkapsp[12] + $rkapst[12];?><input type="hidden" name="rkapsgg[12]" size="8" value="<?=$rkapsgg[12];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[12] = $realsgg[12] /$rkapsgg[12]  * 100 ;?><input type="hidden" name="psgg[12]" size="2" value="<?=$psgg[12];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[12]" size="8" value="<?=$total_sgg[12];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[12] = $realsgg[12] /$total_sgg[12] * 100 ;?><input type="hidden" name="pallsgg[12]" size="2" value="<?=$pallsgg[12];?>"/></td>
 </tr>
 <tr height=26 style=''>
  <td height=26 class='' style=''>Revenue(RpM)
   <input type="hidden" name="draf[13]" size="8" value="4"/>
  <input type="hidden" name="nm_draf[13]" size="8" value="S"/>
  <input type="hidden" name="type[13]" size="8" value="R"/>
  <input type="hidden" name="field[13]" size="8" value="Revenue (RpM)"/></td>
 <td class=xl2491480 style='' align='center'><input type="text" name="realsg[13]" size="8" value="<?=$realsg[13];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsg[13]" size="8" value="<?=$rkapsg[13];?>"/></td>
  <td class=xl2531480 style='' align='center'><?number_format($psg[13]); ?><?=@$psg[13] = $realsg[13] /$rkapsg[13] * 100 ;?><input type="hidden" name="psg[13]" size="2" value="<?=$psg[13];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realsp[13]" size="8" value="<?=$realsp[13];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapsp[13]" size="8" value="<?=$rkapsp[13];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psp[13] = $realsp[13] /$rkapsp[13] * 100 ;?><input type="hidden" name="psp[13]" size="2" value="<?=$psp[13];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="realst[13]" size="8" value="<?=$realst[13];?>"/></td>
  <td class=xl2501480 style='' align='center'><input type="text" name="rkapst[13]" size="8" value="<?=$rkapst[13];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pst[13] = $realst[13] /$rkapst[13] * 100 ;?><input type="hidden" name="pst[13]" size="2" value="<?=$pst[13];?>"/></td>
  <td class=xl2491480 style='' align='center'><?=@$realsgg[13] = $realsg[13] + $realsp[13] + $realst[13];?><input type="hidden" name="realsgg[13]" size="8" value="<?=$realsgg[13];?>"/></td>
  <td class=xl2501480 style='' align='center'><?=@$rkapsgg[13] = $rkapsg[13] + $rkapsp[13] + $rkapst[13];?><input type="hidden" name="rkapsgg[13]" size="8" value="<?=$rkapsgg[13];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$psgg[13] = $realsgg[13] /$rkapsgg[13]  * 100 ;?><input type="hidden" name="psgg[13]" size="2" value="<?=$psgg[13];?>"/></td>
  <td class=xl2491480 style='' align='center'><input type="text" name="total_sgg[13]" size="8" value="<?=$total_sgg[13];?>"/></td>
  <td class=xl2531480 style='' align='center'><?=@$pallsgg[13] = $realsgg[13] /$total_sgg[13] * 100 ;?><input type="hidden" name="pallsgg[13]" size="2" value="<?=$pallsgg[13];?>"/></td>
 </tr>
</table>

</div>

<INPUT TYPE="submit" name="tampil" value="Tampil">&nbsp;<INPUT TYPE="submit" name="simpan" value="Simpan"></FORM>

<!----------------------------->
<!--END OF OUTPUT FROM EXCEL PUBLISH AS WEB PAGE WIZARD-->
<!----------------------------->
</body>

</html>
