<?php
session_start();
require_once 'api/smbr.php';
require_once ('../include/ex_fungsi.php');
require_once ('../security_helper.php');
sanitize_global_input();
$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$org=$_SESSION['user_org'];
$username = $_SESSION['user_name'];

if($org != '1000'){
    echo 'Maaf, menu ini bukan untuk company yang saat ini anda pilih';
    exit;
};

function showMessage($message)
{
?>
    <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
    <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
        <div class="alert alert-info" role="alert">
            <strong>Result!</strong>
            <br>
            <br>
            <div class="" role="alert"><?= $message ?></div>
            <br>
            <a href="pod_upload.php" style="margin-left: 16px;">&lt;&lt;&nbsp;&nbsp;Kembali&nbsp;&nbsp;&gt;&gt;</a>
        </div>
    </div>
<?php
}

if (isset($_POST['pod'])) {
    $error_msg = "";

    if($_FILES['pod1']['error'] != 0 || $_FILES['pod2']['error'] != 0 ){
        $error_msg = "Terjadi kesalahan waktu upload Evidence Pod";
    }

    $file_name_pod1 = $_FILES['pod1']['name'];
    $file_tmp_pod1 = $_FILES['pod1']['tmp_name'];
    $file_name_pod2 = $_FILES['pod2']['name'];
    $file_tmp_pod2 = $_FILES['pod2']['tmp_name'];
    $no_shipment         = $_POST['no_shipment'];
    $qty_ktg_rusak       = $_POST['qty_ktg_rusak'];
    $qty_semen_rusak     = $_POST['qty_semen_rusak'];
    $tanggal_datang      = $_POST['tanggal_datang'];
    $tanggal_bongkar     = $_POST['tanggal_bongkar'];
    $total_ktg_rusak     = $_POST['total_ktg_rusak'];
    $total_ktg_rezak     = $_POST['total_ktg_rezak'];
    $total_klaim_ktg     = $_POST['total_klaim_ktg'];
    $total_semen_rusak   = $_POST['total_semen_rusak'];
    $harga_tebus         = $_POST['harga_tebus'];
    $pdpks               = $_POST['pdpks'];
    $total_klaim_semen   = $_POST['total_klaim_semen'];
    $total_klaim_all     = $_POST['total_klaim_all'];
    $geofence_pod        = $_POST['geofence_pod'];
    $keterangan_pod        = $_POST['keterangan_pod'];

    $check_pod1 = validate_image($file_name_pod1, $file_tmp_pod1);
    if(!$check_pod1['status'] && !$error_msg){
        $error_msg = "Invalid Evidence POD 1: ".$check_pod1['message'];
    }

    $check_pod2 = validate_image($file_name_pod2, $file_tmp_pod2);
    if(!$check_pod2['status'] && !$error_msg){
        $error_msg = "Invalid Evidence POD 2: ".$check_pod2['message'];
    }

    $check_geofence = validate_geofence_pod($geofence_pod);
    if(!$check_geofence && !$error_msg){
        $error_msg = "Invalid Geofence POD";
    }

    if($error_msg){
        echo "<script>alert('" . $error_msg ."');</script>";
    }else{

        $ext = strtolower(substr(strrchr($file_name_pod1, '.'), 1));
        $saved_file_name_pod1 = "POD1" . $no_shipment . "." . $ext;
        move_uploaded_file($file_tmp_pod1,"./upload/".$saved_file_name_pod1);

        $ext = strtolower(substr(strrchr($file_tmp_pod2, '.'), 1));
        $saved_file_name_pod2 = "POD2" . $no_shipment . "." . $ext;
        move_uploaded_file($file_tmp_pod2,"./upload/".$saved_file_name_pod2);

        $sql = "SELECT COUNT(*) AS JML FROM EX_TRANS_HDR WHERE NO_SHP_TRN = :no_shp AND STATUS2='OPEN'";
        $stmt = oci_parse($conn, $sql);
        oci_bind_by_name($stmt, ":no_shp", $no_shp_trn);
        oci_execute($stmt);
        $row = oci_fetch_assoc($stmt);
        if($row['JML'] == 0){
            $msg = "(X) => Gagal POD, pastikan Shipment ($no_shipment) tersedia dan sudah ter-recalculate";
            showMessage($msg);
            exit;
        }

        $field_names=array(
            'STATUS','LAST_UPDATE_DATE','LAST_UPDATED_BY','QTY_KTG_RUSAK',
            'QTY_SEMEN_RUSAK','TOTAL_KTG_RUSAK','TOTAL_KTG_REZAK','TOTAL_KLAIM_KTG',
            'TOTAL_SEMEN_RUSAK','HARGA_TEBUS','PDPKS','TOTAL_KLAIM_SEMEN',
            'TOTAL_KLAIM_ALL','KETERANGAN_POD','EVIDENCE_POD1','EVIDENCE_POD2',
            'GEOFENCE_POD','TANGGAL_DATANG','TANGGAL_BONGKAR','FLAG_POD');
        $field_data=array(
            "OPEN","SYSDATE","$username","$qty_ktg_rusak",
            "$qty_semen_rusak","$total_ktg_rusak","$total_ktg_rezak","$total_klaim_ktg",
            "$total_semen_rusak","$harga_tebus","$pdpks","$total_klaim_semen",
            "$total_klaim_all","$keterangan_pod","$saved_file_name_pod1","$saved_file_name_pod2",
            "$geofence_pod","$tanggal_datang",$tanggal_bongkar,"POD-FIOS"); 		
        $tablename="EX_TRANS_HDR";
        $field_id=array('NO_SHP_TRN');
        $value_id=array("$no_shipment");
        $fungsi->update_safe($conn,$field_names,$field_data,$tablename,$field_id,$value_id);

        $msg = "(S) => Berhasil POD Shipment ($no_shipment)";
        showMessage($msg);
        exit;
    }

    
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Data SPJ</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />

<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
<script src="../include/jquery.min.js"></script>
<script src="../include/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" href="../include/bootstrap/css/bootstrap-cus.css">

</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">POD</th>
</tr></table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
	    </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;No. Shipment</td>
            <td class="puso">:</td>
            <td> <input name="no_shipment" type="text" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Evidence POD 1</td>
            <td class="puso">:</td>
            <td> <input name="pod1" type="file"  class="button" accept="image/gif, image/jpeg, image/png" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Evidence POD 2</td>
            <td class="puso">:</td>
            <td> <input name="pod2" type="file"  class="button" accept="image/gif, image/jpeg, image/png" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;QTY KTG RUSAK</td>
            <td class="puso">:</td>
            <td> <input name="qty_ktg_rusak" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;QTY SEMEN RUSAK</td>
            <td class="puso">:</td>
            <td> <input name="qty_semen_rusak" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TANGGAL DATANG</td>
            <td class="puso">:</td>
            <td> 
                <input name="tanggal_datang" type="text" id="Tanggal Datang" value="<?=$tanggal_datang?>" required/>
                <input name="btn_tanggal_datang" type="button" class="button" onClick="return showCalendar('Tanggal Datang');" value="..." />
            </td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TANGGAL BONGKAR</td>
            <td class="puso">:</td>
            <td> 
                <input name="tanggal_bongkar" type="text" id="Tanggal Bongkar" value="<?=$tanggal_bongkar?>" required/>
                <input name="btn_tanggal_bongkar" type="button" class="button" onClick="return showCalendar('Tanggal Bongkar');" value="..." />
            </td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL KTG RUSAK</td>
            <td class="puso">:</td>
            <td> <input name="total_ktg_rusak" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL KTG REZAK</td>
            <td class="puso">:</td>
            <td> <input name="total_ktg_rezak" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL KLAIM KTG</td>
            <td class="puso">:</td>
            <td> <input name="total_klaim_ktg" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL SEMEN RUSAK</td>
            <td class="puso">:</td>
            <td> <input name="total_semen_rusak" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;HARGA TEBUS</td>
            <td class="puso">:</td>
            <td> <input name="harga_tebus" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;PDPKS</td>
            <td class="puso">:</td>
            <td> <input name="pdpks" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL KLAIM SEMEN</td>
            <td class="puso">:</td>
            <td> <input name="total_klaim_semen" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;TOTAL KLAIM ALL</td>
            <td class="puso">:</td>
            <td> <input name="total_klaim_all" type="number" value="0" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;GEOFENCE POD</td>
            <td class="puso">:</td>
            <td> <input name="geofence_pod" type="text" placeholder="ex: -6,7011737, 111,6255767" required></td>
        </tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;KETERANGAN POD</td>
            <td class="puso">:</td>
            <td> <textarea name="keterangan_pod" placeholder="Isikan keterangan"></textarea></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>

        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="submit" name="pod" value="POD" />
            </td>
        </tr>

        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

<script>
    document.getElementById('Tanggal Datang').addEventListener('keydown', function(e) {
        e.preventDefault();
    });
    document.getElementById('Tanggal Bongkar').addEventListener('keydown', function(e) {
        e.preventDefault();
    });
</script>
</body>
</html>
