<?
session_start();
include ('crm_fungsi.php');
include ('validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=846;
//$user_id=$_SESSION['user_id'];
//$user_org=$_SESSION['user_org'];


$user_id=757;
$user_org=2000;
$distr_id=$fungsi->sapcode(147);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");
$nm_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"NAMA_PLANT");


//if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?><!--
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//
				</SCRIPT>

	 <a href="../index.php">Login....</a>-->
<?

//exit();
//}

if(isset($_POST['cari'])){

		$spj = $_POST['nox_spj'];
		$cekspj=$fungsi->findOneByOne($conn,"CRM_GR_SPJ","NO_SPJ_GR",$spj,"NO_SPJ_GR");
		if ($cekspj!=$spj) {
		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_dataprod.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) { $sap->Open();}
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		//header entri
        $fce->X_VKORG = $user_org;
		$fce->X_KUNNR = $distr_id;
		$fce->X_NOSPJ = $spj;
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
			//if ($fce->ZDATA->row["HARGA"] > 0){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$inco_d[$s] = $fce->ZDATA->row["INCOTERM_DESC"];
				$rute[$s] = $fce->ZDATA->row["ROUTE"];
				$rute_txt[$s] = $fce->ZDATA->row["ROUTE_TXT"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				$rfid[$s] = $fce->ZDATA->row["ID_CARD"];
				$no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$namasold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["ITEM_NO"];
				$produk_txt[$s] = $fce->ZDATA->row["PRODUK"];
				$uom1[$s] = $fce->ZDATA->row["UOM"];
				$berat_ksg[$s]=$fce->ZDATA->row["BERAT_KOSONG"];
				$berat_isi[$s]=$fce->ZDATA->row["BERAT_ISI"];
				$qty_so[$s] = $fce->ZDATA->row["KWMENG"];
				$pltype[$s] = $fce->ZDATA->row["PLTYP"];
				$pltype_txt[$s] = $fce->ZDATA->row["PTEXT"];
				$pricedate[$s] = $fce->ZDATA->row["PRSDT"];
				$kdprov[$s] = $fce->ZDATA->row["PROPINSI"];
				$nmprov[$s] = $fce->ZDATA->row["NAMA_PROP"];
				$s++;
				}
			//}
		}else
        		$fce->PrintStatus();

		var_dump($fce->ZDATA->row);

		$fce->Close();	
		$sap->Close();	
		$total=count($no_spj);
		} else {
			echo '<br><br><div align="center" class="login"> No SPJ '.$spj.' Sudah Pernah di Terima</div>';
			exit;
		}
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />


<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

function gettimbang()
{
	var timbang = document.getElementById('timbangan');
	var hasil = document.getElementById('timb_isi');
	var btn_save = document.getElementById('save');

	hasil.value = timbang.value;
	if (timbang.value > 0 ) {
	btn_save.style.visibility = 'visible'; 
	}
}
function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }

function cektanggal(obj) {
var com_tgl = document.getElementById(obj);
var com_kn = document.getElementById('tglnya');
var tgl = com_tgl.value;
var kn = com_kn.value;
var tgl1 = parseInt(tgl.substr(0,2));
var bln1 = parseInt(tgl.substr(3,2));
var th1 = parseInt(tgl.substr(6,4));
var tglo = bln1+"/"+tgl1+"/"+th1;
var tglx = new Date(tglo);
var tgl2 = parseInt(kn.substr(0,2));
var bln2 = parseInt(kn.substr(3,2));
var th2 = parseInt(kn.substr(6,4));
var tgln = bln2+"/"+tgl2+"/"+th2;
var knx = new Date(tgln);
	if( (tglx >= knx) )
	{
	 com_tgl.value=tgl;
	} else { com_tgl.value=kn; }
}

function cek_data() {
		var obj_spj = document.getElementById('nox_spj');
		var cek_spj = obj_spj.value;
		
		if (cek_spj == "") {
			window.alert("No SPJ tidak boleh kosong");
			return false;
		}
		
		return true;
 }
 
function validasi_data() {
	var obj = document.getElementById('jumlah');
	var cek = obj.value;
		
	for (var i = 1; i <= cek; i++){	
		if (validasi('kd_reason', 'Order Reason', 'R', 'keterangan', 'Keterangan', 'R')) {
		
		}else{
		document.hasil = false;
		return false;
		}		
	}
}
    
//-->
</script>

<title>Aplikasi SGG Online: Penerimaan Gudang :)</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body>
<div align="center">
<table width="700" align="center" class="adminheading" border="0">
<tr>
<th class="ba1">Penerimaan Semen</th>
</tr></table></div>
<div align="center">
<? if(isset($_POST['cari'])){ ?>
<? } else {
?>
<div align="center">
<table width="700" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Form Penerimaan Semen </th>
</tr>
</table>
</div>
<form  action="grzak.php" method="post" name="temukan" id="temukan" onSubmit="if (!cek_data()) return false;">
<table width="700" border="0" class="adminform" align="center">
<tr>
	<td width="175"><strong>No SPJ</strong></td>
	<td><strong>:</strong></td>
	<td><input name="nox_spj" id="nox_spj" type="text" size="10" maxlength="12" value=""/>&nbsp;<input name="cari" type="submit" class="button" id="cari" value="Cari" /></td>
	<td><input name="org" type="hidden" id="org" value="<?=$user_org?>"/></td>
</tr>
 </table>
 </form>
 <?
	} 
	if ($s > 0) {
?>
 <form action="komentar.php" method="post" name="tambah" onSubmit="validasi('plant','','R','timb_isi','','R');return document.hasil">
 <table width="700" border="0" class="adminform" align="center">
 <tr>
	<td ><strong>Tanggal Terima</strong></td>
	<td><strong>:</strong></td>
	<td colspan="2"><input name="tgl1" type="text" id="tgl1" size=20 value="<?php
    echo date("d-m-Y    ".(date("I") ? intval(date("g")) - 1 : date("g")) . ":i s")."";?>"/></td>
</tr>
     <tr>
	<td ><strong>No SPJ</strong></td>
	<td><strong>:</strong></td>
	<td colspan="2"><input type="text" name="no_spj" id="no_spj" value="<?=$_POST['nox_spj']?>" size="12" readonly="true"/></td>
 </tr>
 <tr>
	<td ><strong>No Polisi / Sopir</strong></td>
	<td><strong>:</strong></td>
	<td colspan="2"><input type="text" name="nopol" id="nopol" value="<?=$nopol[0]?>" size="12" readonly="true"/> /
	<input type="text" name="sopir" id="sopir" value="<?=$sopir[0]?>" size="12" readonly="true"/> 
	<input type="text" name="rfid" id="rfid" value="<?=$rfid[0]?>" size="12" readonly="true"/>
	<input name="noso" id="noso" type="hidden" value="<?=$no_so[0];?>" readonly="true"/>  
	<input name="nodo" id="nodo" type="hidden" value="<?=$no_do[0];?>" readonly="true"/></td>
 </tr>
	<tr>
	<td ><strong>Produk</strong></td>
      <td  class="puso">:</td>
	  <td colspan="2">
	  <input name="kd_produk" id="kd_produk" type="text" size="12" maxlength="12" value="<?=$produk[0];?>" readonly="true"/>
	  <input name="nm_produk" id="nm_produk" type="text" size="30" value="<?=$produk_txt[0]?>" readonly="true"/>
	  </td>
    </tr>
	<tr>
	<td><strong>Quantity</strong></td>
	<td><strong>:</strong></td>
	<td colspan="2">
	<input name="qty" id="qty" type="text" value="<?=$qty_do[0];?>" size="10" maxlength="10" readonly="true"/>
	<input name="uom" id="uom" type="text" value="<?=$uom1[0];?>" size="3" maxlength="3" readonly="true"/>
	</td>
	</tr>
	<tr>
	<td><strong>Nilai Timbang Plant (Kg)</strong></td>
	<td><strong>:</strong></td>
	<td>Timbangan Kosong : <input name="kosong" id="kosong" value="<?=$berat_ksg[0]?>" size="12" readonly="true">
	Timbangan Isi :<input name="isi" id="isi" value="<?=$berat_isi[0]?>" size="12" readonly="true"></td>
	</tr>		
	<tr>
	<tr>
	<td ><strong>Distributor</strong></td>
      <td  class="puso">:</td>
      <td colspan="2"><input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
	  <input name="sold_to" id="sold_to" type="text" size="10" maxlength="10" value="<?=$soldto[0];?>" readonly="true"/>
	  <input name="nama_sold_to" id="nama_sold_to" type="text" size="30" value="<?=$namasold[0]?>" readonly="true"/>	    
	 </div></td>
    </tr>		
	<td ><strong>Ekspeditur</strong></td>
      <td  class="puso">:</td>
      <td colspan="2"><div id="distrdiv">
	  <input name="kd_eksp" id="kd_eksp" type="text" size="10" maxlength="10" value="<?=$kdexp[0];?>" readonly="true"/>
	  <input name="nm_eksp" id="nm_eksp" type="text" size="30" value="<?=$nmexp[0]?>" readonly="true"/></td>
	  </div></td>
    </tr>
	<tr>
	<td ><strong>Toko</strong></td>
      <td  class="puso">:</td>
      <td colspan="4">
	  <div id="shiptodiv1">
      <input name="shipto1" type="text" id="shipto1" value="<?=$kdshipto[0]?>"  readonly="true" maxlength="10" size="10"/>
      <input name="nama_shipto1" type="text" id="nama_shipto1" value="<?=$nmshipto[0]?>" readonly="true"  size="20"/>
	  <input type="text" value="<?=$alamat[0]?>" id="alamat1" name="alamat1" size="18" readonly="true" >
	  <input type="hidden" value="<?=$kddistrik[0]?>" id="kode_distrik1" name="kode_distrik1" >
	  <input type="text" value="<?=$nmdistrik[0]?>" id="nama_distrik1" name="nama_distrik1"  size="8"  readonly="true" >
	  <input type="hidden" value="<?=$kdprov[0]?>" id="kode_prov1" name="kode_prov1" >
	  <input type="hidden" value="<?=$nmprov[0]?>" id="nama_prov1" name="nama_prov1" >	  
      <input name="val_error_shipto1" type="hidden" id="val_error_shipto1" value="0" />
    </div>
	  </td>
    </tr>
 <tr>
    <td><strong>Plant Asal</strong></td>
    <td><strong>:</strong></td>
	<td colspan="2">
	    <input name="plant_asal" type="text" id="plant_asal" value="<?=$kdplant[0]?>" readonly="true" maxlength="4" size="6"/>
		<input name="nama_plant_asal" type="text" id="nama_plant_asal" value="<?=$nmplant[0]?>" readonly="true"size="20"/>
		<input name="incoterm" type="hidden" id="incoterm" value="<?=$inco[0]?>" readonly="true" size="6" />
		<input name="incoterm_desc" type="hidden" id="incoterm_desc" value="<?=$inco_d[0]?>" readonly="true" />

	</td>
	</tr>
    <tr>
    <td width="175"><strong>Gudang/Silo </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2"><div id="plantdiv">
	    <input name="plant" type="text" id="plant" value="<?=$kd_gdg?>" readonly="true" size="6"/>
		<input name="nama_plant" type="text" id="nama_plant" value="<?=$nm_gdg?>" readonly="true" size="20"/>	  </div>
	</td>
	<tr>
	<td><strong>Jumlah di Terima</strong></td>
	<td><strong>:</strong></td>
	<td><input name="terima" id="terima" value="" size="12"/></td>
	</tr>	
	<tr>
	<td><strong></strong></td>
	<td><strong></strong></td>
    <td ><div align="left">
		<input type="hidden" value="1" name="jumlah" id="jumlah" />
 		<input name="save" type="submit" class="button" id="save" value="Simpan" />
                <input name="action" type="hidden" value="penerimaanzak" />
                <a href="grzak.php" class="button">Cancel</a></div></td>
    </tr>
	<? } ?>
</table> 
</form>
</div>
</body>
</html>
