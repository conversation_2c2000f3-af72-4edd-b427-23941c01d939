<?php

/*
 * Import Target Adjusment dan <PERSON>rak Volume.
 * @liyantanto
 */
?>
<?
session_start();
require_once '../include/oracleDev.php'; 
$fungsi=new conntoracleDEVSD();
$conn=$fungsi->DEVSDdb();
//Format Nilai
function showNilai2($nilai){
	if($nilai>0) return number_format($nilai,2);
	else return '0';
}

//$hakakses=array("admin");
//$halaman_id=8;

$user_id=$_SESSION['user_id'];
//$user_id='mady';
$importtargetVolume='import_targetVolume.php';

############################# READ XLS ####################
error_reporting(E_ALL ^ E_NOTICE);
require_once 'excel_reader2.php';

if(isset ($_POST['Import'])){
       $allowedExts = "xls";
       $extension = end(explode(".", $_FILES["file"]["name"]));
        if ($extension==$allowedExts)
          {
         
            //echo "Upload: " . $_FILES["file"]["name"] . "<br />";
            $pecah=$_FILES["file"]["name"];
            $pecahTanda=explode("_", $pecah);
            //print_r($pecahTanda);
            $bulan = substr($pecahTanda[1], -10, -8)."<br/>"; 
            $tahun = substr($pecahTanda[1], -8, -4)."<br/>";
            $kd_plant = substr($pecahTanda[1], -4)."<br/>";
            
                 
            $cell   = new Spreadsheet_Excel_Reader($_FILES['file']['tmp_name']);
            $jumlah = $cell->rowcount($sheet_index=0);
             $i = 4; // dimulai dari ke dua karena baris pertama berisi title
            while( $i<=$jumlah ){
               //$cell->val( baris,kolom )
               $bulanF   = $cell->val( 2,1 );
               $tahunF   = $cell->val( 2,2 );
               $kd_plantF   = $cell->val( 2,3 );
                
               $NO_EXPEDITUR   = $cell->val( $i,2 );
               $NAMA_EXPEDITUR   = $cell->val( $i,3 );
               $KODE_KOTA   = $cell->val( $i,4 );
               $NAMA_KOTA    = $cell->val( $i,5 );
               $ADJUSTMANT   = $cell->val( $i,9 );
               $ADJUSTMANT = ereg_replace(',', '.', $ADJUSTMANT);
               $KONTRAK_VOLUME   = $cell->val( $i,10 );
               $KONTRAK_VOLUME = ereg_replace(',', '.', $KONTRAK_VOLUME);
               $STATUS   = $cell->val( $i,11 );
               
               $sqlUpdate2="UPDATE ZREPORT_TARGET_EXP SET ADJUSTMANT=$ADJUSTMANT, KONTRAK_VOL=$KONTRAK_VOLUME , PETUGAS='$user_id'
               , STATUS=$STATUS
               WHERE BULAN=$bulanF and TAHUN='$tahunF' and PLANT='$kd_plantF' and COM='2000' and 
               NO_EXPEDITUR=$NO_EXPEDITUR and KOTA='$KODE_KOTA' and BRAN1 is null
               ";
              // echo $sqlUpdate2."<br/>";
               $query2= oci_parse($conn, $sqlUpdate2);
               oci_execute($query2);
                
               $i++;
            }

            echo "<script>alert('Update Target Volume sukses...!!');</script>";  
          }
        else
          {
               echo "<script>alert('Invalid file...!!');</script>";  
          }

    
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Import Data Target Volume dan Index</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Import Data Target Volume dan Index</th>
</tr></table>
</div>

<form method="post" name="import" id="import" enctype="multipart/form-data" action="<?=$importtargetVolume;?>">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso">&nbsp;&nbsp;&nbsp;Silakan Pilih File Excel</td>
            <td class="puso">:</td>
            <td> <input name="file" type="file"  class="button"></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="Import" type="submit"  class="button" value="Import"></td>
        </tr>

    </table>
</form>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>