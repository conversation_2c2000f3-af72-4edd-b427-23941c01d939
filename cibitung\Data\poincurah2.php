<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();
		$sql="SELECT  NO_TOKO,NAMA_TOKO,ALAMAT,NMOR,KODE_PLANT,KUNNR,NAMA_KUNNR,SUM(TOTAL_VALUME) AS  TOTAL_QTY
                 FROM 
                 PT_RELEASE_CURAH1                
                 GROUP BY
                 NO_TOKO,NAMA_TOKO,ALAMAT,NMOR,KODE_PLANT,KUNNR,NAMA_KUNNR";
		$query= oci_parse($conn, $sql);
		oci_execute($query);
		$s=0;
		$tglstock = date('d/m/Y H:i:s');
		while($data=oci_fetch_array($query)){
			$idnya[]=$data['ID'];
			$KD_DISTR[]=$data['KUNNR'];
			$NM_DISTR[]=$data['NAMA_KUNNR'];
			$KD_SHIPTO[]=$data['NO_TOKO'];
			$NM_SHIPTO[]=$data['NAMA_TOKO'];
			$ALAMAT[]=$data['ALAMAT'];
			$ORG[]=$data['NMOR'];
			$DELETE_MARK[]=$data['DELETE_MARK'];
			$STATUS[]=$data['STATUS'];
			$TGL_RELEASE[]=$data['TGL_RELEASE'];
			$TOTAL[]=$data['TOTAL_QTY'];

				$or_username = "dev";
				$or_password = "semeru2";
				$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
				$conn = oci_connect($or_username, $or_password, $or_db);	
				//$sEndDate   = Date('d/m/Y H:i:s',strtotime( $TGL_RELEASE[$s]));	
				$ww=gmdate("d/m/Y H:i:s",strtotime( $TGL_RELEASE[$s]));
				 



				$q_stock ="INSERT INTO PT_RELEASE_CURAH (NMOR, KUNNR, NAMA_KUNNR, NO_TOKO, NAMA_TOKO,TOTAL_VALUME,ALAMAT,STATUS,DELETE_FLAX,TGL_RELEASE,TGL_CREATE) values ('".$ORG[$s]."','".$KD_DISTR[$s]."','".$NM_DISTR[$s]."','".$KD_SHIPTO[$s]."','".$NM_SHIPTO[$s]."','".$TOTAL[$s]."','".$ALAMAT[$s]."',0,0,to_date('".$ww."','DD/MM/YYYY HH24:MI:SS'),to_date('".$tglstock."','DD/MM/YYYY HH24:MI:SS'))";
				$query1= oci_parse($conn, $q_stock);
				oci_execute($query1);
				$s++;
			echo $q_stock;
			ECHO $sEndDate1;
		
			}
				
			
?> 