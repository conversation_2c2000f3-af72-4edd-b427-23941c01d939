<?
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php');
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

        $sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RFID_MANDIRI2");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
######### NEW ###############
#echo "<br>T1:".
$tgl1 = date('Ymd', strtotime("-5 days"));
#echo "<br>T2:".
$tgl2 = date('Ymd');
#exit;
#$tgl1 = '20120111';
#$tgl2 = '20120112';
$plant = '';
$fce->I_NMORG='2000';
$fce->I_NMPLAN=$plant;
echo $fce->I_TGL_CMPLT_FR=$tgl1;
echo $fce->I_TGL_CMPLT_TO=$tgl2;
//$fce->I_NO_EXPEDITUR='';
#############################
$fce->Call();
if ($fce->GetStatus() == SAPRFC_OK ) {	
			$fce->RETURN_DATA->Reset();

			$s=0; 
			while ( $fce->RETURN_DATA->Next() ){                            
                                $NMORG = $fce->RETURN_DATA->row['NMORG'];
                                $NMPLAN = $fce->RETURN_DATA->row['NMPLAN'];
                                #echo "<br> - ".
                                $NO_TRANSAKSI = $fce->RETURN_DATA->row['NO_TRANSAKSI'];
                                $POSNR = $fce->RETURN_DATA->row['POSNR'];
                                $NO_SHIPMENT = $fce->RETURN_DATA->row['NO_SHIPMENT'];
								$TOTAL_QTY = $fce->RETURN_DATA->row['TOTAL_QTY'];
                                $NO_POLISI = $fce->RETURN_DATA->row['NO_POLISI'];
                                $TGL_CMPLT = $fce->RETURN_DATA->row['TGL_CMPLT'];
                                $JAM_CMPLT = $fce->RETURN_DATA->row['JAM_CMPLT'];
                                $NO_EXPEDITUR = $fce->RETURN_DATA->row['NO_EXPEDITUR'];
                                $NAMA_EXPEDITUR = $fce->RETURN_DATA->row['NAMA_EXPEDITUR'];
                                $SAL_GROUP = $fce->RETURN_DATA->row['SAL_GROUP'];
                                $NMSAL_GROUP = $fce->RETURN_DATA->row['NMSAL_GROUP'];
                                $SAL_OFFICE = $fce->RETURN_DATA->row['SAL_OFFICE'];
                                $NMSAL_OFFICE = $fce->RETURN_DATA->row['NMSAL_OFFICE'];
                                $KODE_KECAMATAN = $fce->RETURN_DATA->row['KODE_KECAMATAN'];
                                $NAMA_KECAMATAN = $fce->RETURN_DATA->row['NAMA_KECAMATAN'];								
                                $TOTAL_QTYX = $fce->RETURN_DATA->row['TOTAL_QTYX'];								
								echo " = ".
                                                                $TGL_RETOKO = $fce->RETURN_DATA->row['TGL_RETOKO'];
								echo " = ".
                                                                $JAM_RETOKO = $fce->RETURN_DATA->row['JAM_RETOKO'];
								$SHIP_TO_CODE = $fce->RETURN_DATA->row['SHIP_TO_CODE'];
								$SHIP_TO_PARTY = $fce->RETURN_DATA->row['SHIP_TO_PARTY'];

							   $s++;

/*
							 

							   	echo'<br>'.	$removeEvent = "delete from rfid_mandiri where no_shipment='".$NO_SHIPMENT."'";
                                $rstmt = OCIParse($conn, $removeEvent);
                                OCIExecute($rstmt, OCI_DEFAULT);
                                OCI_Commit($conn);
                               // OCILogoff($conn);


								$q_stock= "insert into rfid_mandiri(nmorg,nmplant,no_transaksi,posnr,no_shipment,total_qty,no_polisi,tgl_cmplt,jam_cmplt,no_expeditur,nama_expeditur,sal_group,nmsal_group,sal_office,nmsal_office,kode_kecamatan,nama_kecamatan,total_qtyx,tgl_retoko,jam_retoko,ship_to_code,ship_to_party,last_update) values ('".$NMORG."','".$NMPLAN."','".$NO_TRANSAKSI."','".$POSNR."','".$NO_SHIPMENT."','".$TOTAL_QTY."','".$NO_POLISI."','".$TGL_CMPLT."','".$JAM_CMPLT."','".$NO_EXPEDITUR."','".$NAMA_EXPEDITUR."','".$SAL_GROUP."','".$NMSAL_GROUP."','".$SAL_OFFICE."','".$NMSAL_OFFICE."','".$KODE_KECAMATAN."','".$NAMA_KECAMATAN."','".$TOTAL_QTYX."','".$TGL_RETOKO."','".$JAM_RETOKO."','".$SHIP_TO_CODE."','".$SHIP_TO_PARTY."',to_date('02/03/2012','DD/MM/YYYY'))";

								echo'<br>qwert'.$q_stock;
		                        $query = oci_parse($conn,$q_stock);
		                        $status = oci_execute($query);
		                                  oci_commit($conn);

								if($query) echo "SIP";
								else echo "XXXX"; */
                    }                                    

}else
	$fce->PrintStatus();                   


#print_r($NO_SHIPMENT);        
       

?>