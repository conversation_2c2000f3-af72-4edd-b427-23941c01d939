<? 
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=874;
$user_id='388';
$user_org='GD225';
$distr_id='225';

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="daftar_stok_distr.php";
$currentPage="daftar_stok_distr.php";
$komen="";
	
	$sql0="SELECT * FROM CRM_STOK_GDG WHERE KD_GDG LIKE '$distr_id%'";
	//echo $sql0;
	$query0= oci_parse($conn, $sql0);
	oci_execute($query0);
	while($data=oci_fetch_array($query0)){
		$idnya[]=$data['ID'];
		$kd_gdg[]=$data['KD_GDG'];
		$nm_gdg[]=$data['NM_GDG'];
		$kd_mat[]=$data['KD_MATERIAL'];
		$nm_mat[]=$data['NM_MATERIAL'];
		$kd_distr[]=$data['KD_DISTR'];
		$nm_distr[]=$data['NM_DISTR'];
		$stok[]=$data['STOK'];
		$stok_awal[]=$data['STOK_AWAL'];
	}
	$total=count($idnya);
	if ($total < 1) $komen = "Tidak Ada Data Stok Yang Ditemukan";
	
?>
<?

//$sap = new SAPConnection();
	    //$sap->Connect("../cibitung/include/sapclasses/logon_data.conf");
		//if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		//if ($sap->GetStatus() != SAPRFC_OK ) {
		//   echo $sap->PrintStatus();
		//   exit;
		//}
		$loginFile = array(	"ASHOST"	=> "**********", 	// application server host name
								"SYSNR" 	=> "00",			// service
								"CLIENT" 	=> "030", 			// client
								"USER" 		=> "amin", 	// user
								"PASSWD" 	=> "aminazhar"		// password
								);
			$sap = new SAPConnection();
			$sap->Connect($loginFile);
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK )
			{
			   //echo $sap->PrintStatus();
			     exit;
            }

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		$tgl_kini= date('Ymd');
		 $fce->X_VKORG ='2000';
		 $fce->X_TGL1 = $tgl_kini;
		 $fce->X_TGL2 = $tgl_kini; //
				$fce->X_NOFLAG = 'X'; // flag belum sampai gudang
                $fce->X_CURAHBAG = '10';
                $fce->X_STATUS='70';
                $fce->X_STATUS_TO='70';
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_kontrak[$s] = $fce->ZDATA->row["VGBEL"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$tgl_so[$s] = $fce->ZDATA->row["AUDAT"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$pricegrp[$s] = $fce->ZDATA->row["PLTYP"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				$no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$nama_sold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["PRODUK"];
				$uom[$s] = $fce->ZDATA->row["UOM"];
				$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total1=count($nopol);	

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Stok Gudang :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
	<div align="center">
	<table width="1000" align="center" class="adminlist">
  <?  
  		$totaldo= 0;
  		for($z=0; $z<$total1;$z++) {
		$totaldo= $totaldo+$qty_do[$z];
		$b=$z+1;
		if(($z % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<? $b; ?>
		<? $qty_do[$z]; ?>
		
	
	  <? } ?>
	    <? number_format($totaldo,3,".",","); ?>
	</table>	
<div align="center">
<table width="500" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" >Daftar Stok Gudang</th>
</tr></table></div>
<?
	if($total>0){
?>
	<div align="center">
	<table width="500" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="500" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Stok Gudang</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="500" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>No</strong></td>
		<td align="center"><strong >Kode Gudang</strong></td>
		<td align="center"><strong >Nama Gudang</strong></td>
		<td align="center"><strong >Kode Material</strong></td>
		<td align="center"><strong >Nama Material</strong></td>
		<td align="center"><strong >Stok (Ton)</strong></td>
		<td align="center"><strong >STOK AWAL</strong></td>
		<td align="center"><strong >Total TSDP</strong></td>

      </tr >
  <?  for($a=0; $a<$total;$a++) {
		if(($a % 2) == 0)	{	 
		echo "<tr class='odd0'>";
			}
		else	{	
		echo "<tr class='odd1'>";
			}	
		$b=$a+1;
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $kd_gdg[$a];?></td>
		<td align="left"><? echo $nm_gdg[$a]; ?></td>
		<td align="center"><? echo $kd_mat[$a];?></td>
		<td align="left"><? echo $nm_mat[$a]; ?></td>
		<td align="right"><? echo $stok[$a]; ?></td>
		<td align="right"><? echo $stok_awal[$a]; ?></td>
		<? } ?>
		<td align="center" valign='top'rowspan='5'><? echo $totaldo; ?></td>
		</tr>
	
	     
	</table>
	</div>
	<? } ?>
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
