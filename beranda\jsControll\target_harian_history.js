var lineChartSpj;
$(document).ready(function() {
   var result;
   var tglAwal = $("#spjdate1").val();
   var tglAkhir = $("#spjdate2").val();
   var company = $("#company").val();

    $.ajax({
      url: 'xhr/model_dist.php?p=multiline-spj',
      type: 'GET',
      dataType: 'JSON',
      data : {
        tglAwal: tglAwal,
        tglAkhir: tglAkhir,
        company: company,
      },
      beforeSend: function(){
        $("#loadingSpj").fadeIn(1000);
        $("#spj").hide();
      }
    })
    .done(function(data) {
      result = true;
    })
    .fail(function(data) {
      result = false;
    })
    .always(function(data) {
      $("#loadingSpj").fadeOut(1000, function() {
        $("#spj").show(1000);
        if(result){
          var config = {
              type: 'line',
              data: data.grap,
              options: {
                  responsive: true,
                  legend: {
                      position: 'bottom',
                  },
                  title:{
                      display:true,
                      text:'Statistik Surat Perintah Jalan'
                  },
                  tooltips: {
                      mode: 'index',
                      intersect: false,
                  },
                  hover: {
                      mode: 'nearest',
                      intersect: true
                  },
                  scales: {
                      xAxes: [{
                          display: true,
                          scaleLabel: {
                              display: true,
                              labelString: 'Day'
                          }
                      }],
                      yAxes: [{
                          display: true,
                          scaleLabel: {
                              display: true,
                              labelString: 'Value'
                          }
                      }]
                  }
              }
          };

        var ctx = document.getElementById("lineChartSpj").getContext("2d");
        lineChartSpj = new Chart(ctx, config);
        //presentase

        // iterasi do
        var dataDo = "";
        if(data.DataDo.length < 1){
          dataDo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataDo, function(index, data){
              dataDo += '<tr style="font-size:12px;">'+
                          '<td>'+data.NO_SHP_TRN+'</td>'+
                          '<td>'+data.NAMA_VENDOR+'</td>'+
                          '<td>'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi oo
        var dataOo = "";
        if(data.DataOo.length < 1){
          dataOo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataOo, function(index, data){
              dataOo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi po
        var dataPo = "";
        if(data.DataPo.length < 1){
          dataPo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataPo, function(index, data){
              dataPo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi io
        var dataIo = "";
        if(data.DataIo.length < 1){
          dataIo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataIo, function(index, data){
              dataIo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }

        var content = '<tr>'+
                        '<th style="background:#F44336; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                        '<div class="panel-group" id="panelDo">'+
                          '<div class="panel panel-default">'+
                            '<div class="panel-heading">'+
                               '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelDo" href="#panelDoBody">A. Outstanding Expediture <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                            '</div>'+
                            '<div id="panelDoBody" class="panel-collapse collapse">'+
                              '<div class="panel-body">'+
                                '<table class="table table-hover">'+
                                  '<thead>'+
                                    '<tr>'+
                                      '<th style="font-size:12px;">SHP TRN</th>'+
                                      '<th style="font-size:12px;">VENDOR</th>'+
                                      '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                    '</tr>'+
                                  '</thead>'+
                                  '<tbody>'+
                                    dataDo+
                                  '</tbody>'+
                                '</table>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_DRAFT_OPEN+' ('+data.presentase.presentase_draft_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#FF9800; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                          '<div class="panel-group" id="panelOo">'+
                            '<div class="panel panel-default">'+
                              '<div class="panel-heading">'+
                                 '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelOo" href="#panelOoBody">B. Outstanding Distributor <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                              '</div>'+
                              '<div id="panelOoBody" class="panel-collapse collapse">'+
                                '<div class="panel-body">'+
                                  '<table class="table table-hover">'+
                                    '<thead>'+
                                      '<tr>'+
                                        '<th style="font-size:12px;">SHP TRN</th>'+
                                        '<th style="font-size:12px;">VENDOR</th>'+
                                        '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                      '</tr>'+
                                    '</thead>'+
                                    '<tbody>'+
                                        dataOo+
                                    '</tbody>'+
                                  '</table>'+
                                '</div>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_OPEN_OPEN+' ('+data.presentase.presentase_open_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#4CAF50; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                          '<div class="panel-group" id="panelPo">'+
                            '<div class="panel panel-default">'+
                              '<div class="panel-heading">'+
                                 '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelPo" href="#panelPoBody">C. Create Invoice <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                              '</div>'+
                              '<div id="panelPoBody" class="panel-collapse collapse">'+
                                '<div class="panel-body">'+
                                  '<table class="table table-hover">'+
                                    '<thead>'+
                                      '<tr>'+
                                        '<th style="font-size:12px;">SHP TRN</th>'+
                                        '<th style="font-size:12px;">VENDOR</th>'+
                                        '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                      '</tr>'+
                                    '</thead>'+
                                    '<tbody>'+
                                      dataPo+
                                    '</tbody>'+
                                  '</table>'+
                                '</div>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_PROGRESS_OPEN+' ('+data.presentase.presentase_progress_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#009688; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                        '<div class="panel-group" id="panelIo">'+
                          '<div class="panel panel-default">'+
                            '<div class="panel-heading">'+
                               '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelIo" href="#panelIoBody">D. Invoiced <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                            '</div>'+
                            '<div id="panelIoBody" class="panel-collapse collapse">'+
                              '<div class="panel-body">'+
                                '<table class="table table-hover">'+
                                  '<thead>'+
                                    '<tr>'+
                                      '<th style="font-size:12px;">SHP TRN</th>'+
                                      '<th style="font-size:12px;">VENDOR</th>'+
                                      '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                    '</tr>'+
                                  '</thead>'+
                                  '<tbody>'+
                                    dataIo+
                                  '</tbody>'+
                                '</table>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_INVOICED_OPEN+' ('+data.presentase.presentase_invoiced_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th colspan="2" class="text-right">Total Data</th>'+
                        '<th class="text-center">'+data.totalData+'</th>'+
                      '</tr>';

        $("#presentase").find('tbody').append(content);
        } else{
          alert("Internal Server Error.. Sorry");
        }
      });
    });
 });

 $("#reloadChart").on("submit", function(e){
   e.preventDefault();
   lineChartSpj.destroy();
   $("#presentase").find('tbody tr').remove();
   var result;
   var tglAwal = $("#spjdate1").val();
   var tglAkhir = $("#spjdate2").val();
   var company = $("#company").val();
    $.ajax({
      url: 'xhr/model_dist.php?p=multiline-spj',
      type: 'GET',
      dataType: 'JSON',
      data : {
        tglAwal: tglAwal,
        tglAkhir: tglAkhir,
        company: company
      },
      beforeSend: function(){
        $("#loadingSpj").fadeIn(1000);
        $("#spj").hide();
      }
    })
    .done(function(data) {
      result = true;
    })
    .fail(function(data) {
      result = false;
    })
    .always(function(data) {
      $("#loadingSpj").fadeOut(1000, function() {
        $("#spj").show(1000);
        if(result){
          var config = {
              type: 'line',
              data: data.grap,
              options: {
                  responsive: true,
                  legend: {
                      position: 'bottom',
                  },
                  title:{
                      display:true,
                      text:'Statistik Surat Perintah Jalan'
                  },
                  tooltips: {
                      mode: 'index',
                      intersect: false,
                  },
                  hover: {
                      mode: 'nearest',
                      intersect: true
                  },
                  scales: {
                      xAxes: [{
                          display: true,
                          scaleLabel: {
                              display: true,
                              labelString: 'Day'
                          }
                      }],
                      yAxes: [{
                          display: true,
                          scaleLabel: {
                              display: true,
                              labelString: 'Value'
                          }
                      }]
                  }
              }
          };

        var ctx = document.getElementById("lineChartSpj").getContext("2d");
        lineChartSpj = new Chart(ctx, config);
        // iterasi do
        var dataDo = "";
        if(data.DataDo.length < 1){
          dataDo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataDo, function(index, data){
              dataDo += '<tr style="font-size:12px;">'+
                          '<td>'+data.NO_SHP_TRN+'</td>'+
                          '<td>'+data.NAMA_VENDOR+'</td>'+
                          '<td>'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi oo
        var dataOo = "";
        if(data.DataOo.length < 1){
          dataOo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataOo, function(index, data){
              dataOo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi po
        var dataPo = "";
        if(data.DataPo.length < 1){
          dataPo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataPo, function(index, data){
              dataPo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }
        // iterasi io
        var dataIo = "";
        if(data.DataIo.length < 1){
          dataIo += '<tr style="font-size:12px;">'+
                      '<td colspan="3" class="text-center">NO DATA</td>'+
                    '</tr>';
        } else {
            $.each(data.DataIo, function(index, data){
              dataIo += '<tr>'+
                          '<td style="font-size:12px;">'+data.NO_SHP_TRN+'</td>'+
                          '<td style="font-size:12px;">'+data.NAMA_VENDOR+'</td>'+
                          '<td style="font-size:12px;">'+data.TANGGAL_KIRIM+'</td>'+
                        '</tr>';
            })
        }

        var content = '<tr>'+
                        '<th style="background:#F44336; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                        '<div class="panel-group" id="panelDo">'+
                          '<div class="panel panel-default">'+
                            '<div class="panel-heading">'+
                               '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelDo" href="#panelDoBody">A. Outstanding Expediture <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                            '</div>'+
                            '<div id="panelDoBody" class="panel-collapse collapse">'+
                              '<div class="panel-body">'+
                                '<table class="table table-hover">'+
                                  '<thead>'+
                                    '<tr>'+
                                      '<th style="font-size:12px;">SHP TRN</th>'+
                                      '<th style="font-size:12px;">VENDOR</th>'+
                                      '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                    '</tr>'+
                                  '</thead>'+
                                  '<tbody>'+
                                    dataDo+
                                  '</tbody>'+
                                '</table>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_DRAFT_OPEN+' ('+data.presentase.presentase_draft_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#FF9800; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                          '<div class="panel-group" id="panelOo">'+
                            '<div class="panel panel-default">'+
                              '<div class="panel-heading">'+
                                 '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelOo" href="#panelOoBody">B. Outstanding Distributor <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                              '</div>'+
                              '<div id="panelOoBody" class="panel-collapse collapse">'+
                                '<div class="panel-body">'+
                                  '<table class="table table-hover">'+
                                    '<thead>'+
                                      '<tr>'+
                                        '<th style="font-size:12px;">SHP TRN</th>'+
                                        '<th style="font-size:12px;">VENDOR</th>'+
                                        '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                      '</tr>'+
                                    '</thead>'+
                                    '<tbody>'+
                                        dataOo+
                                    '</tbody>'+
                                  '</table>'+
                                '</div>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_OPEN_OPEN+' ('+data.presentase.presentase_open_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#4CAF50; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                          '<div class="panel-group" id="panelPo">'+
                            '<div class="panel panel-default">'+
                              '<div class="panel-heading">'+
                                 '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelPo" href="#panelPoBody">C. Create Invoice <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                              '</div>'+
                              '<div id="panelPoBody" class="panel-collapse collapse">'+
                                '<div class="panel-body">'+
                                  '<table class="table table-hover">'+
                                    '<thead>'+
                                      '<tr>'+
                                        '<th style="font-size:12px;">SHP TRN</th>'+
                                        '<th style="font-size:12px;">VENDOR</th>'+
                                        '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                      '</tr>'+
                                    '</thead>'+
                                    '<tbody>'+
                                      dataPo+
                                    '</tbody>'+
                                  '</table>'+
                                '</div>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_PROGRESS_OPEN+' ('+data.presentase.presentase_progress_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th style="background:#009688; border: 0px;"></th>'+
                        '<th style="border: 0px;" class="text-left">'+
                        '<div class="panel-group" id="panelIo">'+
                          '<div class="panel panel-default">'+
                            '<div class="panel-heading">'+
                               '<a class="panel-title collapsed" data-toggle="collapse" style="font-size: 12px;" data-parent="#panelIo" href="#panelIoBody">D. Invoiced <i style="font-size:16px;" class="pull-right fa fa-caret-down"></i></a>'+
                            '</div>'+
                            '<div id="panelIoBody" class="panel-collapse collapse">'+
                              '<div class="panel-body">'+
                                '<table class="table table-hover">'+
                                  '<thead>'+
                                    '<tr>'+
                                      '<th style="font-size:12px;">SHP TRN</th>'+
                                      '<th style="font-size:12px;">VENDOR</th>'+
                                      '<th style="font-size:12px;">TANGGAL KIRIM</th>'+
                                    '</tr>'+
                                  '</thead>'+
                                  '<tbody>'+
                                    dataIo+
                                  '</tbody>'+
                                '</table>'+
                              '</div>'+
                            '</div>'+
                          '</div>'+
                        '</div>'+
                        '</th>'+
                        '<th style="border: 0px; font-size:13px;" class="text-right"> '+data.qty.TOTAL_INVOICED_OPEN+' ('+data.presentase.presentase_invoiced_open.toFixed(2)+'%)</th>'+
                      '</tr>'+
                      '<tr>'+
                        '<th colspan="2" class="text-right">Total Data</th>'+
                        '<th class="text-center">'+data.totalData+'</th>'+
                      '</tr>';
        $("#presentase").find('tbody').append(content);
        } else{
          alert("Internal Server Error.. Sorry");
        }
      });
    });
 })
