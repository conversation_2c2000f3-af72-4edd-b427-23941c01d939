<? 
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=874;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$_SESSION['distr_id']*1;

//$user_id='757';
//$user_org='2000';
//$distr_id='147';
//$distr='0000000'.'$distr_id';
//$distr = sprintf("%010d", $distr_id);
/*
$sql = "SELECT C.KD_DISTR, C.NM_DISTR, 
A.KD_GDG, A.NM_GDG
FROM CRM_STOK_GDG A, CRM_SPJ_HDR B, CRM_SPJ_DTL C
WHERE A.KD_GDG = B.KD_GDG AND
B.NO_TRANSAKSI = C.NO_TRANSAKSI
GROUP BY C.KD_DISTR, C.NM_DISTR, 
A.KD_GDG, A.NM_GDG";
$query1= oci_parse($conn, $sql);
oci_execute($query1);
while($data=oci_fetch_array($query1)){
	$GUDANG[$data['KD_GDG']] = $data['KD_DISTR'];
	$NM_GUDANG[$data['KD_GDG']] = $data['NM_DISTR'];
}
#print_r($GUDANG);*/

$sql = "SELECT  B.KD_DISTR, B.NM_DISTR,A.KD_GDG, A.NM_GDG,A.STOK
FROM CRM_STOK_GDG A, CRM_GR_SPJ B
WHERE A.KD_GDG = B.KD_GDG 
GROUP BY  A.KD_GDG, A.NM_GDG,A.STOK, B.KD_DISTR, B.NM_DISTR";
$query1= oci_parse($conn, $sql);
oci_execute($query1);
while($data=oci_fetch_array($query1)){
	$GUDANG[$data['KD_GDG']] = $data['KD_DISTR'];
	$NM_GUDANG[$data['KD_GDG']] = $data['NM_DISTR'];
}
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$page="daftar_stok_distr.php";
$currentPage="daftar_stok_distr.php";
$komen="";
	
	$sql0="SELECT * FROM CRM_STOK_GDG  ";
	//echo $sql0;
	$query0= oci_parse($conn, $sql0);
	oci_execute($query0);
	while($data=oci_fetch_array($query0)){
		$idnya[]=$data['ID'];
		$kd_gdg[]=$data['KD_GDG'];
		$nm_gdg[]=$data['NM_GDG'];
		$kd_mat[]=$data['KD_MATERIAL'];
		$nm_mat[]=$data['NM_MATERIAL'];
		$kd_distr[]=$data['KD_DISTR'];
		$nm_distr[]=$data['KD_GDG'];// //$data['NM_DISTR'];
		$stok[]=$data['STOK'];
		$stok_awal[]=$data['STOK_AWAL'];
	}
	$total=count($idnya);
	if ($total < 1) $komen = "Tidak Ada Data Stok Yang Ditemukan";
	//var_dump($kd_gdg);
?>

<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Stok Gudang :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
	<div align="center">
	<table width="1000" align="center" class="adminlist">
  <?  
  		$totaldo= 0;
  		for($z=0; $z<$total1;$z++) {
		$totaldo= $totaldo+$qty_do[$z];
		$b=$z+1;
		if(($z % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<? $b; ?>
		<? $qty_do[$z]; ?>
	
	  <? } ?>
	    <?  number_format($totaldo,3,".",","); ?>
		
	</table>	
<div align="center">
<table width="500" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" >Daftar Stok Gudang</th>
</tr></table></div>
<?
	if($total>0){
?>
	<div align="center">
	<table width="500" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="500" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Stok Gudang</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="500" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>No</strong></td>
		<td align="center"><strong >Nama Distributor</strong></td>
		<td align="center"><strong >Nama Gudang</strong></td>
		<td align="center"><strong >Nama Material</strong></td>
		<td align="center"><strong >Stok hari ini</strong></td>
		
		

      </tr >
  <?  
	
	  for($a=0; $a<$total;$a++) {
		if(($a % 2) == 0)	{	 
		echo "<tr class='odd0'>";
		
			}
		else	{	
		echo "<tr class='odd1'>";
			}	
		$b=$a+1;
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $NM_GUDANG[$kd_gdg[$a]]; ?></td>
		<td align="left"><? echo $nm_gdg[$a]; ?></td>
		<td align="left"><? echo $nm_mat[$a]; ?></td>
		<td align="right"><? echo number_format($stok[$a],0,",","."); ?></td>
		
		<? } ?>
		<!--<td align="center" valign='top'rowspan='5'><? echo number_format($totaldo,3,".",","); ?></td>-->
		</tr>
	
	     
	</table>
	</div>
	<? } ?>
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
