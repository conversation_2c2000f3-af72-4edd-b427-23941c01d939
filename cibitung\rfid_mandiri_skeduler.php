<? 
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=861;

$page="rfid_mandiri_skeduler.php";
$currentPage="rfid_mandiri_skeduler.php";
$komen="";
        $tgl1 = date('Ymd', strtotime("-5 days"));
        $tgl2 = date('Ymd');

		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data1.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		echo $sap->PrintStatus();
		exit;
		}
		$fce = $sap->NewFunction ("Z_ZAPPSD_RFID_MANDIRI2");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}

        echo'<br>'. $fce->I_NMORG ='2000';
		echo'<br>'.$fce->I_TGL_CMPLT_FR = $tgl1;
		echo'<br>'.$fce->I_TGL_CMPLT_TO = $tgl2; // tgl sampai
		echo'<br>'.$fce->I_NMPLAN = '2403'; // plant
	
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			$s=0;
			while ( $fce->RETURN_DATA->Next() ){

			                     echo'<br>'.$NMORG = $fce->RETURN_DATA->row['NMORG'];
                                 echo'<br>'.$NMPLAN = $fce->RETURN_DATA->row['NMPLAN'];
			                     echo'<br>'.$NO_TRANSAKSI = $fce->RETURN_DATA->row['NO_TRANSAKSI'];
                                 echo'<br>'.$POSNR = $fce->RETURN_DATA->row['POSNR'];
                                 echo'<br>'.$NO_SHIPMENT = $fce->RETURN_DATA->row['NO_SHIPMENT'];
								 echo'<br>'.$TOTAL_QTY = $fce->RETURN_DATA->row['TOTAL_QTY'];
                                 echo'<br>'.$NO_POLISI = $fce->RETURN_DATA->row['NO_POLISI'];
                                 echo'<br>'.$TGL_CMPLT = $fce->RETURN_DATA->row['TGL_CMPLT'];
                                 echo'<br>'.$JAM_CMPLT = $fce->RETURN_DATA->row['JAM_CMPLT'];
                                 echo'<br>'.$NO_EXPEDITUR = $fce->RETURN_DATA->row['NO_EXPEDITUR'];
                                 echo'<br>'.$NAMA_EXPEDITUR = $fce->RETURN_DATA->row['NAMA_EXPEDITUR'];
                                 echo'<br>'.$SAL_GROUP = $fce->RETURN_DATA->row['SAL_GROUP'];
                                 echo'<br>'.$NMSAL_GROUP = $fce->RETURN_DATA->row['NMSAL_GROUP'];
                                 echo'<br>'.$SAL_OFFICE = $fce->RETURN_DATA->row['SAL_OFFICE'];
                                 echo'<br>'.$NMSAL_OFFICE = $fce->RETURN_DATA->row['NMSAL_OFFICE'];
                                 echo'<br>'.$KODE_KECAMATAN = $fce->RETURN_DATA->row['KODE_KECAMATAN'];
                                 echo'<br>'.$NAMA_KECAMATAN = $fce->RETURN_DATA->row['NAMA_KECAMATAN'];								
                                 echo'<br>'.$TOTAL_QTYX = $fce->RETURN_DATA->row['TOTAL_QTYX'];								
								 echo'<br>'.$TGL_RETOKO = $fce->RETURN_DATA->row['TGL_RETOKO'];
							     echo'<br>'.$JAM_RETOKO = $fce->RETURN_DATA->row['JAM_RETOKO'];
								 echo'<br>'.$SHIP_TO_CODE = $fce->RETURN_DATA->row['SHIP_TO_CODE'];
								 echo'<br>'.$SHIP_TO_PARTY = $fce->RETURN_DATA->row['SHIP_TO_PARTY'];
				$s++;
				   	echo'<br>'.	$removeEvent = "delete from rfid_mandiri where no_shipment='".$NO_SHIPMENT."'";
                                $rstmt = OCIParse($conn, $removeEvent);
                                OCIExecute($rstmt, OCI_DEFAULT);
                                OCI_Commit($conn);
                               // OCILogoff($conn);


								$q_stock= "insert into rfid_mandiri(nmorg,nmplant,no_transaksi,posnr,no_shipment,total_qty,no_polisi,tgl_cmplt,jam_cmplt,no_expeditur,nama_expeditur,sal_group,nmsal_group,sal_office,nmsal_office,kode_kecamatan,nama_kecamatan,total_qtyx,tgl_retoko,jam_retoko,ship_to_code,ship_to_party,last_update) values ('".$NMORG."','".$NMPLAN."','".$NO_TRANSAKSI."','".$POSNR."','".$NO_SHIPMENT."','".$TOTAL_QTY."','".$NO_POLISI."',to_date('".$TGL_CMPLT."','YYYY/MM/DD'),'".$JAM_CMPLT."','".$NO_EXPEDITUR."','".$NAMA_EXPEDITUR."','".$SAL_GROUP."','".$NMSAL_GROUP."','".$SAL_OFFICE."','".$NMSAL_OFFICE."','".$KODE_KECAMATAN."','".$NAMA_KECAMATAN."','".$TOTAL_QTYX."','".$TGL_RETOKO."','".$JAM_RETOKO."','".$SHIP_TO_CODE."','".$SHIP_TO_PARTY."',to_date('".$tgl2."','YYYY/MM/DD'))";

					echo'<br>qwert'.$q_stock;
		                        $query = oci_parse($conn,$q_stock);
		                        $status = oci_execute($query);
		                                  oci_commit($conn);

								if($query) echo "SIP";
								else echo "XXXX"; 

		}
		}else
        		$fce->PrintStatus();
				echo'<br>qwert'.$q_stock;

		$fce->Close();	
		$sap->Close();	
		$total=count($nopol);	


?>


