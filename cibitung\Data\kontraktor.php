<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$page="kontraktor.php";
$NAMA_KON = $_POST['nama'];
$CONTACT = $_POST['contact'];

if(isset($_POST['cari'])){
		$sql= "SELECT * FROM M_KONTRAKTOR WHERE NM_KONTRAKTOR='".$NAMA_KON."' AND CONTACT_P='".$CONTACT."'";
		$query= oci_parse($conn, $sql);
	    oci_execute($query);
	while($row=oci_fetch_array($query)){
		$ID[]=$row['ID'];
		$NAMA[]=$row['NM_KONTRAKTOR'];
		$ALAMAT[]=$row['ALMT_KONTRAKTOR'];
        $CONTAC[]=$row['CONTACT_P'];
        $TELEPON[]=$row['TELEPON'];
		
	}
	$total=count($ID);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";
//}

}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Truk Sedang Dalam Perjalanan </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="500" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Truk Sedang Dalam Perjalanan </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>">
  <table width="500" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
	<tr width="174">
      <td class="puso">Nama </td>
      <td class="puso">:</td>
      <td><input type="text" id="nama" name="nama" value=""/>
	  </tr>
		<tr width="174">
      <td class="puso">contact </td>
      <td class="puso">:</td>
      <td><input type="text" id="contact" name="contact" value=""/>
	 </tr>
     <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<table width="500" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Truk Sedang Dalam Perjalanan</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="500" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		 <td align="center"><strong>namat</strong></td>
		<td align="center"><strong >alamat</strong></td>
		<td align="center"><strong >contact</strong></td>
		<td align="center"><strong >telpon</strong></td>
	  </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}
		
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $NAMA[$i]; ?></td>
		<td align="left"><? echo $ALAMAT[$i]; ?></td>
		<td align="left"><? echo $CONTAC[$i]; ?></td>
		<td align="left"><? echo $TELEPON[$i]; ?></td>

		</tr>
	  <? } ?>
	 	  
	</table>	
	 <? } ?>
</body>
</html>