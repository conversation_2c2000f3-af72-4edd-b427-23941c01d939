<?
                    require_once "Mail.php"; 
					$from = "Approval<<EMAIL>>";
					$to = "<EMAIL>";
					$subject = "Approval";
					$body = "Anda mendapatkan nomor evaluasi : ".$nomorid." \n\n Segera di approve yah. http://***********/dev/sd/sdonline/loginbb.php?idbb=".$nomorid;

					$host = "sg2.sggrp.com";
					// $username = "";
					//$password = "";

					$headers = array ('From' => $from,
					'To' => $to,
					'Subject' => $subject);
					$smtp = Mail::factory('smtp',array ('host' => $host,'auth' => false));
					// 'username' => $username,
					// 'password' => $password));

					$mail = $smtp->send($to, $headers, $body);

					if (PEAR::isError($mail)) {
					echo("<p>" . $mail->getMessage() . "</p>");
					} else {
				("<p>Message successfully sent!</p>");
					}
?>