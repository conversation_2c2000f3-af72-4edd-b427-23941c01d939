<?
session_start();
include ('../include/crm_fungsi.php');
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$data_set = array();
$currentPage="cari_produk.php";
$nourut=$_GET['nourut'];

if(isset($_POST['nama'])){
$nama = $_REQUEST['nama'];
    if($nama!=""){
        $sql="select * from PT_MASTER_SEMEN where NAMA_SEMEN like '%$nama%'";
} else {
        $sql="select * from PT_MASTER_SEMEN";
}
        $query= oci_parse($conn, $sql);
	oci_execute($query);
        
	while($row=oci_fetch_array($query)){
                $matnr[]	= $row["KODE_SEMEN"];
                $nama_matnr[]	= $row["NAMA_SEMEN"];
                $uom[]		= $row["UOM"];
        }
}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
<!-- Style css jquery table sorter -->
<link href="../Templates/css-sorter/jquery-sorter-style.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../Templates/css-sorter/jquery-latest.js"></script>
<script type="text/javascript" src="../Templates/css-sorter/jquery.js"></script>
<script type="text/javascript">
	$(document).ready(function() 
    { 
        $("#myTable").tablesorter({widthFixed: true, widgets: ['zebra']});
			//.tablesorter( {sortList: [[0,0], [1,0]]} ); 
    } 
	); 
</script>
<!-- end Style css jquery table sorter -->
<script>
<!--
function setForm() {
	var btn = document.getElementById("cekdata"); 
	var urut=<?=$nourut?>;
	if(btn.value != 0){
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var acc_um='acc_um'+kenya;
	var komponen_acc_um=document.getElementById(acc_um); 
	opener.document.getElementById("produk"+urut).value = komponen_acc_id.value;
	opener.document.getElementById("nama_produk"+urut).value = komponen_acc_no.value;
	opener.document.getElementById("uom"+urut).value = komponen_acc_um.value;
    self.close();
	}else
		{
			alert('Pilih Data Produk Dahulu')
			return false;
		}
}
//-->
</script>
<script> 
function checkForother(obj) {  
	if (!document.layers) { 
	var kenya=obj.value;
	var btn = document.getElementById("cekdata"); 
	btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	} 
} 

function checkForother_db(obj) {  
	//if (!document.layers) { 
	//var kenya=obj.value;
	//var btn = document.getElementById("cekdata"); 
	//btn.value = kenya;
    //opener.document.dataformkaryawan.relation_karyawan.value = btn.value;
	//} 
	var btn = document.getElementById("cekdata"); 
	var urut=<?=$nourut?>;
	if(btn.value != 0){
	var kenya=btn.value;
	var acc_no='acc_no'+kenya;
	var komponen_acc_no=document.getElementById(acc_no); 
	var acc_id='acc_id'+kenya;
	var komponen_acc_id=document.getElementById(acc_id); 
	var acc_um='acc_um'+kenya;
	var komponen_acc_um=document.getElementById(acc_um); 
	opener.document.getElementById("produk"+urut).value = komponen_acc_id.value;
	opener.document.getElementById("nama_produk"+urut).value = komponen_acc_no.value;
	opener.document.getElementById("uom"+urut).value = komponen_acc_um.value;
    self.close();
	}else
		{
			alert('Pilih Data Produk Dahulu')
			return false;
		}

} 
</script> 

<style type="text/css">
<!--
#Layer1 {
	position:absolute;
	width:795px;
	height:115px;
	z-index:0;
	left: 159px;
	top: 296px;
}
.style5 {color: #791800}
-->
</style>
<head>

<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Daftar Data Produk</title>
</head>

<body>
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Daftar Produk Semen</th>
</tr></table></div>

<form  id="form1" name="form1" method="post" action="<? $currentPage;?>">
		<table width="800" align="center" class="adminform">
		<tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
		  <td>&nbsp;</td>
		  </tr>
		<tr>
		<td width="173" class="puso">Nama Produk </td>
		<td width="26" class="puso">:</td>
		<td width="585"><input name="nama" type="text" class="" value="<? echo $nama; ?>" size="40"/>
		&nbsp;&nbsp;</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td rowspan="2">
		<input name="Submit" type="submit" class="button" value="Show" />		</td>
		</tr>
		<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
		</table>
</form>
<?
if(isset($_POST['nama'])){
$total=count($matnr);
	if($total>0){
?>
		<p></p>
		<div align="center">
			<table width="600" align="center" class="adminlist">
			<tr>
			<th align="left" colspan="4"> <span class="style5">&nbsp;Tabel Data Produk </span> </th>
			</tr>
			</table>
		</div> 
		<div align="center">
			<form  name="formKaryawan">
				<span id="zebrax">
					<table id="myTable" width="600" align="center" class="pickme">
					<thead >
						<tr class="quote">
							<th width="40"><div align="center"><strong>&nbsp;&nbsp; Cek.&nbsp;&nbsp;&nbsp;</strong></div></th>
							<th align="center"><strong>Kode Produk</strong></th>
							<th align="center"><strong>Nama Produk </strong></th>
							<th align="center"><strong>Satuan </strong></th>
						</tr>
					</thead>
					<tbody >
				<?  for($i=0; $i<$total;$i++) {
						$b=$i+1;
						$acc_id="acc_id".$b;
						$acc_no="acc_no".$b;
						$acc_um="acc_um".$b;
						?>
						<tr>
						<td align="center"><input name="radiokaryawan" type="radio" value="<?=$b?>" onChange="checkForother(this)" id="<?=$b?>" onDblClick="checkForother_db(this)"/>
			<input id="<?=$acc_id;?>" name="<?=$acc_id;?>" type="hidden" value="<?=$matnr[$i]?>" />
			<input id="<?=$acc_no;?>" name="<?=$acc_no;?>" type="hidden" value="<?=$nama_matnr[$i]?>" />
			<input id="<?=$acc_um;?>" name="<?=$acc_um;?>" type="hidden" value="<?=$uom[$i]?>" /></td>    
						<td align="center"><? echo $matnr[$i]; ?></td>
						<td align="left"><? echo $nama_matnr[$i]; ?></td>
						<td align="left"><? echo $uom[$i]; ?></td>
						</tr>
				<? } ?>
					</tbody>
					</table>
				</span>
			<input type="button" value="Oke" name="kartu" class="button" onClick="setForm()">
			<input id="cekdata" name="cekdata" type="hidden" value="0" />
			</form>
		</div>

	<?
	}else $komen = " Maaf.. <br> Tidak Ada Data Yang Di Temukan..";	
	?>

<div align="center">
	<p>&nbsp;</p>
	<br />
	<?
	echo $komen;
	}
	?>
</div>
<p>&nbsp;</p>
</p>

<? include ('../include/ekor.php'); ?>

</body>
</html>
