<?
session_start();
include ('../include/crm_fungsi3.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();


//$fungsi=new ex_fungsi();
//$conn=@$fungsi->ex_koneksi();
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($_SESSION['distr_id']);
$halaman_id='1423';

//if ($fungsi->keamanan($halaman_id,$user_id)==0) { 
?><!--
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				
				</SCRIPT>

	 <a href="../index.php">Login....</a>-->
<?

// exit();
//}

if(isset($_POST['cari'])){

       // $tgl1 = $_POST['tgl1'];
        $tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;

$currentPage="print_invoice.php";
$no_invoice= $_POST['invice']; //$_REQUEST['no_invoice'];
$tanggal_cetak= date('d-m-Y');
$tahun = date("Y");
$bulan = date("M");


     $sqltermin1= "SELECT 'AWAL' as kodeawal, min(to_char(TANGGAL_KIRIM,'DD-MON-YY')) as TANGGAL_KIRIM1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '".$no_invoice."'";
	$querytermin1= oci_parse($conn, $sqltermin1);
	oci_execute($querytermin1);
	while($row1=oci_fetch_array($querytermin1)){
      $KODEAWAL=$row1['TANGGAL_KIRIM1'];
	

	 $sqltermin2= "SELECT 'AKHIR' as kodeakhir, max(to_char(TANGGAL_KIRIM,'DD-MON-YY')) as TANGGAL_KIRIM1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '".$no_invoice."'";
	$querytermin2= oci_parse($conn, $sqltermin2);
	oci_execute($querytermin2);
	while($row2=oci_fetch_array($querytermin2)){
      $KODEAKHIR=$row2['TANGGAL_KIRIM1'];
	


    $sqltermin= "select * from ex_invoice where no_invoice='".$no_invoice."'";
	$querytermin= oci_parse($conn, $sqltermin);
	oci_execute($querytermin);
	while($row=oci_fetch_array($querytermin)){
         $termin=$row['TERMIN'];
		 $tgl_britaacara=$row['TG_BA'];
	}
  }
}
if  ($tglm<=0) {
           ?>
				<SCRIPT LANGUAGE="JavaScript">
				
					alert("TGL Harus Di Isi ");
			
				</SCRIPT>	           
          <?
               exit();
         }
else{

if ($termin==1)
       {
	    $tanggal_mulai_sql='1-'.$bulan.'-'.$tahun;
	    $tanggal_selesai_sql='10-'.$bulan.'-'.$tahun;
		}
	ELSEif ($termin==2)
	   {
	    $tanggal_mulai_sql='11-'.$bulan.'-'.$tahun;
	   	$tanggal_selesai_sql='20-'.$bulan.'-'.$tahun;
		}
	ELSEif ($termin==3)
	   {
		$tanggal_mulai_sql='21-'.$bulan.'-'.$tahun;
		$tanggal_selesai_sql=$jumHari.'-'.$bulan.'-'.$tahun;
		}
	ELSEif ($termin==4)
	   {
		$tanggal_mulai_sql='01-'.$bulan.'-'.$tahun;
		$tanggal_selesai_sql=$jumHari.'-'.$bulan.'-'.$tahun;
		}
	ELSEIF ($termin <= 0) {
           ?>
				<SCRIPT LANGUAGE="JavaScript">
				
					alert("No Invoice Belmua Ada Termin");
			
				</SCRIPT>	           
          <?
               exit();
         }
		
}

               
$sql1= "select to_char(to_date('$tgl1','dd-MM-yyyy'),'DAY,DD MONTH YYYY, DD-MM-YYYY','nls_date_language = INDONESIAN') as TGL from dual";
$query1= oci_parse($conn, $sql1);
oci_execute($query1);
$row1=oci_fetch_array($query1);
$tgl1=$row1[TGL];
$tgl1 = explode(',', $tgl1);
$hari = ucwords($tgl1[0]);
$tgl_format1 = $tgl1[1];
$tgl_format2 = $tgl1[2];


$sql= "SELECT EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MON-YY') as TANGGAL_KIRIM1,to_char(TANGGAL_BONGKAR,'DD-MM-YYYY HH24:MI') as TANGGAL_BONGKAR1,to_char(TANGGAL_DATANG,'DD-MM-YYYY HH24:MI') as TANGGAL_DATANG1 FROM EX_TRANS_HDR WHERE DELETE_MARK = '0' AND NO_INVOICE = '$no_invoice' ORDER BY PLANT,SAL_DISTRIK, KODE_KECAMATAN, KODE_PRODUK ,TANGGAL_KIRIM ASC";

	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
        $row[NO_INVOICE];
		$no_invoice_v[]=$row[NO_INVOICE];
		$no_invoice_ex_v=$row[NO_INV_VENDOR];
		$spj_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM1];
		$tgl_datang_v[]=$row[TANGGAL_DATANG1];
		$tgl_bongkar_v[]=$row[TANGGAL_BONGKAR1];
		$produk_v[]=$row[KODE_PRODUK];
		$nama_produk_v[]=$row[NAMA_PRODUK];
		$shp_trn_v[]=$row[NO_SHP_TRN];
		$plant_v[]=$row[PLANT]; 
		$nama_plant_v[]=$row[NAMA_PLANT]; 
		$warna_plat_v=$row[WARNA_PLAT]; 
		$nama_vendor_v[]=$row[NAMA_VENDOR]; 
		$vendor_v[]=$row[VENDOR]; 
		
		$sal_dis_v[]=$row[SAL_DISTRIK]; 
		$nama_sal_dis_v[]=$row[NAMA_SAL_DIS]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$um_rez=$row[UM_REZ];
		if($um_rez > 1)
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ]/1000;
		else
		$qty_ton_v[]=$row[QTY_SHP]*$row[UM_REZ];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$no_pol_v[]=$row[NO_POL];
        $shp_cost_v[]=$row[SHP_COST];  
		$total_klaim_all_v[]=$row[TOTAL_KLAIM_ALL];  
		$no_pajak_ex=$row[NO_PAJAK_EX];  		
		$kel=$row[KELOMPOK_TRANSAKSI];  		
		$inco=$row[INCO];  		
		$nama_kapal=$row[NAMA_KAPAL];  		
		$kode_kecamatan[]=$row[KODE_KECAMATAN];
                $tipe_truk[]=$row[VEHICLE_TYPE];
                
                //Penyederhanaan Lap.OA @t 6 Feb 2012
                $item_no = trim($row[KODE_PRODUK]);                
                #Klaim Kantong
                $arr_klaim_kantong[$item_no]+=($row[TOTAL_KTG_RUSAK]+$row[TOTAL_KTG_REZAK]);  
                #Klaim Semen
		        $arr_klaim_semen[$item_no]+=($row[TOTAL_SEMEN_RUSAK]+$row[PDPKS]);                
                $arr_nama_material[$item_no] = $row[NAMA_PRODUK];
                
                #Qty.
                $arr_qty_klaim_kantong[$item_no]+=$row[QTY_KTG_RUSAK];
                $arr_qty_klaim_semen[$item_no]+=$row[QTY_SEMEN_RUSAK];        
        }		
		        $tglm = $_POST['tgl1'];
		        list($day,$month,$year)=split("-",$tglm);
	        	$tglm=$day.$month.$year;
		        $total=count($no_invoice_v);

			    $update1= "UPDATE ex_invoice SET TGL_BA = to_date('".$tglm."','DD/MM/YYYY')WHERE NO_INVOICE='".$no_invoice."'";
				$query_update1= oci_parse($conn, $update1);
				$hasil_update1 = oci_execute($query_update1);

}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Berita Acara PT.Semen Gresik, Tbk. Copyright&copy;<?=date("Y");?></title>
<style type="text/css">
<!--
.style1 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}
.style2 {font-family: Arial, Helvetica, sans-serif; font-size: 12; }
.style3 {font-size: 12}
-->
</style>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<?
	if($total<1){
?>
<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Brita Acara </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice </td>
      <td class="puso">:</td>
      <td><input type="text" id="invice" name="invice" value="<?=$no_spj?>"/>
	  <input type="hidden" id="org" name="org" value="<?=$user_org?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Tanggal Berita Acara</td>
      <td  class="puso">:</td>
      <td>><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/></td>
    </tr>    
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<?
	if($total>0){
?>
    <br>
    <th><center><div align="center" class="style2">BERITA ACARA PENYELESAIAN PENGIRIMAN SEMEN</div></center></th>
    <br>
	
	<table width="800" align="center" onload="javascript:window.print();">
	<tr><td><span class="style2">Pada hari <?=$hari?>, tanggal <?=$tgl_format1?>, telah selesai dilakukan pengiriman semen PT. Semen Gresik (persero) Tbk, periode tanggal <?ECHO $KODEAWAL;?> s.d <?ECHO $KODEAKHIR;?> sebagaimana data dibawah ini.</span</td></tr>	
	</table>
	<table width="800" align="center" border="0">
        <tr>
            <td style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
	</tr>
	<tr>
            <td style=" border-bottom:1px #000000 solid;"><span class="style2">No.</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">Tgl.SPJ</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">No. SPJ</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">No.Pol</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">Kota</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">Ekspeditur</span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2">Qty</span></td>
	</tr>
        <?
        $n = count($shp_trn_v);
        for($i=0;$i<$n;$i++){
        $no = $i+1;
        ?>
        <tr>
            <td align="center"><span class="style2"><?=$no?></span></td>
            <td align="center"><span class="style2"><?=$tgl_kirim_v[$i]?></span></td>
            <td align="center"><span class="style2"><?=$shp_trn_v[$i]?></span></td>
            <td align="left"><span class="style2">&nbsp;<?=$no_pol_v[$i]?></span></td>
            <td align="left"><span class="style2">&nbsp;<?=$nama_sal_dis_v[$i]?></span></td>
            <td align="left"><span class="style2">&nbsp;<?=$nama_vendor_v[$i]?></span></td>
            <td align="right"><span class="style2"><?=number_format($qty_v[$i],0,',','.')?></span></td>
	</tr>
	<?
        }
        ?>
        <tr>
            <td style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
            <td align="center" style=" border-bottom:1px #000000 solid;"><span class="style2"></span></td>
	</tr>
        </table>
        
        <table width="800" align="center">
	<tr><td><br><span class="style2">Demikian Berita Acara ini dibuat untuk dopergunakan semestinya.</span></td></tr>	
	</table>        
	<?
	$sqlok= "select * from ex_set_cetak";
	$queryok= oci_parse($conn, $sqlok);
	oci_execute($queryok);
	while($row=oci_fetch_array($queryok)){
         $nama=$row[KEPALA];
	}
	?>
    
    	<table width="800" align="center" border="0">
	<tr>
            <td></td>
            <td width="300"></td>
            <td><span class="style2">Gresik, <?=$tgl_format1?></span></td>
	</tr>
	<tr>
	     <td colspan="3"><br><br><br></td>
	</tr>	
        <tr>
            <td><span class="style2">(<?ECHO $nama;?>)</span></td>
            <td width="300"></td>
            <td><span class="style2">(-------------------------------------)</span></td>
	</tr>
	<tr>
            <td><span class="style2">Seksi Adm. Distr & Transportasi</span></td>
            <td></td>
            <td><span class="style2">Ekspeditur</span></td>
	</tr>
	</table>
	<? } ?>
</body>
</html>
