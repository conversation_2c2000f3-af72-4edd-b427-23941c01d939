<?
 session_start();
	include ('crm_fungsi1.php');
	include ('include/validasi.php'); 
	$fungsi=new crm_fungsi();
	$conn=$fungsi->crm_koneksi();

	$sap = new SAPConnection();
		$sap->Connect("../include/sapclasses/logon_dataprod.conf");
		if ($sap->GetStatus() == SAPRFC_OK )$sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		  $sap->PrintStatus();
		  exit;
		}

		$fce = &$sap->NewFunction ("Z_ZAPPSD_UNDIANCURAH");
		if ($fce == false ) {
		  echo $sap->PrintStatus();
		  exit;
		}
		
		$tglstock = date('Ymd');
		$fce->SO_TGL_CMPLT_TO ='20111004';
		$fce->SO_TGL_CMPLT_TO ='20111004';
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->IT_DATA->Reset();
			$s=0;
			while ( $fce->IT_DATA->Next() ){
				$NMORG[$s] = $fce->IT_DATA->row["NMORG"];
				$NMPLAN[$s] = $fce->IT_DATA->row["NMPLAN"];
				$SOLD_TO_CODE[$s] = $fce->IT_DATA->row["SOLD_TO_CODE"];
				$SOLD_TO_PARTY[$s] = $fce->IT_DATA->row["SOLD_TO_PARTY"];
				$SHIP_TO_CODE[$s] = $fce->IT_DATA->row["SHIP_TO_CODE"];
				$SHIP_TO_PARTY[$s] = $fce->IT_DATA->row["SHIP_TO_PARTY"];
				$ORT01[$s] = $fce->IT_DATA->row["ORT01"];
				$STRAS[$s] = $fce->IT_DATA->row["STRAS"];
				$TELF1[$s] = $fce->IT_DATA->row["TELF1"];
				$ITEM_QTY_ACT[$s] = $fce->IT_DATA->row["ITEM_QTY_ACT"];
	    		$TGL_CMPLT[$s] = $fce->IT_DATA->row["TGL_CMPLT"];
				$DISTRIK[$s] = $fce->IT_DATA->row["DISTRIK"];
				$BZTXT[$s] = $fce->IT_DATA->row["BZTXT"];
				
	
				
				$or_username = "APPSGG";
				$or_password = "sgmerdeka99";
				$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = cmsdb.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';				
				$conn = oci_connect($or_username, $or_password, $or_db);				
					echo $q_stock ="INSERT INTO PT_RELEASE_CURAH1 (NMOR, KODE_PLANT, KUNNR, NAMA_KUNNR, NO_TOKO, NAMA_TOKO, TOTAL_VALUME, JML_UNDIAN, STATUS, DELETE_FLAX, ALAMAT, TGL_RELEASE, TGL_CREATE,DISTRIK,NAMA_KOTA) values ('".$NMORG[$s]."','".$NMPLAN[$s]."','".$SOLD_TO_CODE[$s]."','".$SOLD_TO_PARTY[$s]."','".$SHIP_TO_CODE[$s]."','".$SHIP_TO_PARTY[$s]."','".$ITEM_QTY_ACT[$s]."',0,0,0,'".$STRAS[$s]."','".$TGL_CMPLT[$s]."','".$tglstock."','".$DISTRIK[$s]."','".$BZTXT[$s]."')";
				$stid = oci_parse($conn,$q_stock);
				oci_execute($stid);
				$s++;
				
		       		
			}
			print_r($fce);
		}

	//}

//	exit;

		
?> 