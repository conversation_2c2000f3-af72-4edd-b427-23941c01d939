var doghnutChartStatTag;
$(document).ready(function() {
  var resultTag;
  var statTagDate1 = $("#statTagDate1").val();
  var statTagDate2 = $("#statTagDate2").val();
  var company = $("#company").val();
  $.ajax({
    url: 'xhr/model_dist.php?p=doghnut-stat-tag',
    type: 'GET',
    dataType: 'JSON',
    data: {
      statTagDate1 : statTagDate1,
      statTagDate2 : statTagDate2,
      company: company
    },
    beforeSend: function(){
      $("#loadingStatTag").fadeIn(1000);
      $("#terTag").hide();
    }
  })
  .done(function() {
    resultTag = true;
  })
  .fail(function() {
    resultTag = false;
  })
  .always(function(data) {
    $("#loadingStatTag").fadeOut(1000, function(){
      $("#terTag").show(1000);
      if(resultTag){
        var config = {
            type: 'doughnut',
            data: data.grap,
            options: {
                responsive: true,
                legend: {
                  position: 'bottom'
                },
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Status Penerimaan'
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                },
                tooltips: {
                 callbacks: {
                     label: function(tooltipItem, data) {
                     var dataset = data.datasets[tooltipItem.datasetIndex];
                     var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                       return previousValue + currentValue;
                     });
                     var currentValue = dataset.data[tooltipItem.index];
                     var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                     return precentage + "%";
                   }
                 }
               },
               elements: {
                 arc: {
                   borderWidth: 0
                 }
               }
            }
        };
        var ctx = document.getElementById("doghnutStatTag").getContext("2d");
        doghnutChartStatTag = new Chart(ctx, config);

        if(data.tenDetail.length < 1){
          var contentStatTag = "<tr>"+
                                  "<td colspan='3' class='text-center' style='font-size:13px;'>NO DATA</td>"+
                               "</tr>";
          $("#detailStatTag").find('tbody').append(contentStatTag);
        } else{
          $.each(data.tenDetail, function(index, data){
            var contentStatTag = "<tr>"+
                                    "<td style='font-size:13px;'>"+data.NO_INVOICE+"</td>"+
                                    "<td style='font-size:13px;'>"+data.NAMA_VENDOR+"</td>"+
                                    "<td style='font-size:13px;'>"+data.TGL_INVOICE+"</td>"+
                                 "</tr>";
             $("#detailStatTag").find('tbody').append(contentStatTag);
          })
        }
      } else{
        alert("Internal Server Error.. Sorry");
      }
    });
  });
});

$("#reloadTargetBulanan").on("submit", function(e){
  e.preventDefault();
  doghnutChartStatTag.destroy();
  $("#detailStatTag").find('tbody tr').remove();
  var resultTag;
  var statTagDate1 = $("#statTagDate1").val();
  var statTagDate2 = $("#statTagDate2").val();
  var company = $("#company").val();
  $.ajax({
    url: 'xhr/model_dist.php?p=doghnut-stat-tag',
    type: 'GET',
    dataType: 'JSON',
    data: {
      statTagDate1 : statTagDate1,
      statTagDate2 : statTagDate2,
      company: company
    },
    beforeSend: function(){
      $("#loadingStatTag").fadeIn(1000);
      $("#terTag").hide();
    }
  })
  .done(function() {
    resultTag = true;
  })
  .fail(function() {
    resultTag = false;
  })
  .always(function(data) {
    $("#loadingStatTag").fadeOut(1000, function(){
      $("#terTag").show(1000);
      if(resultTag){
        var config = {
            type: 'doughnut',
            data: data.grap,
            options: {
                responsive: true,
                legend: {
                  position: 'bottom'
                },
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Status Penerimaan'
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                },
                tooltips: {
                 callbacks: {
                     label: function(tooltipItem, data) {
                     var dataset = data.datasets[tooltipItem.datasetIndex];
                     var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                       return previousValue + currentValue;
                     });
                     var currentValue = dataset.data[tooltipItem.index];
                     var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                     return precentage + "%";
                   }
                 }
               },
               elements: {
                 arc: {
                   borderWidth: 0
                 }
               }
            }
        };
        var ctx = document.getElementById("doghnutStatTag").getContext("2d");
        doghnutChartStatTag = new Chart(ctx, config);
        if(data.tenDetail.length < 1){
          var contentStatTag = "<tr>"+
                                  "<td colspan='3' class='text-center' style='font-size:13px;'>NO DATA</td>"+
                               "</tr>";
          $("#detailStatTag").find('tbody').append(contentStatTag);
        } else{
          $.each(data.tenDetail, function(index, data){
            var contentStatTag = "<tr>"+
                                    "<td style='font-size:13px;'>"+data.NO_INVOICE+"</td>"+
                                    "<td style='font-size:13px;'>"+data.NAMA_VENDOR+"</td>"+
                                    "<td style='font-size:13px;'>"+data.TGL_INVOICE+"</td>"+
                                 "</tr>";
            $("#detailStatTag").find('tbody').append(contentStatTag);
          })
        }
      } else{
        alert("Internal Server Error.. Sorry");
      }
    });
  });
})
