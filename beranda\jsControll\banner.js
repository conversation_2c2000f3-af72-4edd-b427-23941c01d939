$(document).ready(function() {
  var approveDate1 = $("#approveDate1").val();
  var approveDate2 = $("#approveDate2").val();
  var company = $("#company").val();
  reqBanner(approveDate1, approveDate2, company);
});

$("#reloadApprove").on("submit", function(e){
  var approveDate1 = $("#approveDate1").val();
  var approveDate2 = $("#approveDate2").val();
  var company = $("#company").val();
  e.preventDefault();
  reqBanner(approveDate1, approveDate2, company);
})

$(".toDetail").click(function(){
  var result = $(this).closest('.approve').find('.inner h3').text();
  if(result == "0")
  {
    $(this).removeAttr('data-target');
    $(this).removeAttr('data-toggle');
    toastr.options = {
      "closeButton": false,
      "debug": false,
      "newestOnTop": false,
      "progressBar": false,
      "positionClass": "toast-top-right",
      "preventDuplicates": false,
      "onclick": null,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "1000",
      "extendedTimeOut": "1000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    }
    toastr["warning"]("Tidak ada data yg akan di tampilkan");
  } else {
    $(this).attr('data-target', '#modalDetailBanner');
    $(this).attr('data-toggle', 'modal');
  }
})

$("#modalDetailBanner").on("show.bs.modal", function(e){
  var title = $(e.relatedTarget).data("title");
  var icon = $(e.relatedTarget).data("icon");
  var company = $("#company").val();
  $(this).find('.modal-title').html("<i style='margin-right: 10px;' class='"+icon+"'></i>"+modalTittle(title));
  var periode = $("#_callbackPeriode").val();
  var result;
  $("#detailTable").find('tbody tr').remove();
  $.ajax({
    url: 'xhr/model.php?p='+title,
    type: 'GET',
    dataType: 'JSON',
    data: {
      periode: periode,
      company: company
    },
    beforeSend: function(){
      $("#loadingModal").show();
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    if(result){
      setTimeout(function () {
        $("#loadingModal").fadeOut('slow', function() {
          if(data.length > 0){
            var i = 1;
            $.each(data, function(index, detail){
              var content = "<tr style='text-align:center;'>"+
                               "<td>"+i+"</td>"+
                               "<td>"+detail.NO_INVOICE+"</td>"+
                               "<td>"+detail.TGL_INVOICE+"</td>"+
                               "<td>"+detail.NAMA_VENDOR+"</td>"+
                            "<tr>";
              $("#detailTable").find('tbody').append(content);
              i++;
            })
          } else{
            var content = "<tr>"+
                             "<td colspan='4' style='text-align:center;'>Data Empty </td>"+
                          "<tr>";
            $("#detailTable").find('tbody').append(content);
          }
        });
      }, 2000);
    }
  });

})

function modalTittle(title)
{
  switch (title) {
    case 'blmApprove': return "Belum Approve"; break;
    case 'approveKasi': return "Approve Kasi"; break;
    case 'approveKabiro': return "Approve Kabiro"; break;
    case 'approved': return "Approved"; break;
    default: 'Error..';
  }
}

function reqBanner(date1, date2, company)
{
  var resultApprove;
  $.ajax({
    url: 'xhr/model.php?p=banner-approve',
    type: 'GET',
    dataType: 'JSON',
    data: {
      approveDate1: date1,
      approveDate2: date2,
      company: company
    },
    beforeSend: function(){
      $(".banner-approve").fadeIn(1000);
      $(".approve").hide();
    }
  })
  .done(function() {
    resultApprove = true;
  })
  .fail(function() {
    resultApprove = false;
  })
  .always(function(data) {
    $(".banner-approve").fadeOut(1000, function() {
      $(".approve").show(1000);
      if(resultApprove){
        $("#_callbackPeriode").val(data.tglPeriode);
        $("#blmapprove h3").html(data.data.BLMAPPROVE);
        $("#kasiapprove h3").html(data.data.KASIAPPROVE);
        $("#kabiroapprove h3").html(data.data.KABIROAPPROVE);
        $("#approved h3").html(data.data.KASIKABIROAPPROVE);
      }else{
        alert("Internal Server Error.. Sorry");
      }
    });
  });
}
