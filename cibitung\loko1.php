<? 
session_start();
//include ('include/crm_fungsi1.php');
include ('../include/validasi.php'); 
//$fungsi=new crm_fungsi();
//$conn=$fungsi->crm_koneksi();


mysql_connect("**********","sgg","sggroup"); //koneksi database
mysql_select_db("eis");

if(isset($_POST['cari'])){
	   
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

$perintah="SELECT kd_distr, nm_distr, kd_shipto,  nm_shipto,  kd_material,  nm_material, tgl_release, SUM(qty_do) AS total_release FROM crm_spj_dtl_logto where tgl_release BETWEEN '".$tglm."01' and '".$tgls."'  GROUP BY kd_distr,kd_shipto,kd_material";

 // echo $perintah;
	$hasil=mysql_query($perintah);
	if (mysql_num_rows($hasil) < 1) {
		echo (" Tabel Masih Kosong "); }
		else {  
		$hasil=mysql_query($perintah);
		while ($data=mysql_fetch_array($hasil)) {	
			$kd_Distr[]=$data['kd_distr'];
			$nama_Distr[]=$data['nm_distr'];
			$kd_shipto[]=$data['kd_shipto'];
			$nm_shipto[]=$data['nm_shipto'];
			$kode_material[]=$data['kd_material'];
			$Nama_Material[]=$data['nm_material'];
			$tgl_release[]=$data['tgl_release'];
			$total_release[]=$data['total_release'];	
	   }
 }

}   $total=count($tgl_release);
	//var_dump($tgl_release);
  
	
?>
<script language=javascript>
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
 <!-- JQuery -->
<script src="../master/jq_fixheader/jquery.js" type="text/javascript"></script>
<script src="../master/jq_fixheader/jquery_002.js" type="text/javascript"></script>	

<script type="text/javascript">
	$(function () {
            $(".adminlist").fixedtableheader({ highlightrow: true, headerrowsize: 2 });
        });   
</script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" align='center'>DAFTAR LOGISTIC & TOKO BANGUNAN ONLINE (SHIPTO).
 </th>
</tr></table></div>
<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search </th>
</tr>
</table>
</div>
<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Bulan</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("01-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<br />
<br />

  <div align="center">
	<table width="1000" align="center" class="adminlist" >
  	<tr>
    <tr class="quote">
	    <td align="center"><strong>&nbsp;&nbsp;No</strong></td>
		<td align="center"><strong>Nama Distributor</strong></td>
		<td align="center"><strong>Nama Shipto</strong></td>
		<td align="center"><strong>Nama Material</strong></td>
		<!--		<td align="center"><strong>Tanggal release</strong></td>
		<td align="center"><strong >Total Release</strong></td>-->
		
		<?
			for($n=1;$n<=31;$n++){
				echo "<td align='center'><strong>$n</strong></td>";
			}
		?>
		
	</tr>
	 <tr class="quote">		
  <?  
  		//$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		//$totaldo= $totaldo+$TOTAL_QTY[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
	?>
		<td align="center"><? echo $b."."?></td>
		<td align="left"><? echo $nama_Distr[$i]; ?></td>
		<td align="left"><? echo $nm_shipto[$i]; ?></td>
		<td align="left"><? echo $Nama_Material[$i]?></td>
	<!--	<td align="left"><? $thn=substr($tgl_release[$i],0,4);
							$bln=substr($tgl_release[$i],4,2);
							$hr=substr($tgl_release[$i],6,2);
							$tglcomplit=$hr.'-'.$bln.'-'.$thn;
							echo $tglcomplit;  ?></td>	
		<td align="right"><? echo number_format($total_release[$i],0,",","."); ?></td>-->

		<?
		for($n=1;$n<=31;$n++){
			$tgl = sprintf('%02d',$n);
			mysql_connect("**********","sgg","sggroup"); //koneksi database
            mysql_select_db("eis");
			$q="SELECT kd_distr, nm_distr, kd_shipto,  nm_shipto,  kd_material,  nm_material, tgl_release, SUM(qty_do) AS total FROM  crm_spj_dtl_logto 	where tgl_release='".$tglm.$tgl."' and kd_shipto='".$kd_shipto[$i]."' and kd_distr='".$kd_Distr[$i]."' group by kd_distr, kd_shipto, tgl_release";
			$r=mysql_query($q);
			//return $data=mysql_fetch_array($r);

			
			//echo "<td align='left'>".@mysql_result($r,0,'total')."</td>";
			//echo "<td align='left'>".number_format("total",0,",",".");."</td>";
			echo "<td align='left'>".@mysql_result($r,0,'total')."</td>";
			

		}
		   //var_dump($data);
		?>
		
		
		</tr>
	  <? } ?><!--
                <tr>
                    <td></td>
                    <td colspan="6"><strong >TOTAL</strong ></td>
                    <td align="right"><strong ><? echo number_format($totaldo,3,".",","); ?></strong ></td>
                                      
                </tr>-->
	</table>	
	<p>&nbsp;</p>
	</div>


<p>&nbsp;</p>
</p>
</body>
</html>
