<? 


	    $or_username = "DEVSD";
		$or_password = "gresik45";
		$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = dev-sggdata3.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = devsgg)(SERVER = DEDICATED)))';				
		$conn = oci_connect($or_username, $or_password, $or_db);

    $sql = "select * from semen_gresik_bersih";
	$query= oci_parse($conn, $sql);
	oci_execute($query);

	while($row=oci_fetch_array($query)){
		$nama[]=$row['NAMA'];
		$email[]=$row['EMAIL'];
        $notelpon[]=$row['NO_TELPON'];
        $keterangan[]=$row['KETERANGAN'];
	}
	$total=count($nama);

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Sales Order :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
</head>
<body>
    <div align="center">
	<table width="600" align="center" class="adminheading" border="0">
	<tr>
	<th class="kb2">Daftar Semen  Gresik Bersih </th>
	</tr></table></div> 
	<div align="center">
	<table width="1300" align="center" class="adminlist">
	<th align="left" colspan="4">Tabel Daftar Bersi-Bersi</th>
	</table>
	</div> 
	<div align="center">
	<table width="1300" align="center" class="adminlist" border='1'>
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >NAMA</strong></td>
		<td align="center"><strong >EMAIL</strong></td>
		<td align="center"><strong >NOTELPON</strong></td>
		<td align="center"><strong >KETERANGAN</strong></td>
      </tr >
  <?  for($i=0; $i<$total;$i++) {
	  $b=$i+1;
   ?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $nama[$i]; ?></td>
		<td align="left"><? echo $email[$i]; ?></td>
		<td align="left"><? echo $notelpon[$i]; ?></td>
		<td align="left"><? echo $keterangan[$i]; ?></td>
        </tr>
	  <? } ?>
</table>
</body>
</html>
