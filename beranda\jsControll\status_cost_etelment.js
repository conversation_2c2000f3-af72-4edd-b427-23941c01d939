// chart etelment;
var chartEtel;

$(document).ready(function() {
  var from = $("#from_etelment").val();
  var to = $("#to_etelment").val();
  var company = $("#company").val();
  reqChartEtelment(from, to, company);
});

$("#reloadStatEtel").on("submit", function(e){
  e.preventDefault();
  var from = $("#from_etelment").val();
  var to = $("#to_etelment").val();
  var company = $("#company").val();
  reqChartEtelment(from, to, company, "isready");
})

function reqChartEtelment(date1, date2, company, set = null)
{
  $("#tabelStatEtel").find('tbody tr').remove();
  var result;
  if(set == "isready"){
    chartEtel.destroy();
  }
  $.ajax({
    url: 'xhr/model.php?p=donat-etelment',
    type: 'GET',
    dataType: 'JSON',
    data: {
      from_etelment : date1,
      to_etelment : date2,
      company: company
    },
    beforeSend: function(){
      $("#loadingStatEtel").fadeIn(1000);
      $("#statEtel").hide();
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    if(result){
      $("#loadingStatEtel").fadeOut(1000, function() {
        $("#statEtel").show(1000);
        var config = {
          type: 'pie',
          data: data.chart,
          options: {
            responsive: true,
            legend: {
              position: 'bottom'
            },
            maintainAspectRatio: false,
            title:{
              display: true,
              text: 'Status Cost Etelment'
            },
            animation: {
              animateScale: true,
              animateRotate: true
            },
            tooltips:{
              callbacks:{
                label: function(tooltipItem, data) {
                  var dataset = data.datasets[tooltipItem.datasetIndex];
                  var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                    return previousValue + currentValue;
                  });
                  var currentValue = dataset.data[tooltipItem.index];
                  var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                  return precentage + "%";
                }
              }
            },
            elements: {
              arc: {
                borderWidth: 0
              }
            }
          }
        };
        var ctx = document.getElementById("chartStatEtel").getContext("2d");
        chartEtel = new Chart(ctx, config);

        $.each(data.detail, function(index, detail) {
          var noInv = (detail.NO_INVOICE == null) ? "KOSONG" : detail.NO_INVOICE;
          var content = "<tr>"+
                          "<td style='font-size:13px;'>"+noInv+"</td>"+
                          "<td style='font-size:13px;'>"+detail.NAMA_VENDOR+"</td>"+
                          "<td style='font-size:13px;'>"+detail.CREATE_DATE+"</td>"+
                        "</tr>";
          $("#tabelStatEtel").find('tbody').append(content);
        });
      });
    }
  });
}
