<?php

session_start();
//include ('../include/ex_fungsi.php');
//$fungsi=new ex_fungsi();
//$conn=$fungsi->ex_koneksi();

include ('../include/or_fungsi.php');
$fungsi     = new or_fungsi();
$conn       = $fungsi->or_koneksi();

$result = array();
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);

$input_query = htmlspecialchars($_REQUEST['input_query']);

$del = htmlspecialchars($_REQUEST['del']);

if(isset ($aksi)){
    switch($aksi) { 
        case 'qry' :
            {
                $sql = stripslashes($input_query);
                $query= oci_parse($conn, $sql);
                oci_execute($query);
                $result=array();
                $i=0;
                // @header('Content-Type: application/json');
                while($row=oci_fetch_array($query)){
                    // $result[$i]=$row;
                    print_r("<pre>".json_encode($result[$i]=$row)."</pre>");
                    echo "<br>";
                    $i++;
                }
                // @header('Content-Type: application/json');
                // echo "<pre>".json_encode($result)."</pre>";
            }
        break;
        case 'qry_show' :
            {
                displayData($conn);
            }
        break;
    }
}

function displayData($conn){
    $org = $_SESSION['user_org'];
    $user_name=$_SESSION['user_name'];
    if($conn){
        $sql = "SELECT
        ZMD_LEADTIME_SO.ID AS ID,
		ZMD_LEADTIME_SO.PLANT AS PLANT,
		RFC_Z_ZAPP_SELECT_SYSPLAN.NAME1 AS NAMA_PLANT,
		ZMD_LEADTIME_SO.KOTA AS KOTA,
		ZMD_LEADTIME_SO.STANDART_AREA AS STANDART_AREA,
		ZMD_LEADTIME_SO.KD_MATERIAL AS MATERIAL,		
		ZMD_LEADTIME_SO.PALLET AS PALLET,		
		PT_MASTER_DISTRIK.DISTRIK AS NAMA_KOTA,
        (CASE WHEN  ZMD_LEADTIME_SO.DELETE_MARK = '0' then 'Aktif'
        Else 'Nonaktif' End) as DEL
    FROM
		ZMD_LEADTIME_SO
    LEFT JOIN PT_MASTER_DISTRIK ON
        ZMD_LEADTIME_SO.KOTA = PT_MASTER_DISTRIK.KODE_DISTRIK
    LEFT JOIN RFC_Z_ZAPP_SELECT_SYSPLAN ON
    	ZMD_LEADTIME_SO.PLANT = RFC_Z_ZAPP_SELECT_SYSPLAN.WERKS 
    ORDER BY
		ZMD_LEADTIME_SO.ID DESC";
        $query= oci_parse($conn, $sql);
        oci_execute($query);
        $result=array();
        $i=0;
        while($row=oci_fetch_array($query)){
            $result[$i]=$row;
            $i++;
        }
        header('Content-Type: application/json');
        echo "<pre>".json_encode($result);
    }
}


?>



