<?
	$conn = @mysql_connect ("10.15.5.71","sgg","sggroup") or die ("koneksi gagal");
	mysql_select_db ("eis", $conn);

	include_once("sapfrc/sapclasses/sap.php");

	$sap = new SAPConnection();
	$sap->Connect("logon_data.conf");


	if ($sap->GetStatus() == SAPRFC_OK ) //$sap->Open ();
	   $sap->Open ();
	if ($sap->GetStatus() != SAPRFC_OK ) {
	   $sap->PrintStatus();
	   exit;
	}

	$fce = &$sap->NewFunction ("Z_ZAPPSD_MM_VALSTOCK");
	if ($fce == false ) {
	   $sap->PrintStatus();
	   exit;
	}
	
for ($j=1;$j<=31;$j++) {
	$comp = '2000';
	$n_days = 1;
	$tgl = sprintf("%02d", $j);
	$date='201101'.$tgl;
	$q_kode = mysql_query($q="select distinct(code) as kode from sap_t_description where sap_t_description.key='MATNR' and (code like '121-200%' or code like '121-301%' or code like '121-302%' or code like '130-100%')");//code like '121-200%'
	//echo $q;
	$i=0;
	while ($data = mysql_fetch_array($q_kode)) {
			//echo $data['kode']."<br>";
		$fce->I_BUKRS = '2000';
		$fce->I_WERKS ='2403';
		$fce->I_DATE = '20110923';//date('Ymd', strtotime("-".$n_days." days"));//
		$fce->I_DATE_TO = '20110923';//date('Ymd', strtotime("-".$n_days." days"));//
		$fce->I_MATNR = '121-301-0056';//$data['kode'];
		$fce->I_FLAG = '2'; 
		$fce->I_SUM ='X';
		$fce->Call();

				$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->T_RETURNDATA->Reset();
			$s=0;
			while ( $fce->T_RETURNDATA->Next() ){
				$WERKS[$s] = $fce->T_RETURNDATA->row["WERKS"];
				$NAME1[$s] = $fce->T_RETURNDATA->row["NAME1"];
				$MATNR[$s] = $fce->T_RETURNDATA->row["MATNR"];
				$MAKTX[$s] = $fce->T_RETURNDATA->row["MAKTX"];
				$MTART[$s] = $fce->T_RETURNDATA->row["MTART"];
				$MTBEZ[$s] = $fce->T_RETURNDATA->row["MTBEZ"];
				$START_STOCK[$s] = $fce->T_RETURNDATA->row["START_STOCK"];
				$START_STOCKX[$s] = $fce->T_RETURNDATA->row["START_STOCKX"];
				$REC_TOTAL[$s] = $fce->T_RETURNDATA->row["REC_TOTAL"];
				$REC_TOTALX[$s] = $fce->T_RETURNDATA->row["REC_TOTALX"];
				echo $ISSUE_TOTAL[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTAL"];
				echo $ISSUE_TOTALX[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTALX"];
				echo $END_STOCK[$s] = $fce->T_RETURNDATA->row["END_STOCK"];
				echo $END_STOCKX[$s] = $fce->T_RETURNDATA->row["END_STOCKX"];
					
			
                $conn = @mysql_connect ("10.15.5.71","sgg","sggroup") or die ("koneksi gagal");
				mysql_select_db ("eis", $conn);
				$q_stock ="INSERT INTO sap_total_stock_gd_penyangga (WERKS, NAME1, MATNR, MAKTX, MTAR, MTBEZ, START_STOCK, START_STOCKX, REC_TOTAL, REC_TOTALX ) values ('".$WERKS[$s]."','".$NAME1[$s]."','".$MATNR[$s]."','".$MAKTX[$s]."','".$MTART[$s]."','".$MTBEZ[$s]."','".$START_STOCK[$s]."','".$START_STOCKX[$s]."','".$REC_TOTAL[$s]."','".$REC_TOTALX[$s]."')";

				$s++;
				$hasil=mysql_query($q_stock);
				//perintah dilaksanakan
				if ($hasil) {
					echo "Success insert ".$s." Record for ".$tgl."<br>";
				} else {
					echo ("<br>Maaf input data gagal");
				}
		       		
			}
		}
	}
	
	

}
?>