<? 
session_start();
include ('../include/or_fungsi.php');
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');

$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();

$halaman_id=85;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];

/*
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
*/
  	function HeaderingExcel($filename) {
		header("Content-type: application/vnd.ms-excel");
		header("Content-Disposition: attachment; filename=$filename" );
		header("Expires: 0");
		header("Cache-Control: must-revalidate, post-check=0,pre-check=0");
		header("Pragma: public");
    }
 
//$action_page=$fungsi->security($conn,$user_id,$halaman_id);
$no_so = $fungsi->linenum($_POST['no_so']);
$no_spj = $fungsi->linenum($_POST['no_spj']);
$plant = $_POST['plant'];
$ship_to = $_POST['shipto'];
$produk = $_POST['produk'];
$status = $_POST['status'];
$tgl1 = $_POST['tgl1'];
$tgl2 = $_POST['tgl2'];

$currentPage="daftar_jualan_xls.php";
$komen="";

	if($no_spj=="" and $plant=="" and $status=="" and $sold_to == "" and $ship_to == "" and $produk == "" and $tgl1 == "" and $tgl2 == ""){
		$sql= "SELECT CRM_LIST_SPJ.*, to_char(TGL_TIMB1,'DD-MM-YYYY') as TGL_TIMB1,to_char(TGL_TIMB2,'DD-MM-YYYY') as TGL_TIMB2 FROM CRM_LIST_SPJ WHERE ORG='$user_org' ORDER BY NO_SO ASC";
	}else {
		$pakeor=0;
		$sql= "SELECT CRM_LIST_SPJ.*, to_char(TGL_TIMB1,'DD-MM-YYYY') as TGL_TIMB1,to_char(TGL_TIMB2,'DD-MM-YYYY') as TGL_TIMB2 FROM CRM_LIST_SPJ WHERE ORG='$user_org' AND ";
		if($no_spj!=""){
			if($pakeor==1){
			$sql.=" AND NO_SPJ LIKE '$no_spj' ";
			}else{
			$sql.=" NO_SPJ LIKE '$no_spj' ";
			$pakeor=1;
			}
		}
		if($status!=""){
			if($pakeor==1){
			$sql.=" AND STATUS LIKE '$status' ";
			}else{
			$sql.=" STATUS LIKE '$status' ";
			$pakeor=1;
			}
		}
		if($no_so!=""){
			if($pakeor==1){
			$sql.=" AND NO_SO LIKE '$no_so' ";
			}else{
			$sql.=" NO_SO LIKE '$no_so' ";
			$pakeor=1;
			}
		}
		if($plant!=""){
			if($pakeor==1){
			$sql.=" AND KD_GDG LIKE '$plant' ";
			}else{
			$sql.=" KD_GDG LIKE '$plant' ";
			$pakeor=1;
			}
		}
		if($sold_to!=""){
			if($pakeor==1){
			$sql.=" AND KD_DISTR LIKE '$sold_to' ";
			}else{
			$sql.=" KD_DISTR LIKE '$sold_to' ";
			$pakeor=1;
			}
		}
		if($ship_to!=""){
			if($pakeor==1){
			$sql.=" AND KD_SHIPTO LIKE '$ship_to' ";
			}else{
			$sql.=" KD_SHIPTO LIKE '$ship_to' ";
			$pakeor=1;
			}
		}
		if($produk!=""){
			if($pakeor==1){
			$sql.=" AND KD_MATERIAL LIKE '$produk' ";
			}else{
			$sql.=" KD_MATERIAL LIKE '$produk' ";
			$pakeor=1;
			}
		}
		if($tgl1!="" and $tgl2!=""){
			if ($tgl1=="")$tgl1_sql = "01-01-1990";
			else $tgl1_sql = $tgl1;
			if ($tgl2=="")$tgl2_sql = "12-12-9999";
			else $tgl2_sql = $tgl2;
			if($pakeor==1){
			$sql.=" AND TGL_TIMB2 BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			}else{
			$sql.=" TGL_TIMB2 BETWEEN TO_Date('$tgl1_sql 00:00:00', 'DD-MM-YYYY HH24:MI:SS') AND TO_Date('$tgl2_sql 23:59:59', 'DD-MM-YYYY HH24:MI:SS') ";
			$pakeor=1;
			}
		}
		$sql.=" ORDER BY NO_SPJ ASC";
	}
	//echo $sql;
	$query= oci_parse($conn, $sql);
	oci_execute($query);
        $s=1;
	while($row=oci_fetch_array($query)){
		$no_so_v[$s]=$row['NO_SO'];
		$no_spj_v[$s]=$row['NO_SPJ'];
                $nopol_v[$s]=$row['NOPOL'];
                $sopir_v[$s]=$row['SOPIR'];
		$tgl_timb1_v[$s]=$row['TGL_TIMB1'];
		$tgl_timb2_v[$s]=$row['TGL_TIMB2'];
		$berat_ksg_v[$s]=$row['BERAT_KSG'];
		$berat_isi_v[$s]=$row['BERAT_ISI'];
		$kdproduk_v[$s]=$row['KD_MATERIAL'];
		$produk_v[$s]=$row['NM_MATERIAL'];
                $kdgdg_v[$s]=$row['KD_GDG'];
                $nmgdg_v[$s]=$row['NM_GDG'];
		$sold_to_v[$s]=$row['KD_DISTR'];
		$namasold_to_v[$s]=$row['NM_DISTR'];
		$ship_to_v[$s]=$row['KD_SHIPTO'];
		$namaship_to_v[$s]=$row['NM_SHIPTO'];
		$alamat_v[$s]=$row['ALAMAT'];
		$kddistrik_v[$s]=$row['KD_DISTRIK'];
		$nmdistrik_v[$s]=$row['NM_DISTRIK'];
		$qty_v[$s]=$row['QTY_DO'];
		$uom_v[$s]=$row['UOM'];
		$id_v[$s]=$row['ID'];
		$item_num[$s]=$row['ITEM_NUMBER'];
                $s++;
	}
	$total=count($no_so_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

if($total>0){

	// HTTP headers
	HeaderingExcel('daftar_penjualan.xls');

	// Creating a workbook
	$workbook = new Workbook("-");
	// Creating the first worksheet
	$worksheet1 =& $workbook->add_worksheet('Penjualan');
	//$worksheet1->set_column(1, 1, 40);
	//$worksheet1->set_row(1, 20);
		$worksheet1->write(0, 0, 'No');
		$worksheet1->write(0, 1, 'No SPJ');
		$worksheet1->write(0, 2, 'Tgl SPJ');
                $worksheet1->write(0, 3, 'No Polisi');
                $worksheet1->write(0, 4, 'Sopir');
		$worksheet1->write(0, 5, 'Kode Distributor');
		$worksheet1->write(0, 6, 'Nama Distributor');
		$worksheet1->write(0, 7, 'Kode Shipto');
		$worksheet1->write(0, 8, 'Nama Shipto');
		$worksheet1->write(0, 9, 'Alamat');
		$worksheet1->write(0, 10, 'Kode Distrik');
		$worksheet1->write(0, 11, 'Nama Distrik');
		$worksheet1->write(0, 12, 'Kode Produk');
		$worksheet1->write(0, 13, 'Nama Produk');
		$worksheet1->write(0, 14, 'Qty');
		$worksheet1->write(0, 15, 'Kode Gudang');
		$worksheet1->write(0, 16, 'Nama Gudang');
	
	for ($i=1; $i <= $total; $i++) {
		$worksheet1->write($i, 0, $i);
		$worksheet1->write($i, 1, $no_spj_v[$i]);
		$worksheet1->write($i, 2, $tgl_timb2_v[$i]);
                $worksheet1->write($i, 3, $nopol_v[$i]);
                $worksheet1->write($i, 4, $sopir_v[$i]);
		$worksheet1->write($i, 5, $sold_to_v[$i]);
		$worksheet1->write($i, 6, $namasold_to_v[$i]);
		$worksheet1->write($i, 7, $ship_to_v[$i]);
		$worksheet1->write($i, 8, $namaship_to_v[$i]);
		$worksheet1->write($i, 9, $alamat_v[$i]);
		$worksheet1->write($i, 10, $kddistrik_v[$i]);
		$worksheet1->write($i, 11, $nmdistrik_v[$i]);
		$worksheet1->write($i, 12, $kdproduk_v[$i]);
		$worksheet1->write($i, 13, $produk_v[$i]);
		$worksheet1->write($i, 14, $qty_v[$i]);
		$worksheet1->write($i, 15, $kdgdg_v[$i]);
		$worksheet1->write($i, 16, $nmgdg_v[$i]);
	}

	$workbook->close();
	}
?>
