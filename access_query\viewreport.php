<?
session_start();
include ('../include/ex_fungsi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

// $halaman_id=2543;
$user_id=$_SESSION['user_id'];
$user_name=$_SESSION['user_name'];
$user_org=$_SESSION['user_org'];
$namauser=$_SESSION['nama_lengkap'];

$aksi = htmlspecialchars($_REQUEST['act']);
$cancelUrl='upload_mass_leadtime_so.php';
$status = '';
$statusInsert = array();
$statusInfo = array();

$dirr = $_SERVER['PHP_SELF'];
$halaman_id = $fungsi->getmainhalam_id($conn,$dirr);

// if(isset ($aksi)){
//     if($aksi == 'update'){
//         readFilexls($conn);
//     }
// }        
 
if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
// $user_name = $user_name;
// function displayData($conn){
//     if($conn){
//         $sql = "select * from ZREPORT_M_KOTA";
//         $query= oci_parse($conn, $sql);
//         oci_execute($query);
//         $result=array();
//         $i=0;
//         while($row=oci_fetch_array($query)){
//             $result[$i]['KD_KOTA'] = $row['KD_KOTA'];
//             $result[$i]['NM_KOTA'] = $row['NM_KOTA'];
//             $result[$i]['DIST_SNI'] = $row['DIST_SNI'];
//             $i++;
//         }
//         echo json_encode($result);
//     }
// }

function readFilexls($conn){
    if($conn){
        ############################# READ XLS ####################
        error_reporting(E_ALL ^ E_NOTICE);
        require_once '../ex_report/excel_reader2.php';

        if(isset ($_POST['Import'])){
            $allowedExts = "xls";
            $extension = end(explode(".", $_FILES["file"]["name"]));
             if ($extension==$allowedExts)
               {
                 //echo "Upload: " . $_FILES["file"]["name"] . "<br />";
                 $pecah=$_FILES["file"]["name"];
                 $pecahTanda=explode("_", $pecah);
 
                 $cell   = new Spreadsheet_Excel_Reader($_FILES['file']['tmp_name']);
                 $jumlah_row = $cell->rowcount($sheet_index=0);
                 $jumlah_col = 4;
                 $kode_file   = trim($cell->val( 1,2 ));
 
                 $dataInsert = array();
                 $dataHeader = array(
                     'kdFile' => $kode_file
                 );
                 $jumlah   = trim($cell->val( 1,1));
                for ($i = 2; $i <=$jumlah; $i++) {                    
                        $code = $cell->val( $i,1 );
                        $plant = $cell->val( $i,2 );
                    $newstring = substr($plant, -3);
                    $newstring2 = substr($plant,0, 3);
                    $newstring3 = date('dmY')."/~".$newstring.$newstring2;
                    if(($plant !='') and ($code == $newstring3)){ 
                        $dataInsert2['PLANT'] = $plant;
                        array_push($dataInsert, $dataInsert2);
                    }else{
                        echo "Tidak Dapat Menjalankan Perintah";
                        exit;
                    }
                }
                updateData($dataInsert,$dataHeader,$conn);  
            }
            else{
                   echo "<script>alert('Please upload xls file...!!');</script>";  
            }  
        }   
    }
    
}

function updateData($dataLst,$dataHeader,$conn){
    global $statusInsert, $statusInfo, $status;
    if($conn && !empty($dataLst)){
        
        for($i = 0 ; $i < sizeof($idLst) ; $i ++){
            prin_r('Data - '.$idLst[$i].' ');
        }
         $count = 0;
         $gagalinsert =0;
         $gagalupdate=0;
         $berhasilinsert=0;
         $berhasilupdate=0;
         foreach ($dataLst as $key => $value) {

            $leadtime_plant     = strtoupper($value['PLANT']);
            // $leadtime_kota   = strtoupper($value['KOTA']);
            // $leadtime_standart_area         = strtoupper($value['STANDART_AREA']);                        
            // $leadtime_material    = strtoupper($value['MATERIAL']);     
            // // $leadtime_pallet  = strtoupper($value['PALLET']);
            // $leadtime_del  = strtoupper($value['DELETE_MARK']);

           /* if($distSNI == ''){
                $distSNI = $kodeKota;
            }*/
            echo "<br>";
            echo $leadtime_plant;
            echo "<br>";
            echo "<br>";
            $sql = $leadtime_plant;
            $query= oci_parse($conn, $sql);
            oci_execute($query);
            $result=array();
            $i=0;
            $ii=0;
            while($row=@oci_fetch_array($query)){
                // print_r("<pre>".json_encode($result[$i]=$row)."</pre>");
                $data_output = $row;
                foreach ($data_output as $key => $value) {
                    $result[$i][$key]= $value;
                    $total_i=count($result[$i]);
                    for($ii=0; $ii<=$total_i; $ii++){
                        unset($result[$i][$ii]);
                    }
                }
                $i++;
            }

            // echo $total_i;
            // for($ii=0; $ii<=$total_i; $ii++){
            //     unset($result[$i][$ii]);
            // }

            echo '<pre>';
            print_r($result);
            echo '</pre>';
            // print_r("tesnya".oci_free_statement($query));
            $count++;
        }
        // $pesan ['pesan'] = "berhasil insert $berhasilinsert, gagal insert $gagalinsert, berhasil update $berhasilupdate, gagal update $gagalupdate";
        array_push($statusInfo, $pesan);
    }
    else{
        echo "<script>alert('Something wrong !!!');</script>";  
    }
}

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Mass Mapping Brand</title>
        <!-- import easyui -->
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/gray/easyui.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/icon.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/themes/color.css">
<link rel="stylesheet" type="text/css" href="../js/easyui/demo/demo.css">
<script type="text/javascript" src="../include/jquery-1.6.2.min.js"></script>
<script type="text/javascript" src="../js/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-detailview.js"></script>
<script type="text/javascript" src="../js/easyui/datagrid-filter.js"></script>
<script type="text/javascript" src="../js/easfyui/datagrid-dnd.js"></script>
<style>
    
}
</style>
</head> 

<body>
    <div id="cc" class="easyui-layout" style="width:800px;height:100px;margin: auto;">
            <form method="post" name="import" id="import" enctype="multipart/form-data">
                <table width="500" align="center" class="adminform">
                    <tr height="10">
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                    </tr>
                    <tr>
                        <td class="puso"></td>
                        <td> <input  name="file" type="file"  class="button" id="fileUpload"></td>
                        <td>
                            <input name="Import" type="submit"  class="button" value="Execute" id="importBtn" onclick="importFile()">
                            <!-- <a href="<?=$cancelUrl;?>" class="button">Cancel</a> -->
                        </td>
                    </tr>
                    <!-- <tr>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                        <td>
                            <input name="Import" type="submit"  class="button" value="Import" id="importBtn" onclick="importFile()"> -->
                            <!-- <a href="<?=$cancelUrl;?>" class="button">Cancel</a> -->
                        <!-- </td>
                    </tr> -->
                    <tr>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>

                    </tr>
                    <!-- <tr>
                        <td class="puso">
                            Download template file
                        </td>
                        <td class="puso">:</td>
                        <td class="puso">
                            <a href="template_excel/template_mass_mapping_leadtime_so.xls" class="easyui-linkbutton">Download</a>
                        </td>
                    </tr> -->
                      <tr>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                        <td class="puso">&nbsp;</td>
                    </tr>
                </table>
            </form>
    </div>
    <script>
        function importFile(){
            $('#import').form('submit',{
                url: 'viewreport.php?act=update'
            });
        }
    </script>
<div align="center">
    <?
    foreach ($statusInfo as $key => $value) {
        echo $value['pesan'].'<br>'; 
    }
    foreach ($statusInsert as $key => $value) {
        echo $value['status'].'<br>'; 
    }
    ?>    
</div>
<p>&nbsp;</p>
<? 
if(isset ($aksi)){
    if($aksi == 'update'){
        readFilexls($conn);
    }
}     
// include ('../include/ekor.php'); 
?>
</body>
</html>