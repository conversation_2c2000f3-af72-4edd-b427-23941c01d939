--TEST--
Mail: Test for bug #9137
--FILE--
<?php

require_once dirname(__FILE__) . '/../Mail/RFC822.php';
require_once 'PEAR.php';

$addresses = array(
    array('name' => '<PERSON>', 'email' => '<EMAIL>'),
    array('name' => '<PERSON>\\', 'email' => '<EMAIL>'),
    array('name' => '<PERSON> "<PERSON>', 'email' => '<EMAIL>'),
    array('name' => '<PERSON> "<PERSON>\\', 'email' => '<EMAIL>'),
);

for ($i = 0; $i < count($addresses); $i++) {
    // construct the address
    $address = "\"" . addslashes($addresses[$i]['name']) . "\" ".
        "<".$addresses[$i]['email'].">";

    $parsedAddresses = Mail_RFC822::parseAddressList($address);
    if (is_a($parsedAddresses, 'PEAR_Error')) {
        echo $address." :: Failed to validate\n";
    } else {
        echo $address." :: Parsed\n";
    }
}

--EXPECT--
"<PERSON>" <<EMAIL>> :: Parsed
"<PERSON>\\" <<EMAIL>> :: Parsed
"John \"Doe" <<EMAIL>> :: Parsed
"John \"Doe\\" <<EMAIL>> :: Parsed
