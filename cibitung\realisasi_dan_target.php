<? 
session_start();
//include ('include/crm_fungsi1.php');
//include ('../include/validasi.php'); 
//$fungsi=new crm_fungsi();
//$conn=$fungsi->crm_koneksi();


mysql_connect("10.15.5.71","sgg","sggroup"); //koneksi database
mysql_select_db("eis");

if(isset($_POST['cari'])){
	   
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;
		$thn=$year1;
		$bln=$month1;
        $type = $_POST['type'];

		if($type=='121-301'){
			$Typeket="ZAK";
		}
		else if($type=='121-302'){
			$Typeket="CURAH";
		}
        


   $PLANT="SELECT KD_PLANT FROM m_plant WHERE ORG='2000'";
				$PLANT_OK=mysql_query($PLANT);
				$s=0;
				while ($PLANT=mysql_fetch_array($PLANT_OK)) {		
				  $nmplan1[]=$PLANT['KD_PLANT'];
				$s++;
				}
			 $plantok=count($nmplan1);

	$KOTA="SELECT * FROM m_kota WHERE kd_prop='1025' ORDER BY kd_area  ASC  ";
				$KOTA_OK=mysql_query($KOTA);
				$s=0;
				while ($KOTA=mysql_fetch_array($KOTA_OK)) {		
				 $KD_KOTA[$s]=$KOTA['kd_kota'];
				 $NM_KOTA[$s]=$KOTA['nm_kota'];
				 $KD_AREA[$s]=$KOTA['kd_area'];
				 $KD_PROP[$s]=$KOTA['kd_prop'];
				$s++;
				}
			 $KOTA=count($KD_KOTA);

 
}
?>
<script language=javascript>
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
 <!-- JQuery -->
<script src="../master/jq_fixheader/jquery.js" type="text/javascript"></script>
<script src="../master/jq_fixheader/jquery_002.js" type="text/javascript"></script>	

<script type="text/javascript">
	$(function () {
            $(".adminlist").fixedtableheader({ highlightrow: true, headerrowsize: 2 });
        });   
</script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" align='center'>Daftar Target Dan Realisasi shipment
 </th>
</tr></table></div>
<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search </th>
</tr>
</table>
</div>
<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Bulan</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("01-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
    <tr>
	<tr>
      <td  class="puso">Produk </td>
      <td  class="puso">:</td>
      <td >
	  <select name=type>
	    <option value='00'>-------</option>
		<option value='121-301'>ZAK</option>
		<option value='121-302'>CURAH</option>
	  </select>
	  </td>
    </tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<br />
<br />
<th align='left'>Tanggal&nbsp;&nbsp;:&nbsp;<? echo $tglm; ?>&nbsp;s/d&nbsp;<? echo $tgls; ?></th>
<br>
<th align='left'>Type&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:&nbsp;<?echo $Typeket;?></th>
<br>&nbsp;
<br>&nbsp;
<br>
  <div align="center">
	<table width="1000" align="center" class="adminlist" border='1'>
  	<tr>
    <tr class="quote">
	    <td align="center"><strong>&nbsp;&nbsp;No</strong></td>
		<td align="center"><strong>Area</strong></td>
		<td align="center"><strong>Kota</strong></td>
		<td align="center"><strong>Distrik</strong></td>

		<? 
			#Kode Plant Header
			for($n=1;$n<$plantok;$n++){
				echo "<td align='center' colspan='2'><strong>".$nmplan1[$n]."</strong></td>";		
			}
			 echo "<tr><td colspan='4'></td>";	
			for($p=1;$p<$plantok;$p++){
				echo"<td align='center'><strong>Target</strong></td>
				<td align='center'><strong>Realisasi</strong></td>";
			}
		?>
		
	</tr>
    <tr class="quote">		
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$KOTA;$i++) {
		$totaldo= $totaldo+$TOTAL_QTY[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
	?>
		<td align="center"><? echo $b."."?></td>
		<td align="left"><?  echo $kode_area = $KD_AREA[$i]; ?></td>
		<td align="left"><?  echo $kode_kota = $KD_KOTA[$i]; ?></td>
		<td align="left"><? echo $NM_KOTA[$i]; ?></td>
		
	    
		<?

			#Kode Plant Header
			for($n=1;$n<$plantok;$n++){
				#echo "<td align='center' colspan='2'><strong>".$nmplan1[$n]."</strong></td>";	
				$kode_plant = $nmplan1[$n];

				#Query Target
				$target=0;
				$rencana_sql="SELECT * FROM sap_t_rencana_sales_area_kota WHERE plant='".$nmplan1[$n]."' AND kdkota='".$kode_kota."'AND thn='".$thn."'AND bln='".$bln."'";
				//$rencana_sql="SELECT SUM(quantum)AS rencana FROM sap_t_rencana_sales_area_kota WHERE plant='2401' AND AREA='111' AND kdkota='252003'";
				$rencanaok=mysql_query($rencana_sql);
				while ($rencana=mysql_fetch_array($rencanaok)) {		
					$target=$rencana[quantum];
				}
				#Query Rilis
				$realisasi="SELECT SUM(TOTAL_QTY)AS TOTAL, VKGRP,NAME_P,NMPLAN,BZIRK,BZTXT,VKORG,BUDAT,ID_SHIPMENT,VKGRP_TXT,VKBUR, item_no 
					FROM sap_t_shipment_penjualan WHERE bzirk='$kode_kota'AND nmplan='$kode_plant' AND budat BETWEEN '$tglm ' AND '$tgls' 
					AND item_no LIKE '$type%'";			
				   $totalok=mysql_query($realisasi);
					while ($ok=mysql_fetch_array($totalok)) {		
					$realisasi=$ok['TOTAL'];
				}

				if($target>0)
				echo"<td align='center'>$target</td>";
				else
				echo"<td align='center'>&nbsp;</td>";
    
                if($realisasi>0)
				echo"<td align='center'>$realisasi</td>";
				else
				echo"<td align='center'>&nbsp;</td>";
				
			}
 
		?>
		
		</tr>
	  <? } ?>
	</table>	
	<p>&nbsp;</p>
	</div>


<p>&nbsp;</p>
</p>
</body>
</html>
