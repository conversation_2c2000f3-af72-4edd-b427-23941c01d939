<?php

/*
 * Formula Perhitungan Volume dan Index  
 * by <EMAIL>
 */
?>
<?
//require_once '../include/oracleDev.php'; 
//$fungsi=new conntoracleDEVSD();
//$conn=$fungsi->DEVSDdb();

$bulanVal=$bulanVolum;
$tahunVal=$tahunVolum;
$plantVal=$nmplant;
$comVal='2000';
$itemNoVal='121-301';

$tgl1='26';
$tgl2='25';
$bulan1=sprintf('%02d',$bulanVal-1);
$bulan2=sprintf('%02d',$bulanVal-2);
$bulan3=sprintf('%02d',$bulanVal-3);


$table1='ZREPORT_TARGET_EXP_tmpVOLGP';
$table2='ZREPORT_TARGET_EXP_tmpINDEXGP';

#################### PROSES 1 #############################
$sqlCreate1= "
create table $table1 as

Select NO_EXPEDITUR,NAMA_EXPEDITUR, KOTA, NM_KOTA, BULAN, TAHUN,PLANT,COM,ITEM_NO,BRAN1,KODE_DA,VOLUME
from
(
select NO_EXPEDITUR,  KOTA, NM_KOTA,
('$bulanVal') AS BULAN,('$tahunVal') AS TAHUN,
('$plantVal') AS PLANT,('$comVal') AS COM,('$itemNoVal') AS ITEM_NO,BRAN1,KODE_DA,
SUM(VOLUME) AS VOLUME

from

(
select NO_EXPEDITUR,  KOTA, NM_KOTA,BRAN1,KODE_DA,PERIODE,QTY,
CASE WHEN PERIODE =1 THEN SUM((QTY * 20)/100)
     WHEN PERIODE = 2 THEN SUM((QTY * 30/100))
     ELSE SUM((QTY * 50 )/100)
END AS VOLUME
from (

select NO_EXPEDITUR,  KOTA, NM_KOTA,BRAN1,KODE_DA,('1') as PERIODE,SUM(KWANTUMX)  as QTY
from ZREPORT_RPT_REAL where
NO_POLISI not LIKE 'S11LO' and STATUS='70'and PLANT='$plantVal' and COM='$comVal' and ITEM_NO like '$itemNoVal%' and
ORDER_TYPE = 'ZNL' AND
TGL_SPJ BETWEEN TO_Date('$tahunVal$bulan3$tgl1','YYYYMMDD') AND TO_Date('$tahunVal$bulan2$tgl2', 'YYYYMMDD')
group by NO_EXPEDITUR,KOTA,NM_KOTA,BRAN1,KODE_DA


UNION

select NO_EXPEDITUR,  KOTA, NM_KOTA,BRAN1,KODE_DA,('2') as PERIODE,SUM(KWANTUMX)  as QTY
from ZREPORT_RPT_REAL where
NO_POLISI not LIKE 'S11LO' and STATUS='70'and PLANT='$plantVal' and COM='$comVal' and ITEM_NO like '$itemNoVal%' and
ORDER_TYPE = 'ZNL' AND
TGL_SPJ BETWEEN TO_Date('$tahunVal$bulan2$tgl1','YYYYMMDD') AND TO_Date('$tahunVal$bulan1$tgl2', 'YYYYMMDD')
group by NO_EXPEDITUR,KOTA,NM_KOTA,BRAN1,KODE_DA

UNION

select NO_EXPEDITUR,  KOTA, NM_KOTA,BRAN1,KODE_DA,('3') as PERIODE,SUM(KWANTUMX)  as QTY
from ZREPORT_RPT_REAL where
NO_POLISI not LIKE 'S11LO' and STATUS='70'and PLANT='$plantVal' and COM='$comVal' and ITEM_NO like '$itemNoVal%' and
ORDER_TYPE = 'ZNL' AND
TGL_SPJ BETWEEN TO_Date('$tahunVal$bulan1$tgl1','YYYYMMDD') AND TO_Date('$tahunVal$bulanVal$tgl2', 'YYYYMMDD')
group by NO_EXPEDITUR,KOTA,NM_KOTA,BRAN1,KODE_DA


)
group by NO_EXPEDITUR,KOTA,NM_KOTA,BRAN1,KODE_DA,PERIODE,QTY
order by NO_EXPEDITUR,KOTA,PERIODE ASC
) t where t.NO_EXPEDITUR=NO_EXPEDITUR and t.KOTA=KOTA

group by NO_EXPEDITUR,KOTA,NM_KOTA,BRAN1,KODE_DA
) g left join ZREPORT_M_EXPEDITUR on (g.NO_EXPEDITUR=KODE_EXPEDITUR)
order by NO_EXPEDITUR,KOTA ASC   

        ";   
     //echo $sqlCreate1;
    $query1= oci_parse($conn, $sqlCreate1);
    oci_execute($query1);
	
################################# Proses 2 ################################################	
$sqlCreate2= "   

        create table $table2 as
        select NO_EXPEDITUR,NAMA_EXPEDITUR,KOTA,NM_KOTA2 as NM_KOTA,('$bulanVal') as BULAN,('$tahunVal') as TAHUN,
        ('$plantVal') AS PLANT,('$comVal') AS COM,('$itemNoVal') AS ITEM_NO,BRAN1,KODE_DA,
        SUM((VOLUME/TOT_VOLUME)*100) AS VOL_INDEX from 

        (       
        select KOTA as KOTA2,NM_KOTA as NM_KOTA2,BRAN1 as BRAN12,KODE_DA as KODE_DA2,SUM(VOLUME) AS TOT_VOLUME from $table1 

        group by KOTA,NM_KOTA,BRAN1,KODE_DA
      
        ) LEFT JOIN $table1 p ON(
            p.KOTA=KOTA2 and p.BRAN1=BRAN12
        )
        group by NO_EXPEDITUR,NAMA_EXPEDITUR,KOTA,NM_KOTA2,BRAN1,KODE_DA,VOLUME
        order by NO_EXPEDITUR,KOTA ASC
		
        ";   
     //echo $sqlCreate2;
    $query2= oci_parse($conn, $sqlCreate2);
    oci_execute($query2);	
################################ PROSES 3 ##################################################
$sqlCreate3= "   
			INSERT INTO ZREPORT_TARGET_EXP
			(
			  NO_EXPEDITUR, 
			  NAMA_EXPEDITUR, 
			  KOTA,   
			  NM_KOTA, 
			  BULAN, 
			  TAHUN,   
			  PLANT,
			  COM,
			  ITEM_NO,
                          BRAN1,
                          PLANT_GP,
			  VOLUME_GP,
			  VOL_INDEX_GP
			)

			select NO_EXPEDITUR, NAMA_EXPEDITUR, KOTA, NM_KOTA,BULAN,TAHUN,PLANT,COM,ITEM_NO,BRAN1,PLANT_GP,VOLUME,VOL_INDEX
			from (
			select NO_EXPEDITUR as NO_EXPEDITUR2, NAMA_EXPEDITUR as NAMA_EXPEDITUR2, KOTA as KOTA2,
			NM_KOTA as NM_KOTA2,BULAN as BULAN2,TAHUN as TAHUN2,PLANT as PLANT2,COM AS COM2,
			ITEM_NO AS ITEM_NO2,BRAN1 as BRAN12,KODE_DA as PLANT_GP,
			VOLUME
			from $table1

			)
			RIGHT OUTER JOIN $table2 a ON (
			a.NO_EXPEDITUR=NO_EXPEDITUR2 and
			a.NAMA_EXPEDITUR=NAMA_EXPEDITUR2 and
			a.KOTA=KOTA2 and 
			a.NM_KOTA=NM_KOTA2 and
			a.BULAN=BULAN2 and
			a.TAHUN=TAHUN2 and
			a.PLANT=PLANT2 and
			a.COM=COM2 and
                        a.BRAN1=BRAN12 and
			a.ITEM_NO=ITEM_NO2
			)
			order by NO_EXPEDITUR,KOTA ASC
		
        ";   
     //echo $sqlCreate3;
    $query3= oci_parse($conn, $sqlCreate3);
    oci_execute($query3);	

################################ PROSES 4 Drop tabel  ##################################################
$sqlCreate4= "   
		drop table $table2
        ";   
     //echo $sqlCreate4;
   $query4= oci_parse($conn, $sqlCreate4);
   oci_execute($query4);		
$sqlCreate5= "   
		drop table $table1
        ";   
     //echo $sqlCreate5;
    $query5= oci_parse($conn, $sqlCreate5);
    oci_execute($query5);	
$sqlCreate6= "   
		COMMIT
        ";   
     //echo $sqlCreate6;
    $query6= oci_parse($conn, $sqlCreate6);
    oci_execute($query6);

?>