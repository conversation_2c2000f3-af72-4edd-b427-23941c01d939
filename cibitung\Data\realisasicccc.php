<? 
session_start();
include ('../include/or_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new or_fungsi();
$conn=$fungsi->or_koneksi();


		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_dataprod.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri


      
		$fce->X_VKORG =2000;
		$fce->X_NOSPJ =2000317126;

		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
			//if ($fce->ZDATA->row["HARGA"] > 0){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$no_kontrak[$s] = $fce->ZDATA->row["VGBEL"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$tipe_so[$s] = $fce->ZDATA->row["AUART"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				$no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$no_sloc[$s] = $fce->ZDATA->row["TO_WHSE"];
				$nama_sloc[$s] = $fce->ZDATA->row["DESC_TO_SLOC"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$namasold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["PRODUK"];
				$uom1[$s] = $fce->ZDATA->row["UOM"];
				$harga[$s] = $fce->ZDATA->row["HARGA"];
				$harga[$s] = $fce->ZDATA->row["HARGA"];                             
				$nama_sloc[$s] = $fce->ZDATA->row["DESC_TO_SLOC"];
				if ($fce->ZDATA->row["KWMENG"] > 0)
				$harga_satuan =  $fce->ZDATA->row["HARGA"]/$fce->ZDATA->row["KWMENG"];
				else
				$harga_satuan =  0;
				$harga_shp[$s]= $harga_satuan * $fce->ZDATA->row["KWANTUM"];
				$kapal[$s] = $fce->ZDATA->row["NAMA_KAPAL"];
				$s++;
				}
			//}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($no_spj);	

		var_dump($no_sloc);
		var_dump($nama_sloc)

		

		

?>

	<table width="2000" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Realisasi Shipment</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="2000" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No PP</strong></td>
		<td align="center"><strong >Tgl PP</strong></td>		
		<td align="center"><strong >No SO</strong></td>
		<td align="center"><strong >No Kontrak</strong></td>
		<td align="center"><strong >Tiper Order</strong></td>	
		<td align="center"><strong >No DO</strong></td>	
		<td align="center"><strong >Incoterm</strong></td>		
		<td align="center"><strong >Tgl DO</strong></td>
		<td align="center"><strong >Qty DO</strong></td>
		<td align="center"><strong >Material</strong></td>
		<td align="center"><strong >UOM</strong></td>
		<td align="center"><strong >Harga </strong></td>
		<td align="center"><strong >No SPJ</strong></td>
		<td align="center"><strong >Tgl SPJ</strong></td>
		<td align="center"><strong >Jam SPJ</strong></td>
		<td align="center"><strong >No SPPS</strong></td>
		<td align="center"><strong >No Polisi</strong></td>
		<td align="center"><strong >Nama Sopir</strong></td>
		<td align="center"><strong >Kode Distributor</strong></td>
		<td align="center"><strong >Distributor</strong></td>
		<td align="center"><strong >Kode Shipto</strong></td>
		<td align="center"><strong >Nama Shipto</strong></td>
		<td align="center"><strong >Alamat Shipto</strong></td>
		<td align="center"><strong >Sloc</strong></td>
		<td align="center"><strong >Kode Distrik</strong></td>
		<td align="center"><strong >Distrik</strong></td>
		<td align="center"><strong >Kode Ekspeditur</strong></td>
		<td align="center"><strong >Nama Ekspeditur</strong></td>
		 <td align="center"><strong>Kode Plant</strong></td>
		 <td align="center"><strong>Nama Plant</strong></td>
		 <td align="center"><strong>Nama Kapal </strong></td>
		 <td align="center"><strong>Status</strong></td>
      </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $no_pp[$i]; ?></td>
		<td align="left"><? $thn=substr($tgl_pp[$i],0,4);
							$bln=substr($tgl_pp[$i],4,2);
							$hr=substr($tgl_pp[$i],6,2);
							$tglpp=$hr.'-'.$bln.'-'.$thn;
							echo $tglpp; ?></td>
		<td align="center"><? echo $no_so[$i]; ?></td>
		<td align="center"><? echo $no_kontrak[$i]; ?></td>
		<td align="center"><? echo $tipe_so[$i]; ?></td>
		<td align="center"><? echo $no_do[$i]; ?></td>
		<td align="center"><? echo $inco[$i]; ?></td>
		<td align="left"><? $thn=substr($tgl_do[$i],0,4);
							$bln=substr($tgl_do[$i],4,2);
							$hr=substr($tgl_do[$i],6,2);
							$tgldo=$hr.'-'.$bln.'-'.$thn;
							echo $tgldo;  ?></td>
		<td align="right"><? echo number_format($qty_do[$i],0,',','.'); ?></td>
		<td align="left"><? echo $produk[$i]; ?></td>
		<td align="left"><? echo $uom1[$i]; ?></td>
		<td align="left"><? echo number_format($harga_shp[$i],2,',','.'); ?></td>
		<td align="left"><? echo $no_spj[$i]; ?></td>
		<td align="center"><? $thn=substr($tgl_spj[$i],0,4);
							$bln=substr($tgl_spj[$i],4,2);
							$hr=substr($tgl_spj[$i],6,2);
							$tglspj=$hr.'-'.$bln.'-'.$thn;
							echo $tglspj;  ?></td>
		<td align="left"><? $jam=substr($jam_spj[$i],0,2);
							$mnt=substr($jam_spj[$i],2,2);
							$dtk=substr($jam_spj[$i],4,2);
							$jamspj=$jam.':'.$mnt.':'.$dtk;
							echo $jamspj; ?></td>							
		<td align="left"><? echo $no_spps[$i]; ?></td>
		<td align="left"><? echo $nopol[$i]; ?></td>
		<td align="left"><? echo $sopir[$i]; ?></td>
		<td align="left"><? echo $soldto[$i]; ?></td>
		<td align="left"><? echo $namasold[$i]; ?></td>
		<td align="left"><? echo $kdshipto[$i]; ?></td>
		<td align="left"><? echo $nmshipto[$i]; ?></td>
		<td align="left"><? echo $alamat[$i]; ?></td>
		<td align="left"><? echo $no_sloc[$i]; ?></td>
		<td align="left"><? echo $kddistrik[$i]; ?></td>
		<td align="left"><? echo $nmdistrik[$i]; ?></td>
		<td align="left"><? echo $kdexp[$i]; ?></td>
		<td align="left"><? echo $nmexp[$i]; ?></td>
		<td align="left"><? echo $kdplant[$i]; ?></td>
		<td align="left"><? echo $nmplant[$i]; ?></td>
		<td align="left"><? echo $kapal[$i]; ?></td>
		<td align="left"><? echo $tstatus[$i]; ?></td>
		</tr>
	  <? } ?>
	 <tr>
	 <td colspan="7" align="center">TOTAL</td>
	 <td align="right"><?=number_format($totaldo,0,',','.')?></td>
	 <td>&nbsp;</td>
	 </tr> 
	</table>
<p>&nbsp;</p>

	</div>


</div>

<p>&nbsp;</p>
</p>
</body>
</html>
