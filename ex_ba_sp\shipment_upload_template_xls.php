<?php
session_start();
require_once('../include/excel/Worksheet.php');
require_once('../include/excel/Workbook.php');
require_once ('../security_helper.php');
sanitize_global_input();

function HeaderingExcel($filename)
{
    header("Content-type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=$filename");
    header("Expires: 0");
    header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
    header("Pragma: public");
}

HeaderingExcel('template_upload_shipment.xls');

// Creating a workbook
$workbook = new Workbook("-");
$format_bold = &$workbook->add_format();
$format_bold->set_bold();
$worksheet1 = &$workbook->add_worksheet('Data Upload');

$fields = array(
    'PLANT', 'SOLD_TO', 'SHIP_TO', 'SAL_DISTRIK', 'SAL_GROUP', 'SAL_OFFICE', 'VENDOR',
    'NAMA_VENDOR', 'NAMA_SAL_OFF', 'NAMA_SAL_GRP', 'NAMA_SAL_DIS', 'NO_POL',
    'VEHICLE_TYPE', 'WARNA_PLAT', 'KODE_PRODUK', 'NAMA_PRODUK', 'KODE_KANTONG',
    'NAMA_KANTONG', 'QTY_SHP', 'TIPE_TRANSAKSI','TANGGAL_KIRIM',
    'NAMA_PLANT', 'STATUS_APP', 'NAMA_SOLD_TO', 'NAMA_SHIP_TO',
    'SATUAN_SHP', 'KELOMPOK_TRANSAKSI', 'KAPASITAS_VEHICLE',
    'DOC_SHP', 'TIPE_DO', 'INCO',
    'SUPIR', 'NO_SO'
    // 'QTY_KTG_RUSAK', 'QTY_SEMEN_RUSAK', 'TANGGAL_DATANG', 'TANGGAL_BONGKAR',
    // 'KETERANGAN_POD', 'EVIDENCE_POD1', 'EVIDENCE_POD2', 'GEOFENCE_POD'
);

// Header Sheet 1: Data Upload
for ($i = 0; $i < count($fields); $i++) {
    $worksheet1->write(0, $i, $fields[$i], $format_bold);
}

// Data Sheet 1
// for ($row = 1; $row <=2; $row++) {
//     for ($col = 0; $col < count($fields); $col++) {
//         $worksheet1->write_string($row, $col, ''. $row);
//     }
// }

// Header Sheet 2: Contoh Data
$worksheet2 = &$workbook->add_worksheet('Contoh Data');
for ($i = 0; $i < count($fields); $i++) {
    $worksheet2->write(0, $i, $fields[$i], $format_bold);
}

// Data Sheet 2
$example_data = array(
    '3400', '0000000138', '1380001000', '251001', '2510', '1025', '0000410092',
    'SEMEN INDONESIA LOGISTIC, PT', 'JAWA TIMUR', 'GRESIK', 'GRESIK', 'N1ND1',
    '300', 'KUNING', '121-301-0050', 'SEMEN PCC ZAK 40KG', '121-400-0225',
    'KTG PASTED KRAFT 2PLY PCC 40KG MERAH', '15', 'BAG', '2025-07-03 00:00:00',
    'CP Indarung', 'DRAFT', 'SEKAWAN NIAGA JAYA, PT', 'TK. NIRMALA 123',
    'ZAK', 'DARAT', '50',
    'ZSD', 'ZLF', 'FRC',
    'ASWAR', '0017007996',
    // '0', '0', '2025-07-03 00:00:00', '2025-07-03 00:00:00',
    // 'TES POD', 'https://media-exp1.licdn.com/dms/image/C560BAQFcklp--SkPxw/company-logo_200_200/0/1597488388435?e=2147483647&v=beta&t=QbSXztb1qtLIcEq8N5aIDhTEPu_XTYYVlamhHc-BahU',
    // 'https://media-exp1.licdn.com/dms/image/C560BAQGvwPSGXDMzqg/company-logo_200_200/0/1593658910212?e=2147483647&v=beta&t=BnqxEi2d5qLsvtJmbq4LD3Hf4GFncKxDA_Ghv22T5L8',
    // '-6,7011737, 111,6255767'
);

for ($i = 0; $i < count($fields); $i++) {
    $worksheet2->write_string(1, $i, $example_data[$i]);
}

$workbook->close();
?>
