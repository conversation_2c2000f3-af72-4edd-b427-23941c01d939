<?
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
// hell0
$halaman_id=2043;
$user_id=trim($_SESSION['user_id']);
$user_org=$_SESSION['user_org'];
$user_name=$_SESSION['user_name'];
$page="penerimaan_inv_vendor.php";

$user_data=$fungsi->ex_find_user($conn,$user_id);

$vendor=$user_data["vendor"];
$nama_vendor=$user_data["nama_vendor"];
//echo $vendor;

//vendor
$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
$mp_coics=$fungsi->getComin($conn,$user_org);
if(count($mp_coics)>0){
unset($orgIn);$orgcounter=0;
foreach ($mp_coics as $keyOrg => $valorgm){
      $inorg .="'".$keyOrg."',";
      $orgcounter++;
}
    $orgIn= rtrim($inorg, ',');
}else{
    $orgIn= $user_org;
}
$dirr = $_SERVER['PHP_SELF'];
$halaman_id=$fungsi->getmainhalam_id($conn,$dirr);

//$action_page=$fungsi->security($conn,$user_id,$halaman_id);

//$page="penerimaan_inv_vendor.php";
//$vendor=$fungsi->ex_find_vendor($conn,$user_id);
//$hanya_baca = $fungsi->ex_hanya_baca($vendor);
//
if($user_id==''){
    ?>
    <SCRIPT LANGUAGE="JavaScript">
    <!--
    alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
    //-->
    </SCRIPT>
    <a href="../index.php">Login....</a>
    <?
    exit;
}
$statusDok=0;
$count = count($t_return);
if ($total <= 0) $komen = "Tidak Ada Data Yang Ditemukan";
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>

    <link rel="stylesheet" href="../Templates/plugins/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../Templates/plugins/font/font-awesome/css/font-awesome.css">
    <link rel="stylesheet" href="../Templates/css/animate.css">
    <link rel="stylesheet" href="../Templates/plugins/toastr/toastr.min.css">
    <link rel="stylesheet" href="../Templates/css/dashboard.css">
    <style media="screen">
      canvas{
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
      }
      .loader {
        border: 16px solid #f3f3f3;
        border-radius: 50%;
        border-top: 16px solid #009688;
        width: 80px;
        height: 80px;
        -webkit-animation: spin 2s linear infinite;
        animation: spin 2s linear infinite;
        margin: auto auto;
        margin-top: 4px;
        position: relative;
        margin-bottom: 100px;
        /* position: absolute; */
        padding: 10px;
      }

      @-webkit-keyframes spin {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .back-loading{
        background: white;
        position: absolute;
        z-index: 9999;
        width: 99%;
        height: 100%;
        margin: 0 auto;
        left: 1;
        display: none;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }

      a:focus, a:hover {
        color: #fff;
        text-decoration: underline;
      }


      .small-box {
          border-radius: 2px;
          position: relative;
          display: block;
          margin-bottom: 20px;
          box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px;
      }

      .small-box>.inner {
        padding: 10px;
        color: #fff;
       }

       .small-box h3, .small-box p {
          z-index: 5;
        }

        .small-box h3 {
            font-size: 38px;
            font-weight: bold;
            margin: 0 0 10px 0;
            white-space: nowrap;
            padding: 0;
        }

        .small-box h3, .small-box p {
            z-index: 5;
          }
        .small-box p {
            font-size: 15px;
        }

        .small-box .icon {
            -webkit-transition: all .3s linear;
            -o-transition: all .3s linear;
            transition: all .3s linear;
            position: absolute;
            top: -10px;
            right: 10px;
            z-index: 0;
            font-size: 50px;
            padding-right: 10px;
            color: rgba(0,0,0,0.15);
            padding: 20px 0px;
        }

        .small-box>.small-box-footer {
            position: relative;
            text-align: center;
            padding: 3px 0;
            color: #fff;
            color: rgba(255,255,255,0.8);
            display: block;
            z-index: 10;
            background: rgba(0,0,0,0.1);
            text-decoration: none;
        }

        .small-box>.small-box-footer:hover{
            color:#fff;
            background:rgba(0,0,0,0.15)
        }
        .small-box:hover{
            text-decoration:none;
            color:#f9f9f9
        }
        .small-box:hover .icon{
            font-size:80px
        }

        /* -------------- loader6 -------------- */

        .loader6{
        	position: relative;
        	width: 12px;
        	height: 12px;

        	top: 46%;
        	top: -webkit-calc(50% - 6px);
        	top: calc(50% - 6px);
        	left: 46%;
        	left: -webkit-calc(50% - 6px);
        	left: calc(50% - 6px);

        	border-radius: 12px;
        	background-color: #fff;
        	-webkit-transform-origin:  50% 50%;
        			transform-origin:  50% 50% ;
        	-webkit-animation: loader6 1s ease-in-out infinite;
        			animation: loader6 1s ease-in-out infinite;
        }

        .loader6:before{
        	content: "";
        	position: absolute;
        	background-color: rgba(255, 255, 255, .5);
        	top: 0px;
        	left: -25px;
        	height: 12px;
        	width: 12px;
        	border-radius: 12px;
        }

        .loader6:after{
        	content: "";
        	position: absolute;
        	background-color: rgba(255, 255 ,255 ,.5);
        	top: 0px;
        	left: 25px;
        	height: 12px;
        	width: 12px;
        	border-radius: 12px;
        }


        @-webkit-keyframes loader6{
            0%{-webkit-transform:rotate(0deg);}
            50%{-webkit-transform:rotate(180deg);}
            100%{-webkit-transform:rotate(180deg);}
        }

        @keyframes loader6{
            0%{transform:rotate(0deg);}
            50%{transform:rotate(180deg);}
            100%{transform:rotate(180deg);}
        }

        .tile_count {
            margin-top: 20px
        }
        .tile_count .tile_stats_count {
            border-bottom: 1px solid #D9DEE4;
            padding: 0 10px 0 20px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            position: relative;
            color: #73879C;
            margin-bottom: 40px;
        }
        @media (min-width: 992px) {
            footer {
                margin-left: 230px
            }
        }
        @media (min-width: 992px) {
            .tile_count .tile_stats_count {
                margin-bottom: 10px;
                border-bottom: 0;
                padding-bottom: 10px
            }
        }
        .tile_count .tile_stats_count:before {
            content: "";
            position: absolute;
            left: 0;
            height: 65px;
            border-left: 2px solid #ADB2B5;
            margin-top: 10px
        }
        @media (min-width: 992px) {
            .tile_count .tile_stats_count:first-child:before {
                border-left: 0
            }
        }
        .tile_count .tile_stats_count .count {
            font-size: 30px;
            line-height: 47px;
            font-weight: 600;
            color: #424141;
        }
        @media (min-width: 768px) {
            .tile_count .tile_stats_count .count {
                font-size: 40px
            }
        }
        @media (min-width: 992px) and (max-width: 1100px) {
            .tile_count .tile_stats_count .count {
                font-size: 30px
            }
        }
        .tile_count .tile_stats_count span {
            font-size: 12px
        }
        @media (min-width: 768px) {
            .tile_count .tile_stats_count span {
                font-size: 13px
            }
        }
        .tile_count .tile_stats_count .count_bottom i {
            width: 12px
        }

        .count_bottom i.red {
          color: #F44336;
        }
        .count_bottom i.yellow {
          color: #FFD600;
        }
        .count_bottom i.green {
          color: #64DD17;
        }

        /* LOADING DEPAN */
        #loader {
          /* Uncomment this to make it run! */
          /*
             animation: loader 5s linear infinite;
          */

          position: absolute;
          top: calc(50% - 20px);
          left: calc(50% - 20px);
          z-index: 999;
        }
        @keyframes loader {
          0% { left: -100px }
          100% { left: 110%; }
        }
        #box {
          width: 50px;
          height: 50px;
          background: #009688;
          animation: animate .5s linear infinite;
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 3px;
        }
        @keyframes animate {
          17% { border-bottom-right-radius: 3px; }
          25% { transform: translateY(9px) rotate(22.5deg); }
          50% {
            transform: translateY(18px) scale(1,.9) rotate(45deg) ;
            border-bottom-right-radius: 40px;
          }
          75% { transform: translateY(9px) rotate(67.5deg); }
          100% { transform: translateY(0) rotate(90deg); }
        }
        #shadow {
          width: 50px;
          height: 5px;
          background: #000;
          opacity: 0.1;
          position: absolute;
          top: 59px;
          left: 0;
          border-radius: 50%;
          animation: shadow .5s linear infinite;
        }
        @keyframes shadow {
          50% {
            transform: scale(1.2,1);
          }
        }


        .container-indie {
          background: #fff;
          overflow: hidden;
          z-index: 99999;
          width: 100%;
          height: 100%;
          position: fixed;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
        }
        .container-indie h4 {
          position: absolute;
          bottom: 20px;
          left: 20px;
          margin: 0;
          font-weight: 100;
          opacity: .5;
          font-size: 100px;
          text-align: center;
        	font-family: sans-serif;
          color: #111;
        }

        .detail-stat-inv{
          color: #73879C;
          text-decoration: none;
          -webkit-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -moz-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -o-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          -ms-transition: background-color 300ms linear, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
          transition: background-color 100ms ease-out, color 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
        }

        .detail-stat-inv:hover, .detail-stat-inv:focus{
          color: #424141;
          text-decoration: none ;
        }


    </style>
  </head>
  <body>

    <body>

      <div class="container-indie">
        <div id="loader">
          <div id="shadow"></div>
          <div id="box"></div>
        </div>
      </div>
      <div class="content-dashboard">
        <h1>DASHBOARD <span>Distributor</span></h1>
        <hr>
        <div class="row">
          <div class="col-md-2 pull-right">
            <div class="form-group">
              <label for="">Company</label>
              <select class="form-control" id="company" name="company">
                <option value="7000">7000</option>
                <option value="5000">5000</option>
              </select>
            </div>
          </div>
        </div>

        <!-- STATUS APPROVAL -->

        <div class="row">

              <!-- STATUS TARGET HARIAN -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Status Target Harian
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div id="loadingStatInv" class="back-loading">
                      <div class="loader"></div>
                    </div>
                    <div class="row">
                      <form id="reloadTargetHarian" action="index.html" method="post">
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="statInvDate1" id="statInvDate1" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>Tanggal</label>
                            <input type="date" class="form-control" name="statInvDate2" id="statInvDate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
<!--                        <div class="col-md-5">
                          <div class="form-group">
                            <label>Chose Expediture</label>
                            <select class="form-control" id="vendor" name="vendor">
                              <option value="">All</option>
                            </select>
                          </div>
                        </div>-->
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              <i class="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div id="cetInv" style="display:none;" class="row">
                      <div class="col-md-8">
                        <div style="width: 100%;">
                            <canvas height="250px;" style="margin-right:20px;" id="doghnutStatInv"></canvas>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <table class="table table-hover" style="box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px;">
                          <thead style="background: #009688;color: #fff;">
                            <tr>
                              <th style="text-align: center;">Labels</th>
                              <th style="text-align: center;">Jumlah</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #009688;border-radius:  50%;margin:  0 auto;"></div></td>
                              <td style="text-align:center;" id="statVerTerima"></td>
                            </tr>
                            <tr>
                              <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #f44336;border-radius:  50%;margin:  0 auto;"></div></td>
                              <td style="text-align: center;" id="statVerTolak"></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <!-- <div class="col-lg-6">
                        <div style="width:150px;">
                          <h1 style="font-size: 15px;font-weight: bold;border-bottom: 2px solid #dddddd;padding-bottom: 6px;color: #111;">Top 10 Belum Cetak</h1>
                        </div>
                        <table id="detailStatInv" class="table" style="margin-top:10px;">
                          <thead>
                            <tr>
                              <th style="font-size:13px;">No. Inv</th>
                              <th style="font-size:13px;">Vendor</th>
                              <th style="font-size:13px;">Tanggal</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                        </table>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <!-- END STATUS TARGET HARIAN -->

              <!-- STATUS TARGET BULANAN -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Status Target Bulanan
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div id="loadingStatTag" class="back-loading">
                      <div class="loader"></div>
                    </div>
                    <div class="row">
                      <form id="reloadTargetBulanan" action="pixxxo.html" method="post">
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="statTagDate1" id="statTagDate1" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-5">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="statTagDate2" id="statTagDate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-2">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">
                              <i class="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div id="terTag" style="display:none;" class="row">
                      <div class="col-md-8">
                        <div style="width: 100%;">
                            <canvas height="250px;" style="margin-right:20px;" id="doghnutStatTag"></canvas>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <table class="table table-hover" style="box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 12px 0px;">
                          <thead style="background: #009688;color: #fff;">
                            <tr>
                              <th style="text-align: center;">Labels</th>
                              <th style="text-align: center;">Jumlah</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #009688;border-radius:  50%;margin:  0 auto;"></div></td>
                              <td style="text-align:center;" id="statVerTerima"></td>
                            </tr>
                            <tr>
                              <td style="text-align: center;"><div style="height: 20px;width: 20px;background: #f44336;border-radius:  50%;margin:  0 auto;"></div></td>
                              <td style="text-align: center;" id="statVerTolak"></td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <!-- <div class="col-lg-6">
                        <div style="width:150px;">
                          <h1 style="font-size: 15px;font-weight: bold;border-bottom: 2px solid #dddddd;padding-bottom: 6px;color: #111;">Top 10 Belum Terima</h1>
                        </div>
                        <table id="detailStatTag" class="table" style="margin-top:10px;">
                          <thead>
                            <tr>
                              <th style="font-size:13px;">No. Inv</th>
                              <th style="font-size:13px;">Vendor</th>
                              <th style="font-size:13px;">Tanggal</th>
                            </tr>
                          </thead>
                          <tbody>
                          </tbody>
                        </table>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <!-- END STATUS TARGET BULANAN -->
          </div>
            <!-- end of row -->
          <div class="col-md-12">
              <div class="row">
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">
                      Surat Perintah Jalan <span>(SPJ)</span>
                    </h3>
                  </div>
                  <div class="panel-body">
                    <div class="row">
                      <form id="reloadChart" action="index.html" method="post">
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>From</label>
                            <input type="date" class="form-control" name="spjdate1" id="spjdate1" value="<?= date("Y-m")."-01" ?>">
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>To</label>
                            <input type="date" class="form-control" name="spjdate2" id="spjdate2" value="<?= date("Y-m-d") ?>">
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-danger btn-aeng form-control" name="button">Filter</button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div style="display:none;" id="spj">
                      <div style="width:100%;">
                          <canvas id="lineChartSpj"></canvas>
                      </div>
                      
                    </div>
                    <div id="loadingSpj" class="back-loading">
                      <div class="loader"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- end of content-dashboard -->
        
      <script src="../Templates/plugins/jquery/jQuery-2.1.4.min.js"></script>
      <script src="../Templates/plugins/bootstrap/js/bootstrap.min.js"></script>
      <script src="../Templates/plugins/chartjs/Chart.bundle.min.js" charset="utf-8"></script>
      <script src="../Templates/plugins/chartjs/utils.js" charset="utf-8"></script>
      <script src="../Templates/plugins/toastr/toastr.min.js" charset="utf-8"></script>
      <script src="jsControll/spj_dist.js" charset="utf-8"></script>
      <script src="jsControll/target_harian_dist.js" charset="utf-8"></script>
      <script src="jsControll/target_bulanan_dist.js" charset="utf-8"></script>
<!--      <script src="jsControll/status_penerimaan.js" charset="utf-8"></script>-->
      <script src="jsControll/status_inv_exp.js" charset="utf-8"></script>
      
      <script type="text/javascript">
        $("#company").on("change", function(){
          $("#reloadChart").trigger('submit');
          $("#reloadTargetHarian").trigger('submit');
          $("#reloadTargetBulanan").trigger('submit');          
        })
      </script>
  </body>
</html>
