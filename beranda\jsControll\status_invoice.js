var doghnutChartStatInv;

$("#statInvDate2, #statInvDate1").on("change blur", function(e){
  $("#vendor").find('option.r').remove();
  $("#vendor").val("");
  var statInvDate1 = $("#statInvDate1").val();
  var statInvDate2 = $("#statInvDate2").val();
  var result;
  $.ajax({
    url: 'xhr/model.php?p=vendor-request',
    type: 'GET',
    dataType: 'JSON',
    data: {
      statInvDate1 : statInvDate1,
      statInvDate2 : statInvDate2,
    }
  })
  .done(function() {
    result = true;
  })
  .fail(function() {
    result = false;
  })
  .always(function(data) {
    console.log(data);
    if(result){
      $.each(data, function(index, vendor) {
        $("#vendor").append('<option class="r" value="'+vendor.NO_VENDOR+'">'+vendor.NAMA_VENDOR+'</option>');
      });
    }
  });

})

$(document).ready(function() {
  var resultInv;
  var statInvDate1 = $("#statInvDate1").val();
  var statInvDate2 = $("#statInvDate2").val();
  var company = $("#company").val();
  var ex = $("#vendor").val();
  $.ajax({
    url: 'xhr/model.php?p=doghnut-stat-inv',
    type: 'GET',
    dataType: 'JSON',
    data: {
      statInvDate1: statInvDate1,
      statInvDate2: statInvDate2,
      vendor: ex,
      company: company
    },
    beforeSend: function(){
      $("#loadingStatInv").fadeIn(1000);
      $("#cetInv").hide();
    }
  })
  .done(function(data) {
    resultInv = true;
  })
  .fail(function() {
    resultInv = false;
  })
  .always(function(data) {
    $("#loadingStatInv").fadeOut(1000, function() {
      $("#cetInv").show(1000);
      if(resultInv){
        var config = {
            type: 'doughnut',
            data: data.grap,
            options: {
                responsive: true,
                legend: {
                  position: 'bottom'
                },
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Status Cetak Invoice'
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                },
                tooltips: {
                 callbacks: {
                     label: function(tooltipItem, data) {
                     var dataset = data.datasets[tooltipItem.datasetIndex];
                     var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                       return previousValue + currentValue;
                     });
                     var currentValue = dataset.data[tooltipItem.index];
                     var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                     return precentage + "%";
                   }
                 }
               },
               elements: {
                 arc: {
                   borderWidth: 0
                 }
               }
            }
        };
        var ctx = document.getElementById("doghnutStatInv").getContext("2d");
        doghnutChartStatInv = new Chart(ctx, config);
          if(data.dataDetail.length < 1){
            var contentStatTag = "<tr>"+
                                    "<td colspan='3' class='text-center' style='font-size:13px;'>NO DATA</td>"+
                                 "</tr>";
            $("#detailStatInv").find('tbody').append(contentStatTag);
          } else{
            $.each(data.dataDetail, function(index, data){
              var contentStatInv = "<tr>"+
                                      "<td style='font-size:13px;'>"+data.NO_INVOICE+"</td>"+
                                      "<td style='font-size:13px;'>"+data.NAMA_VENDOR+"</td>"+
                                      "<td style='font-size:13px;'>"+data.TGL_TERIMA+"</td>"+
                                   "</tr>";
              $("#detailStatInv").find('tbody').append(contentStatInv);
            })
          }
      } else{
        alert("Internal Server Error.. Sorry");
      }
    });
  });
});

$("#reloadStatInv").on("submit", function(e){
  e.preventDefault();
  doghnutChartStatInv.destroy();
  $("#detailStatInv").find('tbody tr').remove();
  var resultInv;
  var statInvDate1 = $("#statInvDate1").val();
  var statInvDate2 = $("#statInvDate2").val();
  var company = $("#company").val();
  var ex = $("#vendor").val();
  $.ajax({
    url: 'xhr/model.php?p=doghnut-stat-inv',
    type: 'GET',
    dataType: 'JSON',
    data: {
      statInvDate1: statInvDate1,
      statInvDate2: statInvDate2,
      vendor: ex,
      company: company
    },
    beforeSend: function(){
      $("#loadingStatInv").fadeIn(1000);
      $("#cetInv").hide();
    }
  })
  .done(function(data) {
    resultInv = true;
  })
  .fail(function() {
    resultInv = false;
  })
  .always(function(data) {
    $("#loadingStatInv").fadeOut(1000, function() {
      $("#cetInv").show(1000);
      if(resultInv){
        var config = {
            type: 'doughnut',
            data: data.grap,
            options: {
                responsive: true,
                legend: {
                  position: 'bottom'
                },
                maintainAspectRatio: false,
                title: {
                    display: true,
                    text: 'Status Cetak Invoice'
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                },
                tooltips: {
                 callbacks: {
                     label: function(tooltipItem, data) {
                     var dataset = data.datasets[tooltipItem.datasetIndex];
                     var total = dataset.data.reduce(function(previousValue, currentValue, currentIndex, array) {
                       return previousValue + currentValue;
                     });
                     var currentValue = dataset.data[tooltipItem.index];
                     var precentage = Math.floor(((currentValue/total) * 100)+0.5);
                     return precentage + "%";
                   }
                 }
               },
               elements: {
                 arc: {
                   borderWidth: 0
                 }
               }
            }
        };
        var ctx = document.getElementById("doghnutStatInv").getContext("2d");
        doghnutChartStatInv = new Chart(ctx, config);
          if(data.dataDetail.length < 1){
            var contentStatTag = "<tr>"+
                                    "<td colspan='3' class='text-center' style='font-size:13px;'>NO DATA</td>"+
                                 "</tr>";
            $("#detailStatInv").find('tbody').append(contentStatTag);
          } else{
            $.each(data.dataDetail, function(index, data){
              var contentStatInv = "<tr>"+
                                      "<td style='font-size:13px;'>"+data.NO_INVOICE+"</td>"+
                                      "<td style='font-size:13px;'>"+data.NAMA_VENDOR+"</td>"+
                                      "<td style='font-size:13px;'>"+data.TGL_TERIMA+"</td>"+
                                   "</tr>";
              $("#detailStatInv").find('tbody').append(contentStatInv);
            })
          }
      } else{
        alert("Internal Server Error.. Sorry");
      }
    });
  });
})
