<? 
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

function getOraHdr($org, $no_shp_sap){
	$fungsi2=new crm_fungsi();
	$conn2=$fungsi2->crm_koneksi();
	$sql0="SELECT EX_TRANS_HDR.*,to_char(TANGGAL_BONGKAR, 'YYYYMMDD') as tanggalbong<PERSON>,to_char(TANGGAL_BONGKAR, 'HH24MISS') as jambongkar , to_char(TANGGAL_DATANG, 'HH24MISS') as JAM_TANGGAL_DATANG, to_char(TANGGAL_BONGKAR,'HH24MISS') as JAM_TANGGAL_BONGKAR FROM EX_TRANS_HDR where ORG = '".$org."' and no_shp_trn='".$no_shp_sap."'";
	$query0= @oci_parse($conn2, $sql0);
	@oci_execute($query0);
	return $data=@oci_fetch_array($query0);
}


$distr=$fungsi->sapcode($distr);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");

$page="expeditur.php";
$currentPage="expeditur.php";
$komen="";

if(isset($_POST['cari'])){
		$nopol=$_POST['nopol'];
		$exp= $_POST['exp'];
		$tglm = $_POST['tglp'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgld'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;
// print_r ($_POST);

		$sap = new SAPConnection(); 
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_SELECT_TRANSHDR");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
		$fce->I_NMORG='2000';
		$fce->I_NMPLAN='2403';
    	$fce->I_NO_POLISI=$nopol ;
		//$fce->I_NO_SHIPMENT= ;
		$fce->I_TGL_CMPLT_FR=$tglm ;
		$fce->I_TGL_CMPLT_TO=$tgls;
        $fce->I_NO_EXPEDITUR=$exp;
	  

		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->RETURN_DATA->Reset();
			$s=0;
			while ( $fce->RETURN_DATA->Next() ){
							$NMORG[$s] = $fce->RETURN_DATA->row['NMORG'];
							$NMPLAN[$s] = $fce->RETURN_DATA->row['NMPLAN'];
							$NO_TRANSAKSI[$s] = $fce->RETURN_DATA->row['NO_TRANSAKSI'];
							$POSNR[$s] = $fce->RETURN_DATA->row['POSNR'];
							$NO_SHIPMENT[$s] = $fce->RETURN_DATA->row['NO_SHIPMENT'];
							$NO_BOOKING[$s] = $fce->RETURN_DATA->row['NO_BOOKING'];
							$TIPE_ANTRI[$s] = $fce->RETURN_DATA->row['TIPE_ANTRI'];
							$STATUS_TRANS[$s] = $fce->RETURN_DATA->row['STATUS_TRANS'];
							$STATUS_HOLD[$s] = $fce->RETURN_DATA->row['STATUS_HOLD'];
							$APPROVE_HOLD_BY[$s] = $fce->RETURN_DATA->row['APPROVE_HOLD_BY'];
							$APPROVE_REASON[$s] = $fce->RETURN_DATA->row['APPROVE_REASON'];
							$ORDER_TYPE[$s] = $fce->RETURN_DATA->row['ORDER_TYPE'];
							$LOADING_POINT[$s] = $fce->RETURN_DATA->row['LOADING_POINT'];
							$CURAH_OR_BAG[$s] = $fce->RETURN_DATA->row['CURAH_OR_BAG'];
							$BERAT_ISI[$s] = $fce->RETURN_DATA->row['BERAT_ISI'];
							$BERAT_KOSONG[$s] = $fce->RETURN_DATA->row['BERAT_KOSONG'];
							$BERAT_BERSIH[$s] = $fce->RETURN_DATA->row['BERAT_BERSIH'];
							$BERAT_TOTAL_DO[$s] = $fce->RETURN_DATA->row['BERAT_TOTAL_DO'];
							$BERAT_DELTA[$s] = $fce->RETURN_DATA->row['BERAT_DELTA'];
							$TOTAL_QTY[$s] = $fce->RETURN_DATA->row['TOTAL_QTY'];
							$JALUR_ANTRI[$s] = $fce->RETURN_DATA->row['JALUR_ANTRI'];
							$JALUR_CONVEYOR[$s] = $fce->RETURN_DATA->row['JALUR_CONVEYOR'];
							$TIPE_TRUK[$s] = $fce->RETURN_DATA->row['TIPE_TRUK'];
							$NO_POLISI[$s] = $fce->RETURN_DATA->row['NO_POLISI'];
							$NO_STNK[$s] = $fce->RETURN_DATA->row['NO_STNK'];
							$NO_SIM[$s] = $fce->RETURN_DATA->row['NO_SIM'];
							$NAMA_SUPIR[$s] = $fce->RETURN_DATA->row['NAMA_SUPIR'];
							$ID_CARD[$s] = $fce->RETURN_DATA->row['ID_CARD'];
							$NO_SEGEL[$s] = $fce->RETURN_DATA->row['NO_SEGEL'];
							$NO_KTG_ACT[$s] = $fce->RETURN_DATA->row['NO_KTG_ACT'];
							$KTG_DESC_ACT[$s] = $fce->RETURN_DATA->row['KTG_DESC_ACT'];
							$JML_KTG_ACT[$s] = $fce->RETURN_DATA->row['JML_KTG_ACT'];
							$NO_KTG[$s] = $fce->RETURN_DATA->row['NO_KTG'];
							$KTG_DESC[$s] = $fce->RETURN_DATA->row['KTG_DESC'];
							$CHG_KTG[$s] = $fce->RETURN_DATA->row['CHG_KTG'];
							$REASON_CHG[$s] = $fce->RETURN_DATA->row['REASON_CHG'];
							$NO_KTG_OTH[$s] = $fce->RETURN_DATA->row['NO_KTG_OTH'];
							$KTG_DESC_OTH[$s] = $fce->RETURN_DATA->row['KTG_DESC_OTH'];
							$ADJ_STATUS[$s] = $fce->RETURN_DATA->row['ADJ_STATUS'];
							$NO_ADJ[$s] = $fce->RETURN_DATA->row['NO_ADJ'];
							$PETUGAS_ADJ[$s] = $fce->RETURN_DATA->row['PETUGAS_ADJ'];
							$APPROVE_ADJ_BY[$s] = $fce->RETURN_DATA->row['APPROVE_ADJ_BY'];
							$CREATE_ADJ_DATE[$s] = $fce->RETURN_DATA->row['CREATE_ADJ_DATE'];
							$CREATE_JOURNAL[$s] = $fce->RETURN_DATA->row['CREATE_JOURNAL'];
							$PTGS_ANTRIAN[$s] = $fce->RETURN_DATA->row['PTGS_ANTRIAN'];
							$TGL_ANTRI[$s] = $fce->RETURN_DATA->row['TGL_ANTRI'] ;
							$JAM_ANTRI[$s] = $fce->RETURN_DATA->row['JAM_ANTRI'];
							$PTGS_MASUK[$s] = $fce->RETURN_DATA->row['PTGS_MASUK'];
							$TGL_MASUK[$s] = $fce->RETURN_DATA->row['TGL_MASUK'];
							$JAM_MASUK[$s] = $fce->RETURN_DATA->row['JAM_MASUK'];
							$PTGS_KTG[$s] = $fce->RETURN_DATA->row['PTGS_KTG'];
							$TGL_KTG[$s] = $fce->RETURN_DATA->row['TGL_KTG'];
							$JAM_KTG[$s] = $fce->RETURN_DATA->row['JAM_KTG'];
							$PTGS_ISI[$s] = $fce->RETURN_DATA->row['PTGS_ISI'];
							$TGL_ISI[$s] = $fce->RETURN_DATA->row['TGL_ISI'];
							$JAM_ISI[$s] = $fce->RETURN_DATA->row['JAM_ISI'];
							$PTGS_CMPLT[$s] = $fce->RETURN_DATA->row['PTGS_CMPLT'];
							$TGL_CMPLT[$s] = $fce->RETURN_DATA->row['TGL_CMPLT'];
							$JAM_CMPLT[$s] = $fce->RETURN_DATA->row['JAM_CMPLT'];
							$STS_ANTRI_PRINT[$s] = $fce->RETURN_DATA->row['STS_ANTRI_PRINT'];
							$CNT_PRINT_ANTRI[$s] = $fce->RETURN_DATA->row['CNT_PRINT_ANTRI'];
							$STS_MASUK_PRINT[$s] = $fce->RETURN_DATA->row['STS_MASUK_PRINT'];
							$CNT_PRINT_MASUK[$s] = $fce->RETURN_DATA->row['CNT_PRINT_MASUK'];
							$STS_KELUAR_PRINT[$s] = $fce->RETURN_DATA->row['STS_KELUAR_PRINT'];
							$CNT_PRINT_KELUAR[$s] = $fce->RETURN_DATA->row['CNT_PRINT_KELUAR'];
							$CNT_PRINT_KELUAR[$s] = $fce->RETURN_DATA->row['CNT_PRINT_KELUAR'];
							$LAST_UPDATE_DATE[$s] = $fce->RETURN_DATA->row['LAST_UPDATE_DATE'];
							$LAST_UPDATE_TIME[$s] = $fce->RETURN_DATA->row['LAST_UPDATE_TIME'];
							$LAST_UPDATED_BY[$s] = $fce->RETURN_DATA->row['LAST_UPDATED_BY'];
							$SHP_POINT[$s] = $fce->RETURN_DATA->row['SHP_POINT'];
							$DEL[$s] = $fce->RETURN_DATA->row['DEL'];
							$COMPLETED[$s] = $fce->RETURN_DATA->row['COMPLETED'];
							$DOC_NUM[$s] = $fce->RETURN_DATA->row['DOC_NUM'];
							$JALUR_CAD[$s] = $fce->RETURN_DATA->row['JALUR_CAD'];
							$KTG_RUSAK[$s] = $fce->RETURN_DATA->row['KTG_RUSAK'];
							$SPARE_KTG[$s] = $fce->RETURN_DATA->row['SPARE_KTG'];
							$STATUS_KTG_PECAH[$s] = $fce->RETURN_DATA->row['STATUS_KTG_PECAH'];
							$STATUS_KTG_RUSAK[$s] = $fce->RETURN_DATA->row['STATUS_KTG_RUSAK'];
							$STATUS_SPARE_KTG[$s] = $fce->RETURN_DATA->row['STATUS_SPARE_KTG'];
							$QUANTITY_RECEIPT[$s] = $fce->RETURN_DATA->row['QUANTITY_RECEIPT'];
							$PETUGAS_RECEIPT[$s] = $fce->RETURN_DATA->row['PETUGAS_RECEIPT'];
							$TGL_RECEIPT[$s] = $fce->RETURN_DATA->row['TGL_RECEIPT'];
							$JAM_RECEIPT[$s] = $fce->RETURN_DATA->row['JAM_RECEIPT'];
							$STATUS_RECEIPT[$s] = $fce->RETURN_DATA->row['STATUS_RECEIPT'];
							$IS_SG[$s] = $fce->RETURN_DATA->row['IS_SG'];
							$ATTRIBUTE1[$s] = $fce->RETURN_DATA->row['ATTRIBUTE1'];
							$ATTRIBUTE2[$s] = $fce->RETURN_DATA->row['ATTRIBUTE2'];
							$KAPASITAS[$s] = $fce->RETURN_DATA->row['KAPASITAS'];
							$SYSTEM_BONGKAR[$s] = $fce->RETURN_DATA->row['SYSTEM_BONGKAR'];
							$NO_EXPEDITUR[$s] = $fce->RETURN_DATA->row['NO_EXPEDITUR'];
							$NAMA_EXPEDITUR[$s] = $fce->RETURN_DATA->row['NAMA_EXPEDITUR'];
							$NO_SPPS[$s] = $fce->RETURN_DATA->row['NO_SPPS'];
							$PRIORITAS[$s] = $fce->RETURN_DATA->row['PRIORITAS'];
							$BZIRK[$s] = $fce->RETURN_DATA->row['BZIRK'];
							$BZTXT[$s] = $fce->RETURN_DATA->row['BZTXT'];
							$TGL_MATCH1[$s] = $fce->RETURN_DATA->row['TGL_MATCH1'];
							$JAM_MATCH1[$s] = $fce->RETURN_DATA->row['JAM_MATCH1'];
							$TGL_MATCH2[$s] = $fce->RETURN_DATA->row['TGL_MATCH2'];
							$JAM_MATCH2[$s] = $fce->RETURN_DATA->row['JAM_MATCH2'];
							$PETUGAS_MATCH1[$s] = $fce->RETURN_DATA->row['PETUGAS_MATCH1'];
							$PETUGAS_MATCH2[$s] = $fce->RETURN_DATA->row['PETUGAS_MATCH2'];
							$NAMA_KAPAL[$s] = $fce->RETURN_DATA->row['NAMA_KAPAL'];
							$SHIFT[$s] = $fce->RETURN_DATA->row['SHIFT'];
							$POSO_TMP[$s] = $fce->RETURN_DATA->row['POSO_TMP'];
							$POSNR_TMP[$s] = $fce->RETURN_DATA->row['POSNR_TMP'];
							$MATNR_TMP[$s] = $fce->RETURN_DATA->row['MATNR_TMP'];
							$MAKTX_TMP[$s] = $fce->RETURN_DATA->row['MAKTX_TMP'];
							$UOM_TMP[$s] = $fce->RETURN_DATA->row['UOM_TMP'];
							$ROUTE_TMP[$s] = $fce->RETURN_DATA->row['ROUTE_TMP'];
							$KAPASITAS_MASTER[$s] = $fce->RETURN_DATA->row['KAPASITAS_MASTER'];
							$SHIPTO_ADDRESS[$s] = $fce->RETURN_DATA->row['SHIPTO_ADDRESS'];
							$KODE_PORT[$s] = $fce->RETURN_DATA->row['KODE_PORT'];
							$NMPORT[$s] = $fce->RETURN_DATA->row['NMPORT'];
							$SAL_GROUP[$s] = $fce->RETURN_DATA->row['SAL_GROUP'];
							$NMSAL_GROUP[$s] = $fce->RETURN_DATA->row['NMSAL_GROUP'];
							$SAL_OFFICE[$s] = $fce->RETURN_DATA->row['SAL_OFFICE'];
							$NMSAL_OFFICE[$s] = $fce->RETURN_DATA->row['NMSAL_OFFICE'];
							$EARTX[$s] = $fce->RETURN_DATA->row['EARTX'];
							$KODE_KECAMATAN[$s] = $fce->RETURN_DATA->row['KODE_KECAMATAN'];
							$NAMA_KECAMATAN[$s] = $fce->RETURN_DATA->row['NAMA_KECAMATAN'];
							$KODE_CHECKPOINT[$s] = $fce->RETURN_DATA->row['KODE_CHECKPOINT'];
							$NAMA_CHECKPOINT[$s] = $fce->RETURN_DATA->row['NAMA_CHECKPOINT'];
							$TGL_CHECKPOINT[$s] = $fce->RETURN_DATA->row['TGL_CHECKPOINT'];
							$JAM_CHECKPOINT[$s] = $fce->RETURN_DATA->row['JAM_CHECKPOINT'];
							$CHECK_POINT_BY[$s] = $fce->RETURN_DATA->row['CHECK_POINT_BY'];
							$WARNA_PLAT[$s] = $fce->RETURN_DATA->row['WARNA_PLAT'];
							$ALASAN_CANCEL[$s] = $fce->RETURN_DATA->row['ALASAN_CANCEL'];
							$TGL_CANCEL[$s] = $fce->RETURN_DATA->row['TGL_CANCEL'];
							$IDOC_CANCEL[$s] = $fce->RETURN_DATA->row['IDOC_CANCEL'];
							$DISTRIK[$s] = $fce->RETURN_DATA->row['DISTRIK'];

							//$TGL_JAM_ANTRI[$s] = $fungsi->getAntriTerakhir('2000',$fce->RETURN_DATA->row['NO_POLISI']);
/*
							$sqli = "insert into lampiran(id,no_shipment,no_expeditur,nama_expeditur,qty_shipment,tgl_antri,jam_antri,,tgl_keluar,jam_keluar) values ('".$NO_SHIPMENT."','".$NO_EXPEDITUR."','".$NO_TRANSAKSI."','".$NAMA_EXPEDITUR."','".$TOTAL_QTY."','".$TGL_ANTRI."','".$JAM_ANTRI."','".($TGL_CMPLT."','".$JAM_CMPLT."','".$TOTAL_QTY."','".$TOTAL_UM."','".$TOTAL_TON."','".$TGL_CMPLT."')";
				            @mysql_query($sqli);
*/
							$TOTAL_JAM1[$s] = $fungsi->getDurasi1(
								$fce->RETURN_DATA->row['TGL_ANTRI'],
								$fce->RETURN_DATA->row['JAM_ANTRI'],
								$fce->RETURN_DATA->row['TGL_CMPLT'],
								$fce->RETURN_DATA->row['JAM_CMPLT']);

							$TOTAL_JAM2[$s] = $fungsi->getDurasi1(
								$fce->RETURN_DATA->row['TGL_CMPLT'],
								$fce->RETURN_DATA->row['JAM_CMPLT'],
								$fce->RETURN_DATA->row['TGL_MASUK'],
								$fce->RETURN_DATA->row['JAM_MASUK']);

							

							
							$dat_hdr = getOraHdr("2000",$fce->RETURN_DATA->row['NO_SHIPMENT']);
							$no_spj[$s] = $dat_hdr['NO_SHP_TRN'];
							$QTY_SHP[$s]=$dat_hdr['QTY_SHP'];
							$TOTAL_KLAIM_KTG[$s]=$dat_hdr['TOTAL_KLAIM_KTG'];
							$TANGGAL_DATANG[$s]=$dat_hdr['TANGGAL_DATANG'];
							$TANGGAL_BONGKAR[$s]=$dat_hdr['TANGGAL_BONGKAR'];
							$TOTAL_KLAIM_SEMEN[$s]=$dat_hdr['TOTAL_KLAIM_SEMEN'];
							$KODE_PRODUK[$s]=$dat_hdr['KODE_PRODUK'];
							$tanggalbongkar[$s]=$dat_hdr['tanggalbongkar'];
							$jambongkar[$s]=$dat_hdr['jambongkar'];
														
				$s++;
				$tglantri[$fce->RETURN_DATA->row['TGL_ANTRI']];
				
				
		}
		}else

        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($NO_TRANSAKSI);
		//var_dump($dat_hdr);

		
		
	
}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="500" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Lampiran Ekspeditur </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="500" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Lampiran Ekspeditur  </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('tgl1','','R','tgl2','','R');return document.hasil">
  <table width="500" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Expeditur </td>
      <td  class="puso">:</td>
      <td ><input name="exp" type="text" id="exp" value="" size="10" maxlength="10"/></td></tr>
	<tr>
      <td  class="puso">No Polisi </td>
      <td  class="puso">:</td>
      <td ><input name="nopol" type="text" id="nopol" value="" size="10" maxlength="10"/></td></tr>
    <tr>
      <td  class="puso">Priode</td>
      <td  class="puso">:</td>
      <td ><input name="tglp" type="text" id="tglp" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tglp');"/>&nbsp; s.d &nbsp;
	<input name="tgld" type="text" id="tgld" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgld');"/></td>
    </tr>
	<tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />

<?
	
	if($total>0) {
?>

	<div align="center">
	<table width="1000" align="center" class="adminlist" border='0'>
	  <tr class="quote">
	    <td rowspan="2"align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td rowspan="2"align="center"><strong>Carrir Name.</strong></td>
		<td rowspan="2"align="center"><strong>Truck Number</strong></td>
		<td rowspan="2"align="center"><strong >Qty Shipment</strong></td>
		<td rowspan="2"align="center"><strong >Tanggal Antri</strong></td>
		<td rowspan="2"align="center"><strong >Jam Antri</strong></td>
		<td rowspan="2"align="center"><strong >Antri-Keluar Jam.menit</strong></td>
		<td rowspan="2"align="center"><strong >SG</strong></td>
		<td rowspan="2"align="center"><strong >Tanggal Keluar</strong></td>
		<td rowspan="2"align="center"><strong >Jam Keluar</strong></td>
		<td rowspan="2"align="center"><strong >Keluar Datang Jam,Menit</strong></td>
		<td rowspan="2"align="center"><strong >EXP</strong></td>
		<td rowspan="2"align="center"><strong >Tanggal Datang</strong></td>
		<td rowspan="2"align="center"><strong >Jam Datang</strong></td>
		<td rowspan="2"align="center"><strong >datang-bongkar jam,menit</strong></td>
		<td rowspan="2"align="center"><strong >SG</strong></td>
		<td rowspan="2"align="center"><strong >Tanggal Bongkar</strong></td>
		<td rowspan="2"align="center"><strong >JAm Bongkar</strong></td>
		<td rowspan="2"align="center"><strong >Tanggal Masuk</strong></td>
		<td rowspan="2"align="center"><strong >Jam Masuk</strong></td>
		<td rowspan="2"align="center"><strong >Bongkar-Antri</strong></td>
		<td rowspan="2"align="center"><strong >EXP</strong></td>
		<td rowspan="2"align="center"><strong >Keluar Masuk jam,Menit</strong></td>
		<td rowspan="2"align="center"><strong >EXP</strong></td>
		<td colspan="2"align="center"><strong >Claim</strong></td>
	 </tr>
	 <tr class="quote">
		<td align="center"><strong >Semen</strong></td>
		<td align="center"><strong >Zak</strong></td>></tr>
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$TOTAL_QTY[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	


		$dat_hdr = getOraHdr("2000",$NO_SHIPMENT[$i]);
		
		$no_spj = $dat_hdr['NO_SHP_TRN'];
		$QTY_SHP=$dat_hdr['QTY_SHP'];
		$TOTAL_KLAIM_KTG=$dat_hdr['TOTAL_KLAIM_KTG'];
		$TANGGAL_DATANG=$dat_hdr['TANGGAL_DATANG'];
		$tgl_b=$dat_hdr['TANGGAL_BONGKAR'];
		$TOTAL_KLAIM_SEMEN=$dat_hdr['TOTAL_KLAIM_SEMEN'];
		$KODE_PRODUK=$dat_hdr['KODE_PRODUK'];
		$jam_tgl_dtng=$dat_hdr['JAM_TANGGAL_DATANG'];
		$jam_tgl_bngkr=$dat_hdr['JAM_TANGGAL_BONGKAR'];
		$tanggalbongkar=$dat_hdr['TANGGALBONGKAR'];
		$jambongkar=$dat_hdr['JAMBONGKAR'];
		$tgl_dtg=$dat_hdr['TANGGAL_DATANG'];
		//var_dump($dat_hdr);
		//echo $dat_hdr['JAM_TANGGAL_DATANG']; 
		//echo "----------";
		//echo $dat_hdr['NO_SHP_TRN'];
         
		
		       
		$datang_bongkar[$i] = $fungsi->getDurasi1(
		$dat_hdr['TANGGALBONGKAR'],
		$dat_hdr['JAMBONGKAR'],
		$TGL_MASUK[$i],
		$JAM_MASUK[$i]);
         //echo "<br>============================================<br>";
		 $truk_balik = $fungsi->getAntriTerakhir('2000',$NO_POLISI[$i]);
        // print_r($truk_balik);
	$kunci = $NO_POLISI[$i].$TGL_ANTRI[$i].$JAM_ANTRI[$i];
	$tgljam_balik = $truk_balik[$kunci];
	$tgljam_balik = explode("|",$tgljam_balik);
	$tgl_balik = $tgljam_balik[0];
	$jam_balik = $tgljam_balik[1];

	    $bongkar_antri[$i] = $fungsi->getDurasi1(
	    $tgl_balik,
		$jam_balik,
		$dat_hdr['TANGGALBONGKAR'],
		$dat_hdr['JAMBONGKAR']);

		$keluar_masuk[$i] = $fungsi->getDurasi1(
		$tgl_balik,
		$jam_balik,
		$TGL_CMPLT[$i],
	    $JAM_CMPLT[$i]);

		?>
		<td align="center"><? echo $b."-"?></td>
		<td align="left"><? echo $NAMA_EXPEDITUR[$i]; ?></td>
		<td align="left"><? echo $NO_POLISI[$i]; ?></td>
		<td align="left"><? echo $TOTAL_QTY[$i]; ?></td>
		<td align="left"><? $thn=substr($TGL_ANTRI[$i],0,4);
							$bln=substr($TGL_ANTRI[$i],4,2);
							$hr=substr($TGL_ANTRI[$i],6,2);
							$tglantri=$hr.'-'.$bln.'-'.$thn;
							echo $tglantri;  ?></td>
		<td align="left"><? $jam=substr($JAM_ANTRI[$i],0,2);
							$mnt=substr($JAM_ANTRI[$i],2,2);
							$dtk=substr($JAM_ANTRI[$i],4,2);
							$jamantri=$jam.':'.$mnt.':'.$dtk;
							echo $jamantri; ?></td>
		<td align="left"><? $jam=substr($TOTAL_JAM1[$i],0,2);
							$mnt=substr($TOTAL_JAM1[$i],2,2);
							$dtk=substr($TOTAL_JAM1[$i],4,2);
							$totaljam1=$jam.':'.$mnt.':'.$dtk;
							echo $totaljam1; ?></td>		
		<td align="left"><? echo $totaljam1; ?></td>	
		<td align="left"><? $thn=substr($TGL_CMPLT[$i],0,4);
							$bln=substr($TGL_CMPLT[$i],4,2);
							$hr=substr($TGL_CMPLT[$i],6,2);
							$tglcomplit=$hr.'-'.$bln.'-'.$thn;
							echo $tglcomplit;  ?></td>	
		<td align="left"><? $jam=substr($JAM_CMPLT[$i],0,2);
							$mnt=substr($JAM_CMPLT[$i],2,2);
							$dtk=substr($JAM_CMPLT[$i],4,2);
							$jamcomplit=$jam.':'.$mnt.':'.$dtk;
							echo $jamcomplit; ?></td>	
		<td align="left"><? $jam=substr($TOTAL_JAM2[$i],0,2);
							$mnt=substr($TOTAL_JAM2[$i],2,2);
							$dtk=substr($TOTAL_JAM2[$i],4,2);
							$totaljamcomplit=$jam.':'.$mnt.':'.$dtk;
							echo $totaljamcomplit; ?></td>	
		<td align="left"><? echo $totaljamcomplit; ?></td>	
		<td align="left"><? echo $tgl_dtg  ?></td>	
		<td align="left"><? $jam=substr($jam_tgl_dtng,0,2);
							$mnt=substr($jam_tgl_dtng,2,2);
							$dtk=substr($jam_tgl_dtng,4,2);
							$jamdatang=$jam.':'.$mnt.':'.$dtk;
							echo $jamdatang; ?></td>	
		<td align="left"><? $jam=substr($datang_bongkar[$i],0,2);
							$mnt=substr($datang_bongkar[$i],2,2);
							$dtk=substr($datang_bongkar[$i],4,2);
							$totaljam3=$jam.':'.$mnt.':'.$dtk;
							echo $totaljam3; ?></td>	
		<td align="left"><? echo $totaljam3; ?></td>
		<td align="left"><? echo $tgl_b; ?></td>
		<td align="left"><? $jam=substr($jam_tgl_bngkr,0,2);
							$mnt=substr($jam_tgl_bngkr,2,2);
							$dtk=substr($jam_tgl_bngkr,4,2);
							$jam_bngkr=$jam.':'.$mnt.':'.$dtk;
							echo $jam_bngkr; ?></td>
		<td align="left"><? $thn=substr( $tgl_balik ,0,4);
							$bln=substr( $tgl_balik ,4,2);
							$hr=substr( $tgl_balik ,6,2);
							$tgldatang=$hr.'-'.$bln.'-'.$thn;
							echo $tgldatang;  ?></td>
		<td align="left"><? $jam=substr($jam_balik,0,2);
							$mnt=substr($jam_balik,2,2);
							$dtk=substr($jam_balik,4,2);
							$jam_datang=$jam.':'.$mnt.':'.$dtk;
							echo $jam_datang; ?></td>
		<td align="left"><? $jam=substr($bongkar_antri[$i],0,2);
							$mnt=substr($bongkar_antri[$i],2,2);
							$dtk=substr($bongkar_antri[$i],4,2);
							$bongkarantri=$jam.':'.$mnt.':'.$dtk;
							echo $bongkarantri; ?></td>
		<td align="left"><? echo $bongkarantri; ?></td>
		<td align="left"><? $jam=substr($keluar_masuk[$i],0,2);
							$mnt=substr($keluar_masuk[$i],2,2);
							$dtk=substr($keluar_masuk[$i],4,2);
							$keluarmasuk=$jam.':'.$mnt.':'.$dtk;
							echo $keluarmasuk; ?></td>
		<td align="left"><? echo $keluarmasuk; ?></td>
		<td align="center"><? echo '0'; //$TOTAL_KLAIM_SEMEN; ?></td>
		<td align="center"><? echo '0';//$KODE_PRODUK; ?></td>
		
	
		

		</tr>
	  <? } ?><!--
                <tr>
                    <td></td>
                    <td colspan="6"><strong >TOTAL</strong ></td>
                    <td align="right"><strong ><? echo number_format($totaldo,3,".",","); ?></strong ></td>
                                      
                </tr>-->
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
