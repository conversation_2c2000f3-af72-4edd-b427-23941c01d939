<?xml version="1.0" encoding="windows-1250"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9,00"
	Name="Saprfc PHP 5.2.8 - RFC 7.10"
	ProjectGUID="{89AC1FF2-3486-4908-A584-5E60F711DA6B}"
	RootNamespace="Saprfc PHP 5"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release_TS|Win32"
			OutputDirectory=".\Release_PHP5"
			IntermediateDirectory=".\Release_PHP5"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Release_PHP5/saprfc.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="&quot;..\Build\Php.Build\php-5.2.8&quot;;&quot;..\Build\Php.Build\php-5.2.8\Zend&quot;;&quot;..\Build\Php.Build\php-5.2.8\TSRM&quot;;&quot;..\Build\Php.Build\php-5.2.8\main&quot;;&quot;..\Build\Php.Build\php-5.2.8\win32&quot;;&quot;C:\Program Files\Microsoft SDKs\Windows\v6.1\Include&quot;;..\Build\RFCSDK.710.Win32\include;..\Build\Php.Build\Win32\include"
				PreprocessorDefinitions="WIN32;SAPRFC_EXPORTS;COMPILE_DL_SAPRFC;ZEND_DEBUG=0;NDEBUG;_WINDOWS;_USRDLL;ZEND_WIN32;PHP_WIN32;_USE_32BIT_TIME_T;ZTS=1"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				DisableLanguageExtensions="false"
				PrecompiledHeaderFile=".\Release_PHP5/saprfc.pch"
				AssemblerListingLocation=".\Release_PHP5/"
				ObjectFile=".\Release_PHP5/"
				ProgramDataBaseFileName=".\Release_PHP5/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1030"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="php5ts.lib librfc32.lib"
				OutputFile=".\Release_PHP5/php_saprfc_528.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=".\Release_PHP5;..\Build\RFCSDK.710.Win32\lib;..\Build\Php.Build\Win32\lib;..\Build\Php.Runtime\Win32.528\dev"
				ProgramDatabaseFile=".\Release_PHP5/php_saprfc.pdb"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary=".\Release_PHP5/php_saprfc.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release_PHP5/saprfc.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="rfccal.c"
				>
				<FileConfiguration
					Name="Release_TS|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="saprfc.c"
				>
				<FileConfiguration
					Name="Release_TS|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="php_saprfc.h"
				>
			</File>
			<File
				RelativePath="rfccal.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
