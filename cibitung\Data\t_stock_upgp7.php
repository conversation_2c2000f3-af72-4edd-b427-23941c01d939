<?
	$conn = @mysql_connect ("10.15.5.71","sgg","sggroup") or die ("koneksi gagal");
	mysql_select_db ("eis", $conn);

	include_once("sapfrc/sapclasses/sap.php");

       $q_kode = mysql_query("select kd_plant from m_plant where kd_plant='2606'") ;//'2601','2405','2602','2604','2605','2606','2608')"); //2609	
	   while ($data = mysql_fetch_array($q_kode)) {
		echo "<br>".$kdplant = $data["kd_plant"];
	
	
		$sap = new SAPConnection();
		$sap->Connect("logon_data.conf");


		if ($sap->GetStatus() == SAPRFC_OK ) //$sap->Open ();
		   $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   $sap->PrintStatus();
		   exit;
		}

		$fce = &$sap->NewFunction ("Z_ZAPPSD_MM_VALSTOCK");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}	

		
		$tglstock = date('Ymd');
		$fce->I_BUKRS = '2000';
		$fce->I_WERKS = $kdplant;
		$fce->I_DATE = $tglstock;//date('Ymd', strtotime("-".$n_days." days"));//
		$fce->I_DATE_TO = $tglstock;//date('Ymd', strtotime("-".$n_days." days"));//
		//$fce->I_MATNR = ;
		$fce->I_FLAG = '2'; 
		$fce->I_SUM ='X';
		$fce->Call();
		
	echo	$q = "delete from sap_total_stock_gd_penyangga  where werks='".$kdplant."' tgl='".$tglstock."'";
		mysql_query($q);

		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->T_RETURNDATA->Reset();
			$s=0;
			while ( $fce->T_RETURNDATA->Next() ){
				$WERKS[$s] = $fce->T_RETURNDATA->row["WERKS"];
				$NAME1[$s] = $fce->T_RETURNDATA->row["NAME1"];
				$MATNR[$s] = $fce->T_RETURNDATA->row["MATNR"];
				$MAKTX[$s] = $fce->T_RETURNDATA->row["MAKTX"];
				$MTART[$s] = $fce->T_RETURNDATA->row["MTART"];
				$MTBEZ[$s] = $fce->T_RETURNDATA->row["MTBEZ"];
				$START_STOCK[$s] = $fce->T_RETURNDATA->row["START_STOCK"];
				$START_STOCKX[$s] = $fce->T_RETURNDATA->row["START_STOCKX"];
				$REC_TOTAL[$s] = $fce->T_RETURNDATA->row["REC_TOTAL"];
				$REC_TOTALX[$s] = $fce->T_RETURNDATA->row["REC_TOTALX"];
				$ISSUE_TOTAL[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTAL"];
				$ISSUE_TOTALX[$s] = $fce->T_RETURNDATA->row["ISSUE_TOTALX"];
				$END_STOCK[$s] = $fce->T_RETURNDATA->row["END_STOCK"];
				$END_STOCKX[$s] = $fce->T_RETURNDATA->row["END_STOCKX"];
				

				$conn = @mysql_connect ("10.15.5.71","sgg","sggroup") or die ("koneksi gagal");
				mysql_select_db ("eis", $conn);
				$q_stock ="INSERT INTO sap_total_stock_gd_penyangga (tgl,WERKS, NAME1, MATNR, MAKTX, MTAR, MTBEZ, START_STOCK, START_STOCKX, REC_TOTAL, REC_TOTALX, ISSUE_TOTAL, ISSUE_TOTALX, END_STOCK, END_STOCKX) values ('".$tglstock."','".$WERKS[$s]."','".$NAME1[$s]."','".$MATNR[$s]."','".$MAKTX[$s]."','".$MTART[$s]."','".$MTBEZ[$s]."','".$START_STOCK[$s]."','".$START_STOCKX[$s]."','".$REC_TOTAL[$s]."','".$REC_TOTALX[$s]."','".$ISSUE_TOTAL[$s]."','".$ISSUE_TOTALX[$s]."','".$END_STOCK[$s]."','".$END_STOCKX[$s]."')";

				$s++;
				$hasil=mysql_query($q_stock);
				if ($hasil) {
					echo "Success insert ".$s."Record for".$tgl."<br>";
				} else {
					echo ("<br>Maaf input data gagal");
				}
		       		
			}
		}

		$sap->Close();
		

	}

		
?> 