<? 
session_start();

mysql_connect("10.15.5.71","sgg","sggroup"); //koneksi database
mysql_select_db("eis");

if(isset($_POST['cari'])){
	   
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;
        
		$TES="&nbsp;-&nbsp;";
		$type = trim($_POST['type']);
		if($type=='121-301'){
			$Typeket="ZAK";
		}
		else if($type=='1470167000'){
			$Typeket="GUDANG GONDANG LEGI";
		}
		else if($type=='1580022000'){
			$Typeket="PT.SINAR INDAH KENCANA JAYA";
		}
		else if($type=='1590018000'){
			$Typeket="PT.SINAR INDAH PERKASA";
		}
		else if($type=='1300008000'){
			$Typeket="GUD. SUMBER BUANAJAYA";
		}
		else if($type=='1300001000'){
			$Typeket="GUD. SUMBER BUANAJAYA (jkt)";
		}
		else if($type=='00'){
			?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Nama Gudang Harus di Isi");
				//-->
				</SCRIPT>
	      	<?

			exit();
		}

$perintah="SELECT * FROM rfid_mandiri_spj WHERE ship_to_code LIKE'$type%'AND TGL_CMPLT BETWEEN '$tglm' AND '$tgls'GROUP BY no_shipment ORDER BY no_shipment ASC ";
// echo$perintah;
	$hasil=mysql_query($perintah);
	if (mysql_num_rows($hasil) < 1) {
		echo (" Tabel Masih Kosong "); }
		else {  
		$hasil=mysql_query($perintah);
		$s=0;
		while ($data=mysql_fetch_array($hasil)) {	
		    $NMPLANT[]=$data['nmplant'];
			$NMORG[]=$data['nmorg'];
			$NO_TRANSAKSI[]=$data['no_transaksi'];
			$NMSAL_OFFICE[]=$data['nmsal_office'];
			$NO_SHIPMENT[]=$data['no_shipment'];
			$TOTAL_QTY[]=$data['total_qty'];
			$NO_POLISI[]=$data['no_polisi'];
			$TGL_CMPLT[]=$data['tgl_cmplt'];
			$JAM_CMPLT[]=$data['jam_cmplt'];
			$NO_EXPEDITUR[]=$data['no_expeditur'];
			$SAL_OFFICE[]=$data['sal_office'];
			$TOTAL_QTYX[]=$data['total_qtyx'];
			$SHIP_TO_CODE[]=$data['ship_to_code'];
			$SHIP_TO_PARTY[]=$data['ship_to_party'];
			$LAST_UPDATE[]=$data['last_update'];
			$NAMA_KECAMATAN[]=$data['nama_kecamatan'];			

			$perintah1="SELECT no_shipment,tgl_retoko,jam_retoko FROM rfid_mandiri_spj WHERE no_shipment='".$data['no_shipment']."'AND tgl_retoko IS NOT NULL ";
 echo'<br>tesssssssssss'.$perintah1;
	$hasil1=mysql_query($perintah1);
	if (mysql_num_rows($hasil1) < 1) {
		echo (" Tabel Masih Kosong "); }
		else {  
		$hasil1=mysql_query($perintah1);
		$s=0;
		while ($data1=mysql_fetch_array($hasil1)) {	
		    $TGL_RETOKO[]=$data['tgl_retoko'];
			$JAM_RETOKO[]=$data['jam_retoko'];	

					    $or_username = "APPSGG";
				$or_password = "sgmerdeka99";
				$or_db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = cmsdb.sggrp.com)(PORT = 1521))) (CONNECT_DATA = (SID = CMSDB)(SERVER = DEDICATED)))';				
			 $conn = oci_connect($or_username, $or_password, $or_db);
             $sql0="SELECT NO_SPJ_GR,CREATE_DATE,NOPOL FROM CRM_GR_SPJ WHERE NO_SPJ_GR='".$data['no_shipment']."'";
       echo'<b>tttt'. $sql0;
	         $query0= oci_parse($conn, $sql0);
	         oci_execute($query0);
	         while($data=oci_fetch_array($query0)){
		     $idnya[]=$data['ID'];
		     $tgl_create[]=$data['CREATE_DATE'];  
			 }

         $s++;
	   }
		}
		}
 }
}   $total=count($NMPLANT);

?>
<script language=javascript>
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />
 <!-- JQuery -->
<script src="../master/jq_fixheader/jquery.js" type="text/javascript"></script>
<script src="../master/jq_fixheader/jquery_002.js" type="text/javascript"></script>	

<script type="text/javascript">
	$(function () {
            $(".adminlist").fixedtableheader({ highlightrow: true, headerrowsize: 2 });
        });   
</script>
</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2" align='center'>RFID MANDIRI.
 </th>
</tr></table></div>
<div align="center">
<table width="400" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search </th>
</tr>
</table>
</div>
<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="400" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">TGL Kirim</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("01-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/> &nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>    
	<tr>
      <td  class="puso">Gudang / Silo</td>
      <td  class="puso">:</td>
      <td ><div id="plantdiv">
	    <select name=type>
	    <option value='00'>-------</option>
		<option value='1470167000'>GUDANG GONDANG LEGI</option>
		<option value='1580022000'>PT.SINAR INDAH KENCANA JAYA </option>
		<option value='1590018000'>PT.SINAR INDAH PERKASA</option>
		<option value='1300008000 '>GUD. SUMBER BUANAJAYA, PT ( LT )</option>
		<option value='1300001000 '>GUD. SUMBER BUANAJAYA, PT ( LT )</option>
	  </select>
	  </div></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<br />
<br />
<div align="center">
<table width="1000" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> <?ECHO $type;ECHO $TES;ECHO $Typeket;?></th>
</tr>
</table>
 <table width="1000" align="center" class="adminlist" >	
    <tr class="quote">
	    <td align="center"><strong>&nbsp;&nbsp;No</strong></td>
		<td align="center"><strong>Nopol</strong></td>
		<td align="center"><strong>No.SPJ</strong></td>
		<td align="center"><strong>Tanggal</strong></td>
	    <td align="center"><strong>Jam</strong></td>
		<td align="center"><strong >Qty(TO)</strong></td>
		<td align="center"><strong>Tgl Tiba</strong></td>
		<td align="center"><strong >Jam Tiba</strong></td>
		<td align="center"><strong>Tgl GR</strong></td>
		<td align="center"><strong >Jam GR</strong></td>
		<td align="center"><strong >Alamat</strong></td>				
	</tr>

	 <tr class="quote">	
	  
  <?  
  		for($i=0; $i<$total;$i++) {
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
	
		    $tanggal = date("Y-m-d", strtotime($tgl_create[$i]));
	        $jam = date("H:i:s", strtotime($tgl_create[$i]));
	?>
		<td align="center"><? echo $b."."?></td>
		<td align="left"><? echo $NO_POLISI[$i]; ?></td>
		<td align="left"><? echo $NO_SHIPMENT[$i]; ?></td>
		<td align="left"><? echo $TGL_CMPLT[$i];?></td>
		<td align="center"><? echo $JAM_CMPLT [$i];?></td>
		<td align="left"><? echo $TOTAL_QTY[$i]; ?></td>
		<td align="left"><? echo $TGL_RETOKO[$i]; ?></td>
		<td align="left"><? echo $JAM_RETOKO[$i];?></td>
		<td align="center"><? echo $tanggal;?></td>
		<td align="left"><? echo $jam;?></td>
		<td align="left"><? echo $NAMA_KECAMATAN[$i]; ?></td>
	</tr>
	  <?} ?>
	</table>	
	<p>&nbsp;</p>
	</div>


<p>&nbsp;</p>
</p>
</body>
</html>
