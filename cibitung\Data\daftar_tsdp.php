<? 
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=861;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr=$_SESSION['distr_id'];
$distr=$fungsi->sapcode($distr);
$kd_gdg=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"PLANT");

$page="daftar_tsdp.php";
$currentPage="daftar_tsdp.php";
$komen="";



if(isset($_POST['cari'])){
		$plant = $_POST['plant'];
		$shipto = $_POST['shipto'];
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		   echo $sap->PrintStatus();
		   exit;
		}

		$fce = $sap->NewFunction ("Z_ZAPPSD_RPT_REALISASI");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}
		
		//header entri
        echo '<br>org:'.$fce->X_VKORG = $user_org;
		echo '<br>tgl1:'.$fce->X_TGL1 = $tglm;
		echo '<br>tgl2:'.$fce->X_TGL2 = $tgls; // tgl sampai
		echo '<br>plant:'.$fce->X_WERKS = $plant; // plant
		echo '<br>dist:'.$fce->X_KUNNR = $distr; // distributor
		echo '<br>ship to:'.$fce->X_SHIP_TO_CODE = $shipto; // shipto
		$fce->X_NOFLAG = 'X'; // flag belum sampai gudang
        $fce->X_CURAHBAG = '20';
		
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			$fce->ZDATA->Reset();
			$s=0;
			while ( $fce->ZDATA->Next() ){
				$no_pp[$s] = $fce->ZDATA->row["NO_MINTA"];
				$no_kontrak[$s] = $fce->ZDATA->row["VGBEL"];
				$no_so[$s] = $fce->ZDATA->row["NO_SO"];
				$tgl_so[$s] = $fce->ZDATA->row["AUDAT"];
				$no_spj[$s] = $fce->ZDATA->row["NO_SPJ"];
				$no_do[$s] = $fce->ZDATA->row["NO_DO"];
				$pricegrp[$s] = $fce->ZDATA->row["PLTYP"];
				$inco[$s] = $fce->ZDATA->row["INCOTERM"];
				$tgl_spj[$s] = $fce->ZDATA->row["TGL_CMPLT"];
				$jam_spj[$s] = $fce->ZDATA->row["JAM_CMPLT"];
				$tgl_do[$s] = $fce->ZDATA->row["TGL_DO"];
				$tgl_pp[$s] = $fce->ZDATA->row["TGL_MINTA"];
				$qty_do[$s] = $fce->ZDATA->row["KWANTUM"];
				$nopol[$s] = $fce->ZDATA->row["NO_POLISI"];
				$no_spps[$s] = $fce->ZDATA->row["NO_SPPS"];
				$sopir[$s] = $fce->ZDATA->row["NAMA_SOPIR"];
				$kdshipto[$s] = $fce->ZDATA->row["KODE_DA"];
				$nmshipto[$s] = $fce->ZDATA->row["NAMA_TOKO"];
				$alamat[$s] = $fce->ZDATA->row["ALAMAT_DA"];
				$kddistrik[$s] = $fce->ZDATA->row["AREA"];
				$nmdistrik[$s] = $fce->ZDATA->row["NAMA_AREA"];
				$soldto[$s] = $fce->ZDATA->row["SOLD_TO"];
				$nama_sold[$s] = $fce->ZDATA->row["NAMA_SOLD_TO"];
				$kdplant[$s] = $fce->ZDATA->row["PLANT"];
				$nmplant[$s] = $fce->ZDATA->row["NAMA_PLANT"];
				$kdexp[$s] = $fce->ZDATA->row["NO_EXPEDITUR"];
				$nmexp[$s] = $fce->ZDATA->row["NAMA_EXPEDITUR"];
				$tstatus[$s] = $fce->ZDATA->row["STATUS"];
				$produk[$s] = $fce->ZDATA->row["PRODUK"];
				$uom[$s] = $fce->ZDATA->row["UOM"];
				$s++;
		}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($nopol);	
}

?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Truk Sedang Dalam Perjalanan </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">

<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Truk Sedang Dalam Perjalanan </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('tgl1','','R','tgl2','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Shipto </td>
      <td  class="puso">:</td>
      <td ><select name="shipto" id="shipto" onChange="document.tambah.nama_shipto.value=this.options[this.selectedIndex].title">
		<? $fungsi->shipto_gdg($kd_gdg); ?>     
		</select>	
		<input name="nama_shipto" type="hidden" id="nama_shipto" value="<?=$nama_shipto;?>" <?=$hanyabaca?> >
    </div>
</td></tr>
    <tr>
      <td  class="puso">Tanggal SPJ</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<form name="export" method="post" action="daftar_tsdp_xls.php">
<input name="plant" type="hidden" id="plant" value="<?=$plant?>"/>
<input name="no_so" id="no_so" class="inputlabel" type="hidden" size="10" maxlength="10" value="<?=$no_so;?>"/>
<input type="hidden" value="<?=$sold_to;?>" class="inputlabel" id="sold_to" name="sold_to" size="10">
<input name="status" type="hidden" id="status" size=12 value="<?=$status?>"/>
<input name="ship_to" type="hidden" id="ship_to" size=12 value="<?=$ship_to?>"/>
<input name="produk" type="hidden" id="produk" size=12 value="<?=$produk?>"/>
<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tgl1?>" />
<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgl2?>" />
<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	
&nbsp;&nbsp;
<input name="excel" type="Submit" id="excel" value="Export" />
	<table width="1000" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Truk Sedang Dalam Perjalanan</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1000" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		 <td align="center"><strong>Plant</strong></td>
		<td align="center"><strong >No SPJ</strong></td>
		<td align="center"><strong >Tgl SPJ</strong></td>
		<td align="center"><strong >Jam SPJ</strong></td>
		<td align="center"><strong >No Polisi</strong></td>
		<td align="center"><strong >Nama Sopir</strong></td>
		<td align="center"><strong >Material</strong></td>
		<td align="center"><strong >Qty DO</strong></td>
		<td align="center"><strong >UOM</strong></td>
		<td align="center"><strong >Kode Shipto</strong></td>
		<td align="center"><strong >Nama Shipto</strong></td>
		<td align="center"><strong >Alamat Shipto</strong></td>
		<td align="center"><strong >Distrik</strong></td>
		<td align="center"><strong >Nama Ekspeditur</strong></td>
      </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $nmplant[$i]; ?></td>
		<td align="left"><? echo $no_spj[$i]; ?></td>
		<td align="center"><? $thn=substr($tgl_spj[$i],0,4);
							$bln=substr($tgl_spj[$i],4,2);
							$hr=substr($tgl_spj[$i],6,2);
							$tglspj=$hr.'-'.$bln.'-'.$thn;
							echo $tglspj;  ?></td>
		<td align="left"><? $jam=substr($jam_spj[$i],0,2);
							$mnt=substr($jam_spj[$i],2,2);
							$dtk=substr($jam_spj[$i],4,2);
							$jamspj=$jam.':'.$mnt.':'.$dtk;
							echo $jamspj; ?></td>
		<td align="left"><? echo $nopol[$i]; ?></td>
		<td align="left"><? echo $sopir[$i]; ?></td>
		<td align="left"><? echo $produk[$i]; ?></td>
		<td align="right"><? echo $qty_do[$i]; ?></td>
		<td align="left"><? echo $uom[$i]; ?></td>
		<td align="left"><? echo $kdshipto[$i]; ?></td>
		<td align="left"><? echo $nmshipto[$i]; ?></td>
		<td align="left"><? echo $alamat[$i]; ?></td>
		<td align="left"><? echo $nmdistrik[$i]; ?></td>
		<td align="left"><? echo $nmexp[$i]; ?></td>
		</tr>
	  <? } ?>
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
