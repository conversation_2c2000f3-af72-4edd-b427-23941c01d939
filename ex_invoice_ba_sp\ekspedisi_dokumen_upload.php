<?php
require_once '../ex_ba_sp/Excel/reader.php';
require_once ('../include/ex_fungsi.php');
require_once '../security_helper.php';
sanitize_global_input();

$fungsi = new ex_fungsi();
$conn = $fungsi->ex_koneksi();

$org=$_SESSION['user_org'];
$no_invoice = $_GET['no_invoice'];

if($org != '1000'){
  echo 'Maaf, menu ini bukan untuk company yang saat ini anda pilih';
  exit;
};

function showMessage($message)
{
?>
  <link rel="stylesheet" href="../include/bootstrap/css/bootstrap.min.css">
  <div align="center" style="margin-left: auto;margin-right: auto;margin-top: 6em;padding: 15px;width: 429px;">
      <div class="alert alert-info" role="alert">
          <strong>Result!</strong>
          <br>
          <br>
          <div class="" role="alert"><?= $message ?></div>
          <br>
      </div>
  </div>
<?php
}

if (isset($_POST['upload']) && $_FILES['excel_file']['error'] == 0) {
    $file = $_FILES['excel_file']['tmp_name'];

    $data = new Spreadsheet_Excel_Reader();
    $data->setOutputEncoding('CP1251');
    $data->read($file);

    $sheet = $data->sheets[0];

    // Hitung jumlah baris
    $msg = "";
    for ($i = 3; $i <= 3; $i++) {
        $col1 = isset($sheet['cells'][$i][1]) ? $sheet['cells'][$i][1] : ''; // NO_EKSPEDISI
        $col2 = isset($sheet['cells'][$i][2]) ? $sheet['cells'][$i][2] : ''; // GROSS_AMOUNT

        $sql = "DELETE FROM EX_INVOICE_SMBR_EKS_BENDAHARA WHERE NO_INV = :no_invoice";
        $stmt = oci_parse($conn, $sql);
        oci_bind_by_name($stmt, ':no_invoice', $no_invoice);
        oci_execute($stmt);

        $field_names = array(
            'NO_INV', 'NO_EKSPEDISI', 'GROSS_AMOUNT'
        );

        $field_data = array(
            $no_invoice,
            $col1,
            $col2
        );

        $tablename = "EX_INVOICE_SMBR_EKS_BENDAHARA";

        $fungsi->insert_safe($conn, $field_names, $field_data, $tablename);
        $msg .= "upload success<br>";
    }

    echo $msg;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>    
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Upload Ekspedisi Dokumen</title>
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../css/tombol.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
</head>
<style>
table.excel {
	border-style:ridge;
	border-width:1;
	border-collapse:collapse;
	font-family:sans-serif;
	font-size:12px;
}
table.excel thead th, table.excel tbody th {
	background:#CCCCCC;
	border-style:ridge;
	border-width:1;
	text-align: center;
	vertical-align:bottom;
}
table.excel tbody th {
	text-align:center;
	width:20px;
}
table.excel tbody td {
	vertical-align:bottom;
}
table.excel tbody td {
    padding: 0 3px;
	border: 1px solid #EEEEEE;
}
</style>

<body>    
<div align="center">
<table width="800" align="center" class="adminheading" border="0">
<tr>
<th class="da2">Upload Ekspedisi Dokumen</th>
</tr></table>
</div>

<form method="post" name="upload" id="import" enctype="multipart/form-data">
    <table width="800" align="center" class="adminform">
        <tr height="30">
		  <td class="puso">&nbsp;</td>
		  <td class="puso">&nbsp;</td>
                  <td class="puso">&nbsp;</td>
	</tr>
        <tr>
            <td class="puso" width="150">&nbsp;&nbsp;&nbsp;Pilih File</td>
            <td class="puso">:</td>
            <td> <input name="excel_file" type="file"  class="button" accept=".xls, application/vnd.ms-excel" required></td>
        </tr>
        <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
            <td><input name="upload" type="submit"  class="button" value="Upload"></td>
        </tr>
    <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>

          <tr>
            <td class="puso">&nbsp;</td>
            <td class="puso">&nbsp;</td>
             <td class="puso">&nbsp;</td>
           
        </tr>
    </table>
</form>

<form method="post" name="download" id="import" enctype="multipart/form-data" action="posting_ppl_upload_template_xls.php">
    <table width="800" align="center" class="adminform">
        <tr><td colspan="3" class="puso">&nbsp;</td></tr>

        <tr>
            <td colspan="3" style="text-align:center;">
                <input type="submit" name="download_template" value="Download Template" />
            </td>
        </tr>

        <tr><td colspan="3" class="puso">&nbsp;</td></tr>
    </table>
</form>
<br><br>


   
<div align="center">
</div>
<p>&nbsp;</p>
</p>
<? include ('../include/ekor.php'); ?>

</body>
</html>
