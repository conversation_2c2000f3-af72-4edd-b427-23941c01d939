<?php
session_start();
include ('../include/crm_fungsi1.php');
#include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$sql = "SELECT  B.KD_DISTR, B.NM_DISTR,A.KD_GDG, A.NM_GDG,A.STOK
FROM CRM_STOK_GDG A, CRM_GR_SPJ B
WHERE A.KD_GDG = B.KD_GDG 
GROUP BY  A.KD_GDG, A.NM_GDG,A.STOK, B.KD_DISTR, B.NM_DISTR";
$query1= oci_parse($conn, $sql);
oci_execute($query1);
while($data=oci_fetch_array($query1)){
	$GUDANG[$data['KD_GDG']] = $data['KD_DISTR'];
	$NM_GUDANG[$data['KD_GDG']] = $data['NM_DISTR'];
        #echo "<br>";
        #echo $data['KD_DISTR']." ".$data['NM_DISTR']." ".$data['KD_GDG'];
}


$sql0="SELECT * FROM CRM_STOK_GDG  ";
//echo $sql0;
$query0= oci_parse($conn, $sql0);
oci_execute($query0);
$data_stok = array();
while($data=oci_fetch_array($query0)){
    $kode = $data['KD_GDG']."|".$data['KD_MATERIAL'];
    $data_stok[$kode] =  $data['STOK'];
}
#print_r($data_stok);
#exit;

// These are the coordinates the location we wish to plot.<br> // These are being passed in the URL, but we will set them to a 
// default if nothing is passed.


//$coord_array = file("gudang.txt");

$im = imagecreatefromjpeg("img_map/jatim_01.jpg");
//imagecopyresized($im, $im, 150, 150, 10, 10, 40, 40, 40, 40);

//imagecopyresized($dst_image, $src_image, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);
        
$red = imagecolorallocate ($im, 255,0,0);
$biru = imagecolorallocate($im, 0, 0, 255);

$scale_x = imagesx($im);
$scale_y = imagesy($im); 

/*
foreach($coord_array as $value)
{
	$co = explode(" ",$value);
	$pt = getlocationcoords($co[0], $co[1], $scale_x, $scale_y);
	imagefilledrectangle($im,$pt["x"]-1,$pt["y"]-1,$pt["x"]+1,$pt["y"]+1,$red);
}
 */

/*
int imagestring(image, font, x, y, string, color);
int image: Image identifier
int font: Font identifier
int x: X coordinate
int y: Y coordinate
string string: String to display
int color: Specified color
 */
$val_stok40 = $data_stok["1060006000|121-301-0110"];
$val_stok50 = $data_stok["1060006000|121-301-0056"];
imagestring($im,14,440,115,"KWSG TUBAN",$biru); // kode gundang : 1060006000
imagestring($im,14,440,130,$val_stok40." ZAK",$biru); 
imagestring($im,14,440,145,$val_stok50." ZAK",$biru); 
    
$val_stok40 = $data_stok["1410190000|121-301-0110"];
$val_stok50 = $data_stok["1410190000|121-301-0056"];
imagestring($im,14,570,110,"BPW LAMONGAN",$biru); // kode gundang : 1410190000
imagestring($im,14,570,125,$val_stok40." ZAK",$biru);
imagestring($im,14,570,140,$val_stok40." ZAK",$biru);

$val_stok40 = $data_stok["1410150000|121-301-0110"];
$val_stok50 = $data_stok["1410150000|121-301-0056"];
imagestring($im,14,420,200,"BPW BJNGORO",$biru); // kode gundang : 1410150000
imagestring($im,14,420,215,$val_stok40." ZAK",$biru);
imagestring($im,14,420,230,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000128000|121-301-0110"];
$val_stok50 = $data_stok["1000128000|121-301-0056"];
imagestring($im,14,740,190,"VU MADURA",$biru); // kode gundang : 1000128000
imagestring($im,14,740,205,$val_stok40." ZAK",$biru);
imagestring($im,14,740,220,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1530068000|121-301-0110"];
$val_stok50 = $data_stok["1530068000|121-301-0056"];
imagestring($im,14,570,195,"LANCAR BBS",$biru); // kode gundang : 1530068000
imagestring($im,14,570,210,$val_stok40." ZAK",$biru);
imagestring($im,14,570,225,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000205000|121-301-0110"];
$val_stok50 = $data_stok["1000205000|121-301-0056"];
imagestring($im,14,380,300,"VU BJNGORO",$biru); // kode gundang : 1000205000
imagestring($im,14,380,315,$val_stok40." ZAK",$biru);
imagestring($im,14,380,330,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1440032000|121-301-0110"];
$val_stok50 = $data_stok["1440032000|121-301-0056"];
imagestring($im,14,130,310,"ABADI PW",$biru); // kode gundang : 1440032000
imagestring($im,14,130,325,$val_stok40." ZAK",$biru);
imagestring($im,14,130,340,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1440028000|121-301-0110"];
$val_stok50 = $data_stok["1440028000|121-301-0056"];
imagestring($im,14,220,390,"ABADI PW",$biru); // kode gundang : 1440028000
imagestring($im,14,220,410,$val_stok40." ZAK",$biru);
imagestring($im,14,220,425,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1440014000|121-301-0110"];
$val_stok50 = $data_stok["1440014000|121-301-0056"];
imagestring($im,14,135,510,"ABADI PW",$biru); // kode gundang : 1440014000
imagestring($im,14,135,525,$val_stok40." ZAK",$biru);
imagestring($im,14,135,540,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1440014000|121-301-0110"];
$val_stok50 = $data_stok["1440014000|121-301-0056"];
imagestring($im,14,120,560,"VU PONOROGO",$biru); // kode gundang : 1000014000
imagestring($im,14,120,575,$val_stok40." ZAK",$biru);
imagestring($im,14,120,590,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400105000|121-301-0110"];
$val_stok50 = $data_stok["1400105000|121-301-0056"];
imagestring($im,14,230,600,"PPCP",$biru); // kode gundang : 1400105000
imagestring($im,14,230,615,$val_stok40." ZAK",$biru);
imagestring($im,14,230,630,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040021073|121-301-0110"];
$val_stok50 = $data_stok["1040021073|121-301-0056"];
imagestring($im,14,220,650,"WA TRENGGALEK",$biru); // kode gundang : 1040021073
imagestring($im,14,220,665,$val_stok40." ZAK",$biru);
imagestring($im,14,220,680,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400104000|121-301-0110"];
$val_stok50 = $data_stok["1400104000|121-301-0056"];
imagestring($im,14,380,370,"PPCP",$biru); // kode gundang : 1400104000
imagestring($im,14,380,385,$val_stok40." ZAK",$biru);
imagestring($im,14,380,400,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400095000|121-301-0110"];
$val_stok50 = $data_stok["1400095000|121-301-0056"];
imagestring($im,14,460,475,"PPCP",$biru); // kode gundang : 1400095000
imagestring($im,14,460,490,$val_stok40." ZAK",$biru);
imagestring($im,14,460,505,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040140000|121-301-0110"];
$val_stok50 = $data_stok["1040140000|121-301-0056"];
imagestring($im,14,460,540,"WA KEDIRI",$biru); // kode gundang : 1040140000
imagestring($im,14,460,560,$val_stok40." ZAK",$biru);
imagestring($im,14,460,575,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000203000|121-301-0110"];
$val_stok50 = $data_stok["1000203000|121-301-0056"];
imagestring($im,14,420,590,"VU TULUNGAGUNG",$biru); // kode gundang : 1000203000
imagestring($im,14,420,600,$val_stok40." ZAK",$biru);
imagestring($im,14,420,615,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400096000|121-301-0110"];
$val_stok50 = $data_stok["1400096000|121-301-0056"];
imagestring($im,14,380,630,"PPCP",$biru); // kode gundang : 1400096000
imagestring($im,14,380,650,$val_stok40." ZAK",$biru);
imagestring($im,14,380,665,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040148000|121-301-0110"];
$val_stok50 = $data_stok["1040148000|121-301-0056"];
imagestring($im,14,470,650,"WA BLITAR",$biru); // kode gundang : 1040148000
imagestring($im,14,470,665,$val_stok40." ZAK",$biru);
imagestring($im,14,470,680,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400106000|121-301-0110"];
$val_stok50 = $data_stok["1400106000|121-301-0056"];
imagestring($im,14,500,690,"PPCP",$biru); // kode gundang : 1400106000
imagestring($im,14,500,705,$val_stok40." ZAK",$biru);
imagestring($im,14,500,720,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000050000|121-301-0110"];
$val_stok50 = $data_stok["1000050000|121-301-0056"];
imagestring($im,14,480,430,"VU JOMBNG",$biru); // kode gundang : 1000050000
imagestring($im,14,480,445,$val_stok40." ZAK",$biru);
imagestring($im,14,480,460,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1400094000|121-301-0110"];
$val_stok50 = $data_stok["1400094000|121-301-0056"];
imagestring($im,14,540,380,"PPCP",$biru); // kode gundang : 1400094000
imagestring($im,14,540,395,$val_stok40." ZAK",$biru);
imagestring($im,14,540,405,$val_stok50." ZAK",$biru);

/* Gresik */
imagestring($im,14,800,35,"GRESIK",$biru);
$val_stok40 = $data_stok["1000193000|121-301-0110"];
$val_stok50 = $data_stok["1000193000|121-301-0056"];
imagestring($im,14,800,50,"VU GRESIK 2 : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru);  // kode gundang : 1000193000

$val_stok40 = $data_stok["1060003000|121-301-0110"];
$val_stok50 = $data_stok["1060003000|121-301-0056"];
imagestring($im,14,800,65,"KWSG GRESIK : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru);  // kode gundang : 1060003000

$val_stok40 = $data_stok["1000101000|121-301-0110"];
$val_stok50 = $data_stok["1000101000|121-301-0056"];
imagestring($im,14,800,80,"VU GRESIK 1 : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru);  // kode gundang : 1000101000

/* Surabaya */
imagestring($im,14,800,325,"SURABAYA",$biru);
$val_stok40 = $data_stok["1390077000|121-301-0110"];
$val_stok50 = $data_stok["1390077000|121-301-0056"];
imagestring($im,14,800,340,"SBP Margomulyo  : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //SURI BUANA PERKASA  1390077000

$val_stok40 = $data_stok["1410077000|121-301-0110"];
$val_stok50 = $data_stok["1410077000|121-301-0056"];
imagestring($im,14,800,355,"BPW 1           : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //1410077000

$val_stok40 = $data_stok["1390079000|121-301-0110"];
$val_stok50 = $data_stok["1390079000|121-301-0056"];
imagestring($im,14,800,370,"SBP Kalilom Lor : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //SURI BUANA PERKASA 1390079000

$val_stok40 = $data_stok["1390015000|121-301-0110"];
$val_stok50 = $data_stok["1390015000|121-301-0056"];
imagestring($im,14,800,385,"SBP Pandegiling : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //SURI BUANA PERKASA 1390015000

$val_stok40 = $data_stok["1410068000|121-301-0110"];
$val_stok50 = $data_stok["1410068000|121-301-0056"];
imagestring($im,14,800,400,"BPW 2           : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //1410068000
//imagestring($im,14,800,420,"VU GRESIK Krian : 6 TO",$biru);

$val_stok40 = $data_stok["1470076000|121-301-0110"];
$val_stok50 = $data_stok["1470076000|121-301-0056"];
imagestring($im,14,800,415,"JAWA BERKAT UTAMA Gempol : ".$val_stok40." ZAK, ".$val_stok50." ZAK",$biru); //1470076000

/* Pasuruan - Jatim Kanan bawah --------------------------------------------- */
$val_stok40 = $data_stok["1060035000|121-301-0110"];
$val_stok50 = $data_stok["1060035000|121-301-0056"];
imagestring($im,14,715,475,"KWSG PRWSARI",$biru); // kode gundang : 1060035000
imagestring($im,14,715,490,$val_stok40." ZAK",$biru);
imagestring($im,14,715,505,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040035010|121-301-0110"];
$val_stok50 = $data_stok["1040035010|121-301-0056"];
imagestring($im,14,855,460,"WA PROBOLINGGO",$biru); // kode gundang : 1040035010
imagestring($im,14,855,475,$val_stok40." ZAK",$biru);
imagestring($im,14,855,490,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1530073000|121-301-0110"];
$val_stok50 = $data_stok["1530073000|121-301-0056"];
imagestring($im,14,890,515,"LANCAR BBS",$biru); // kode gundang : 1530073000
imagestring($im,14,890,530,$val_stok40." ZAK",$biru);
imagestring($im,14,890,545,$val_stok50." ZAK",$biru);


$val_stok40 = $data_stok["1470155000|121-301-0110"];
$val_stok50 = $data_stok["1470155000|121-301-0056"];
imagestring($im,14,575,570,"JBU Mlng",$biru);// kode gundang : 1470155000 
imagestring($im,14,575,585,$val_stok40." ZAK",$biru);
imagestring($im,14,575,600,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1470092000|121-301-0110"];
$val_stok50 = $data_stok["1470092000|121-301-0056"];
imagestring($im,14,640,520,"JBU 1",$biru);// kode gundang : 1470092000 
imagestring($im,14,640,535,$val_stok40." ZAK",$biru);
imagestring($im,14,640,550,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000049000|121-301-0110"];
$val_stok50 = $data_stok["1000049000|121-301-0056"];
imagestring($im,14,665,590,"VU KEPANJEN",$biru); // kode gundang : 1000049000
imagestring($im,14,665,605,$val_stok40." ZAK",$biru);
imagestring($im,14,665,620,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1470167000|121-301-0110"];
$val_stok50 = $data_stok["1470167000|121-301-0056"];
imagestring($im,14,670,645,"JBU GndLegi",$biru); // kode gundang : 1470167000 
imagestring($im,14,670,660,$val_stok40." ZAK",$biru);
imagestring($im,14,670,675,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1410182000|121-301-0110"];
$val_stok50 = $data_stok["1410182000|121-301-0056"];
imagestring($im,14,1140,430,"BPW SITUBONDO",$biru); // kode gundang : 1410182000
imagestring($im,14,1140,445,$val_stok40." ZAK",$biru);
imagestring($im,14,1140,460,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040145000|121-301-0110"];
$val_stok50 = $data_stok["1040145000|121-301-0056"];
imagestring($im,14,1100,500,"WA BONDOWOSO",$biru); // kode gundang : 1040145000
imagestring($im,14,1100,515,$val_stok40." ZAK",$biru);
imagestring($im,14,1100,530,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1040147000|121-301-0110"];
$val_stok50 = $data_stok["1040147000|121-301-0056"];
imagestring($im,14,1060,570,"WA JEMBER",$biru); // kode gundang : 1040147000
imagestring($im,14,1060,585,$val_stok40." ZAK",$biru);
imagestring($im,14,1075,600,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1420062000|121-301-0110"];
$val_stok50 = $data_stok["1420062000|121-301-0056"];
imagestring($im,14,1060,650,"MITRA MM",$biru); // kode gundang : 1420062000
imagestring($im,14,1060,665,$val_stok40." ZAK",$biru);
imagestring($im,14,1060,680,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1420063000|121-301-0110"];
$val_stok50 = $data_stok["1420063000|121-301-0056"];
imagestring($im,14,890,610,"MITRA MM",$biru); // kode gundang : 1420063000
imagestring($im,14,890,625,$val_stok40." ZAK",$biru);
imagestring($im,14,890,640,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1000202000|121-301-0110"];
$val_stok50 = $data_stok["1000202000|121-301-0056"];
imagestring($im,14,1280,580,"VU BANYUWANGI",$biru); // kode gundang : 1000202000
imagestring($im,14,1280,600,$val_stok40." ZAK",$biru);
imagestring($im,14,1280,615,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1420075000|121-301-0110"];
$val_stok50 = $data_stok["1420075000|121-301-0056"];
imagestring($im,14,1320,640,"MITRA MM",$biru); // kode gundang : 1420075000
imagestring($im,14,1320,655,$val_stok40." ZAK",$biru);
imagestring($im,14,1320,670,$val_stok50." ZAK",$biru);

$val_stok40 = $data_stok["1060140000|121-301-0110"];
$val_stok50 = $data_stok["1060140000|121-301-0056"];
imagestring($im,14,1260,735,"KWSG BANYUWANGI",$biru); // kode gundang : 1060140000
imagestring($im,14,1260,750,$val_stok40." ZAK",$biru);
imagestring($im,14,1260,765,$val_stok50." ZAK",$biru);

/* Bali */
#imagestring($im,14,1500,675,"SURTICON SINGARAJA",$biru); // kode gundang : 00000
#imagestring($im,14,1500,690,"151 TO",$biru);

#imagestring($im,14,1550,580,"SURTICON BUANA",$biru); // kode gundang : 00000
#imagestring($im,14,1550,595,"161 TO",$biru);


/*
imagestring($im,1,0,400,"Gudang 5",$biru);
imagestring($im,2,100,400,"Gudang 6",$biru);
imagestring($im,3,200,400,"Gudang 7",$biru);
imagestring($im,4,300,400,"Gudang 8",$biru);
*/
// Load the gd font 
/*
$font = imageloadfont('font/skaterdude.gdf');
imagefontwidth($font);
imagestring($im, $font, 0, 0, 'Hello', $biru);
*/

//imagestring($image, $font, $x, $y, $string, $color)
header("Content-Type: image/jpeg");
imagejpeg($im,"",80);
imagedestroy($im);

function getlocationcoords($lat, $lon, $width, $height)
{  
    $x = (($lon + 180) * ($width / 360));
    $y = ((($lat * -1) + 90) * ($height / 180));
    return array("x"=>round($x),"y"=>round($y));
}
// Now we convert the long/lat coordinates into screen coordinates
?>