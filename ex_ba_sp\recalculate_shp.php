<? 
session_start();
include ('../include/ex_fungsi.php');
include ('../include/validasi.php'); 
require_once ('../security_helper.php');
sanitize_global_input();

$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();

$halaman_id=438;
$user_id=$_SESSION['user_id'];
$org=$_SESSION['user_org'];

if($org == '1000'){
    $action = 'recalculate_shp_smbr';
	$display_upload = "inline-block";
}else {
    $action = 'recalculate_shp_sp';
	$display_upload = "none";
}

if($user_org != '5000'){
    $mp_coics=$fungsi->getComin($conn,$org);
}else{
    unset($mp_coics);
}

// echo $mp_coics;

if(count($mp_coics)>0){
    unset($inorg);$orgcounter=0;
    foreach ($mp_coics as $keyOrg => $valorgm){
          $inorg .="'".$keyOrg."',";
          $orgcounter++;
    }
    $orgIn= rtrim($inorg, ',');        
}else{
   $orgIn= $org;
}

if($org=='') {
    ?>
    <SCRIPT LANGUAGE="JavaScript">
    alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
    </SCRIPT>
    <a href="../index.php">Login....</a>
    <?
    exit();
}
/*

 if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}
 

$action_page=$fungsi->security($conn,$user_id,$halaman_id);
*/$page="recalculate_shp.php";

$vendor=$fungsi->ex_find_vendor($conn,$user_id);
$hanya_baca = $fungsi->ex_hanya_baca($vendor);
if (isset($_POST['vendor']))
$vendor = $_POST['vendor'];

$no_invoice = $_POST['no_invoice'];
$no_shipment = $_POST['no_shipment'];
$distributor = $_POST['distributor'];
$tipe_transaksi = $_POST['tipe_transaksi'];

$tanggal_shpawal = $_POST['tanggal_shpawal'];
$tanggal_shpakhir = $_POST['tanggal_shpakhir'];

$warna_plat = $_POST['warna_plat'];
$kapal = $_POST['kapal'];
#$tahun = date("Y");

#if (isset($_POST['tahun'])) $tahun=$_POST['tahun'];
#$bulan = $_POST['bulan'];
$plant = $_POST['plant'];

$status = $_POST['status'];
$status2 = $_POST['status2'];

$org_input = $_POST['org'];
if(!$org_input && $org){
	$org_input = $org;
}

$currentPage="recalculate_shp.php";
$komen="";
if(isset($_POST['cari'])){       

    $sql_plant = '';

	$params_sql     = array();
	$sql_filter = "";
	$sql_plant  = "";
	$sql_org    = "";

	if ($plant != "") {
		$sql_plant .= " AND PLANT = :plant";
		$params_sql[":plant"] = $plant;
	}

	if ($org_input != "") {
		$sql_org .= " AND ORG = :org";
		$params_sql[":org"] = $org_input;
	}


    if($kapal=="" and $plant=="" and $no_shipment=="" and $distributor=="" and $vendor=="" and $tipe_transaksi == "" and $warna_plat == "" and $tanggal_shpawal =="" and $tanggal_shpakhir=="" and $status=="" and $status2 == "" and $no_invoice == ""){
        $sql =  "
         SELECT ROWNUM as r, EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1
           FROM EX_TRANS_HDR
          WHERE INCO        != 'FOT'
            AND DELETE_MARK =  '0'
            AND STATUS IN ('DRAFT','OPEN')
            $sql_plant 
            $sql_org
            AND STATUS2 IN ('DRAFT','OPEN')
			AND TIPE_TRANSAKSI = 'BAG'
          ORDER BY VENDOR, SAL_DISTRIK, NO_SHP_TRN ASC";
	}else {
		$sql = "SELECT ROWNUM as r, EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1 
				FROM EX_TRANS_HDR 
				WHERE INCO != 'FOT' 
				AND TIPE_TRANSAKSI = 'BAG'";

		if ($no_shipment != "") {
			$sql .= " AND NO_SHP_TRN LIKE :no_shipment ";
			$params_sql[':no_shipment'] = "%$no_shipment%";
		}

		if ($distributor != "") {
			$sql .= " AND (NAMA_SOLD_TO LIKE :distributor OR SOLD_TO LIKE :distributor) ";
			$params_sql[':distributor'] = "%$distributor%";
		}

		if ($vendor != "") {
			$sql .= " AND (NAMA_VENDOR LIKE :vendor OR VENDOR LIKE :vendor) ";
			$params_sql[':vendor'] = "%$vendor%";
		}

		if ($tanggal_shpawal != "" || $tanggal_shpakhir != "") {
			if ($tanggal_shpakhir == "") {
				$sql .= " AND TANGGAL_KIRIM = TO_DATE(:tanggal_shpawal, 'DD-MM-YYYY')";
				$params_sql[":tanggal_shpawal"] = $tanggal_shpawal;
			} else {
				$sql .= " AND TANGGAL_KIRIM BETWEEN TO_DATE(:tanggal_shpawal, 'DD-MM-YYYY') AND TO_DATE(:tanggal_shpakhir, 'DD-MM-YYYY')";
				$params_sql[":tanggal_shpawal"] = $tanggal_shpawal;
				$params_sql[":tanggal_shpakhir"] = $tanggal_shpakhir;
			}
		}

		if ($warna_plat != "") {
			$sql .= " AND WARNA_PLAT LIKE :warna_plat ";
			$params_sql[':warna_plat'] = "%$warna_plat%";
		}

		if ($kapal != "") {
			$sql .= " AND NAMA_KAPAL LIKE :kapal ";
			$params_sql[':kapal'] = "%$kapal%";
		}

		if ($status != "") {
			$sql .= " AND STATUS = :status ";
			$params_sql[':status'] = $status;
		} else {
			$sql .= " AND STATUS IN ('DRAFT', 'OPEN') ";
		}

		if ($status2 != "") {
			$sql .= " AND STATUS2 = :status2 ";
			$params_sql[':status2'] = $status2;
		} else {
			$sql .= " AND STATUS2 IN ('DRAFT', 'OPEN') ";
		}

		$sql .= " $sql_plant ";
		$sql .= " $sql_org ";

		$sql .= " ORDER BY VENDOR, SAL_DISTRIK, NO_SHP_TRN ASC";

		if ($no_invoice != "") {
			$sql = "SELECT ROWNUM as r, EX_TRANS_HDR.*, to_char(TANGGAL_KIRIM,'DD-MM-YYYY') as TANGGAL_KIRIM1 
					FROM EX_TRANS_HDR 
					WHERE DELETE_MARK = '0' AND ORG IN ($orgIn) 
					AND NO_INVOICE LIKE :no_invoice 
					ORDER BY VENDOR, SAL_DISTRIK, NO_SHP_TRN ASC";
			$params_sql = array(':no_invoice' => "%$no_invoice%");
		}  
	}
    $_SESSION['sql'] = $sql;
	$_SESSION['params_sql'] = $params_sql;
	 //echo "$sql";
	#exit;
        /*
	$query= oci_parse($conn, $sql);
	oci_execute($query);
        
	while($row=oci_fetch_array($query)){
		$no_shipment_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
		$produk_v[]=$row[NAMA_PRODUK];
		$plant_v[]=$row[PLANT]; 
		$no_pol_v[]=$row[NO_POL];
		$sal_distrik_v[]=$row[SAL_DISTRIK]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$nama_ship_to_v[]=$row[NAMA_SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$tarif_cost_v[]=$row[TARIF_COST];  
		$shp_cost_v[]=$row[SHP_COST];  
		$vendor_v[]=$row[VENDOR];  
		$nama_vendor_v[]=$row[NAMA_VENDOR];  
		$status_v[]=$row[STATUS];  
		$status2_v[]=$row[STATUS2];  
	}
	$total=count($no_shipment_v);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";         
         */
        
}

#Pagging fungsi ----------------------------------------------------------------
//Menghitung Total Halaman
function total_pages($total_rows, $rows_per_page) {
    if ( $total_rows < 1 ) $total_rows = 1;
    return ceil($total_rows/$rows_per_page);
}
//Mendapatkan Baris Data Awal
function page_to_row($current_page, $rows_per_page) {
    $start_row = ($current_page-1) * $rows_per_page + 1;
    return $start_row;
}
//Menghitung Total Baris Data
function count_rows($sql, $conn, $params_sql) {    
    $sqlx = explode("WHERE", $_SESSION['sql']);    
    $sqln = "select count(ID) as NUM_ROWS from EX_TRANS_HDR where ".$sqlx[1];
    $stid = @oci_parse($conn, $sqln);
	// var_dump($params_sql);
	if($params_sql && is_array($params_sql)){
		foreach ($params_sql as $key => $val) {
        	@oci_bind_by_name($stid, $key, $params_sql[$key]);
    	}
	}
    @oci_define_by_name($stid, 'NUM_ROWS', $num_rows);
    @oci_execute($stid);
    @oci_fetch($stid);
    return $num_rows;
}
//Mencetak Navigasi Paging
function draw_pager($url, $total_pages, $current_page) {

    if ( $current_page <= 0 || $current_page > $total_pages ) {
        $current_page = 1;
    }

    if ( $current_page > 1 ) {
        printf( "<a href='$url?page=%d'>[ Awal ]</a> \n" , 1);
        printf( "<a href='$url?page=%d'>[ Sebelumnya ]</a> \n" , ($current_page-1));
    }

    for( $i = ($current_page-3); $i <= $current_page+3; $i++ ) {

        if ($i < 1) continue;
        if ( $i > $total_pages ) break;

        if ( $i != $current_page ) {
            printf( "<a href='$url?page=%1\$d' style=\"color:#0000CC\">%1\$d</a> \n" , $i);
        } else {
            printf("<a href='$url?page=%1\$d' style=\"color: #FF0000\"><strong>%1\$d</strong></a> \n",$i);
        }

    }

    if ( $current_page < $total_pages ) {
        printf( "<a href='$url?page=%d'>[ Selanjutnya ]</a> \n" , ($current_page+1));
        printf( "<a href='$url?page=%d'>[ Akhir ]</a> \n" , $total_pages);
    }
}

function get_sql_pagging($sqls, $nawal, $nakhir){
    $xsql = explode("ORDER BY",$sqls);
    $_SESSION['sqlawal'] = $xsql[0];
    $_SESSION['sqlakhir'] = $xsql[1];
    
    $sql = "SELECT * FROM ( ".$_SESSION['sqlawal']." ORDER BY".$_SESSION['sqlakhir'].") a WHERE a.r between ".$nawal." and ".$nakhir;
    return $sql;
}

#Paging data---        
        $current_page = 1;
        if(isset($_GET['page'])){
            $current_page = $_GET['page'];
        }
		if(!$params_sql){
			$params_sql = $_SESSION['params_sql'];
		}        
        $total_rows = count_rows($sql, $conn, $params_sql);
        $rows_per_page = 100; //change by iljas - 11 Juli 2012
        $total_pages = total_pages($total_rows, $rows_per_page);
        $start_row = page_to_row($current_page,$rows_per_page);
        //draw_pager("", $total_pages, $current_page);
        $end_row = $start_row + $rows_per_page - 1;
        
        $sqlcari = get_sql_pagging($_SESSION['sql'],$start_row,$end_row);
		// var_dump($params_sql);
		$query= @oci_parse($conn, $sqlcari);
		if($params_sql && is_array($params_sql)){
			foreach ($params_sql as $key => $val) {
				@oci_bind_by_name($query, $key, $params_sql[$key]);
			}
		}
		// echo $sqlcari;
		@oci_execute($query);
        while($row=@oci_fetch_array($query)){
                $r_v[]=$row[R];
		$no_shipment_v[]=$row[NO_SHP_TRN];
		$tgl_kirim_v[]=$row[TANGGAL_KIRIM];
		$produk_v[]=$row[NAMA_PRODUK];
		$plant_v[]=$row[PLANT]; 
		$no_pol_v[]=$row[NO_POL];
		$sal_distrik_v[]=$row[SAL_DISTRIK]; 
		$sold_to_v[]=$row[SOLD_TO];
		$nama_sold_to_v[]=$row[NAMA_SOLD_TO];
		$ship_to_v[]=$row[SHIP_TO];
		$nama_ship_to_v[]=$row[NAMA_SHIP_TO];
		$qty_v[]=$row[QTY_SHP];
		$qty_kantong_rusak_v[]=$row[QTY_KTG_RUSAK];
		$qty_semen_rusak_v[]=$row[QTY_SEMEN_RUSAK];
		$id_v[]=$row[ID];  
		$tarif_cost_v[]=$row[TARIF_COST];  
		$shp_cost_v[]=$row[SHP_COST];  
		$vendor_v[]=$row[VENDOR];  
		$nama_vendor_v[]=$row[NAMA_VENDOR];  
		$status_v[]=$row[STATUS];  
		$status2_v[]=$row[STATUS2];  
	}
	$total=count($no_shipment_v);
	if ($total < 1 && isset($_POST['cari'])) $komen = "Tidak Ada Data Yang Ditemukan";         
       
#-------------------------------------------------------------------------------
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Input Cost Claim :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module --> 
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<script language="JavaScript" type="text/javascript" src="../include/scrollabletable.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" /> 

<script type="text/javascript">

checked=false;
function checkedAll (frm1) {
	var aa= document.getElementById('fsimpan');
	 if (checked == false)
          {
           checked = true
		   markAllRows('fsimpan');
          }
        else
          {
          checked = false
		  unMarkAllRows('fsimpan')
          }
/*	for (var i =0; i < aa.elements.length; i++) 
	{
	 aa.elements[i].checked = checked;

	}
*/ }

function markAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != true){
				checkbox.checked = true;
				rows[i].className += ' selected';
			}
        }
    }

    return true;
}

function unMarkAllRows( container_id ) {
    var rows = document.getElementById(container_id).getElementsByTagName('tr');
    var checkbox;

    for ( var i = 0; i < rows.length; i++ ) {

        checkbox = rows[i].getElementsByTagName( 'input' )[0];

        if ( checkbox && checkbox.type == 'checkbox' ) {
			if (checkbox.checked != false){
			checkbox.checked = false;
            rows[i].className = rows[i].className.replace(' selected', '');
			}
        }
    }

    return true;
}

</script> 

<style type="text/css">
body	{background:#fff;}
table	{border:0;border-collapse:collapse;}
td		{padding:4px;}
tr.odd1	{background:#F9F9F9;}
tr.odd0	{background:#FFFFFF;}
tr.highlight	{background:#BDA9A2;}
tr.selected		{background:orange;color:#fff;}
</style>

<script type="text/javascript">

function IsNumeric(obj,panjang)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789";
   var strChar;
   var strString = obj.value;
   if (strString.length != panjang){
     alert("Isi dengan Angka " + panjang + " digit..");
	 obj.value="";
	 return false;
	} else {
	   //  test strString consists of valid characters listed above
	   for (i = 0; i < strString.length; i++)
		  {
		  strChar = strString.charAt(i);
		  if (strValidChars.indexOf(strChar) == -1)
			 {
			 alert("Hanya Masukkan Angka 0-9 dengan " + panjang + " digit..");
			 obj.value="";
			 return false;
			 }
		  }
	 } 
   }


function addLoadEvent(func) {
  var oldonload = window.onload;
  if (typeof window.onload != 'function') {
    window.onload = func;
  } else {
    window.onload = function() {
      oldonload();
      func();
    }
  }
}

function addClass(element,value) {
  if (!element.className) {
    element.className = value;
  } else {
    newClassName = element.className;
    newClassName+= " ";
    newClassName+= value;
    element.className = newClassName;
  }
}


function stripeTables() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var i=0; i<tbodies.length; i++) {
				var odd = true;
				var rows = tbodies[i].getElementsByTagName("tr");
				for (var j=0; j<rows.length; j++) {
					if (odd == false) {
						odd = true;
						addClass(rows[j],"odd1");
					} else {
						addClass(rows[j],"odd0");
						odd = false;
					}
				}
			}
		}
	}
}
function highlightRows() {
  if(!document.getElementsByTagName) return false;
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			  var tbodies = tables[m].getElementsByTagName("tbody");
			  for (var j=0; j<tbodies.length; j++) {
				 var rows = tbodies[j].getElementsByTagName("tr");
				 for (var i=0; i<rows.length; i++) {
					   rows[i].oldClassName = rows[i].className
					   rows[i].onmouseover = function() {
						  if( this.className.indexOf("selected") == -1)
							 addClass(this," highlight");
					   }
					   rows[i].onmouseout = function() {
						  if( this.className.indexOf("selected") == -1)
							 this.className = this.oldClassName
					   }
				 }
			  }
		}
	}
}

function selectRowCheckbox(row) {
	var checkbox = row.getElementsByTagName("input")[0];
	if (checkbox.checked == true) {
		checkbox.checked = false;
	} else
	if (checkbox.checked == false) {
		checkbox.checked = true;
	}
}

function lockRow() {
  	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
				var tbodies = tables[m].getElementsByTagName("tbody");
				for (var j=0; j<tbodies.length; j++) {
					var rows = tbodies[j].getElementsByTagName("tr");
					for (var i=0; i<rows.length; i++) {
						rows[i].oldClassName = rows[i].className;
						rows[i].onclick = function() {
							if (this.className.indexOf("selected") != -1) {
								this.className = this.oldClassName;
							} else {
								addClass(this," selected");
							}
							selectRowCheckbox(this);
						}
					}
				}
		}
	}
}

addLoadEvent(stripeTables);
addLoadEvent(highlightRows);
addLoadEvent(lockRow);


function lockRowUsingCheckbox() {
	var tables = document.getElementsByTagName("table");
	for (var m=0; m<tables.length; m++) {
		if (tables[m].className == "pickme") {
			var tbodies = tables[m].getElementsByTagName("tbody");
			for (var j=0; j<tbodies.length; j++) {
				var checkboxes = tbodies[j].getElementsByTagName("input");
				for (var i=0; i<checkboxes.length; i++) {
					checkboxes[i].onclick = function(evt) {
						if (this.parentNode.parentNode.className.indexOf("selected") != -1){
							this.parentNode.parentNode.className = this.parentNode.parentNode.oldClassName;
						} else {
							addClass(this.parentNode.parentNode," selected");
						}
						if (window.event && !window.event.cancelBubble) {
							window.event.cancelBubble = "true";
						} else {
							evt.stopPropagation();
						}
					}
				}
			}
		}
	}
}
addLoadEvent(lockRowUsingCheckbox);

function findplant(org) {	
		var com_org = document.getElementById('org');
		var strURL="cari_plant.php?org="+com_org.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function cekTgl(){
    var a = document.getElementById("Tanggal Shp Awal").value;
    var b = document.getElementById("Tanggal Shp Akhir").value;
    var explod = a.split('-');
    var explod2 = b.split('-');
    var tgl = new Date();
    var tgl_a = tgl.setFullYear(explod[2],explod[1],explod[0]);
    var tgl_b = tgl.setFullYear(explod2[2],explod2[1],explod2[0]);
    var milisecond = 60*60*24*1000;
    var c = ((tgl_b-tgl_a)/ milisecond);
        if(c>7){
            alert('Maaf, tanggal maksimal 7 hari :)');
            return false;
        }
} 

</script>

</head>

<body>
<script type="text/javascript" language="JavaScript">
	//ini ni yang buat div tapi kita hidden... ocre....
	document.write('<div id="tunggu_ya" style="display:none" ><table width="100%" height="95%" align="center" valign="middle"><tr><td width="100%" height="100%" align="center" valign="middle"><h3>Loading Data....<br><br><div align="center"><img src="../images/loading.gif"></img></div></h3></td></tr></table></div>');
	
	</script>
<div id="halaman_tampil" style="display:inline">

<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Recalculate Shipment Cost </th>
</tr></table></div>
<?
	if(!isset($_POST['cari']) and !isset($_GET['page'])){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Shipment Cost </th>
</tr>
</table>
</div>

<form id="form1" name="form1" method="post" action="<?=$page?>" >
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr width="174">
      <td class="puso">No Invoice</td>
      <td class="puso">:</td>
      <td><input type="text" id="no_invoice" name="no_invoice" value="<?=$no_invoice?>"/></td>
    </tr>
    <tr width="174">
      <td class="puso">No SPJ </td>
      <td class="puso">:</td>
      <td><input type="text" id="no_shipment" name="no_shipment" value="<?=$no_shipment?>"/></td>
    </tr>
    <tr>
      <td  class="puso">Distributor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="distributor" name="distributor"  value="<?=$distributor?>" /></td>
    </tr>
    <tr>
      <td  class="puso">Vendor</td>
      <td  class="puso">:</td>
      <td ><input type="text" id="vendor" name="vendor"  value="<?=$vendor?>" <? echo $hanya_baca; ?>/></td>
    </tr>
    <tr>
      <td  class="puso">Nama Kapal </td>
      <td  class="puso">:</td>
      <td ><input type="text" id="kapal" name="kapal"  value="<?=$kapal?>" <? echo $hanya_baca; ?>/></td>
    </tr>
   <!--  <tr>
      <td  class="puso">Periode Shipment </td>
      <td  class="puso">:</td>
      <td ><select name="bulan" id="bulan">
        <option value="">---Pilih---</option>
        <? $fungsi->ex_bulan($bulan);?>
      </select> <input type="text" id="tahun" name="tahun"  value="<?=$tahun?>" maxlength="4" size="10"/></td>
    </tr> -->
	<tr>
      <td  class="puso">Periode Shipment</td>
      <td  class="puso">:</td>
      <td>
			<input name="tanggal_shpawal" type="text" id="Tanggal Shp Awal" required value="<?=$tanggal_shpawal?>"/>
			<input name="btn_mulai" type="button" class="button" onClick="return showCalendar('Tanggal Shp Awal');" value="..." />
			&nbsp;&nbsp;s/d&nbsp;&nbsp;
			<input name="tanggal_shpakhir" type="text" id="Tanggal Shp Akhir" required value="<?=$tanggal_shpakhir?>"/>
			<input name="btn_selesai" type="button" class="button" onClick="return showCalendar('Tanggal Shp Akhir');" value="..." />
			<span style="color:red;font-size: 15px;"> *</span> 
	  </td>
    </tr>
	<tr>
      <td  class="puso">Org</td>
      <td  class="puso">:</td>
      <td >
		  <select name="org" id="status">
            <?php foreach($mp_coics as $org): ?>
                <option value="<?= $org ?>"><?= $org ?></option>
            <?php  endforeach; ?>
		  </select>
		  <span style="color:red;font-size: 15px;"> *</span> 
	  </td>
    </tr>
    <tr>
      <td  class="puso">Plant</td>
      <td  class="puso">:</td>
      <td >
		  <input name="plant" id="plant" type="text" size="5">
	  </td>
    </tr>
	<tr>
      <td  class="puso">Status</td>
      <td  class="puso">:</td>
      <td >
		  <select name="status" id="status">
			<option value="">---All---</option>
			<? $fungsi->ex_status2($status);?>
		  </select>
	  </td>
    </tr>
	<tr>
      <td  class="puso">Status 2</td>
      <td  class="puso">:</td>
      <td >
		  <select name="status2" id="status2">
			<option value="">---All---</option>
			<? $fungsi->ex_status2($status2);?>
		  </select>
	  </td>
    </tr>
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" class="button" id="cari" value="Find" onclick="return cekTgl();" />    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>

  
</form>
<? } ?>
<br />
<br />
<?
	if( ($total> 0 and isset($_POST['cari'])) or isset($_GET['page']) ){

?>
<form id="fsimpan" name="fsimpan" method="post" action="komentar.php">
	<div align="center">
            <? draw_pager("", $total_pages, $current_page); ?>
	<table width="95%" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Data Shipment Cost :. </span><? echo ' Record ke-'.$start_row.' s/d '.$end_row.' dari '.$total_rows.' Records';?></th>
	</tr>o
	</table>
	</div> 
	<div align="center">
	<table  width="95%" align="center" class="adminlist">
	<thead >
	  <tr class="quote">
		<td ><strong><input type="button" class="button" onClick="checkedAll('fsimpan');" value="CEK ALL"></strong></td>
		<td align="center"><strong >TGL. SPJ </strong></td>
		<td align="center"><strong >NO. SPJ </strong></td>
		 <td align="center"><strong>NO. POL </strong></td>
		 <td align="center"><strong>Nilai</strong></td>
		 <td align="center"><strong>DISTRIBUTOR</strong></td>
		 <td align="center"><strong>SHIPTO</strong></td>
		 <td align="center"><strong>EXPEDITUR</strong></td>
		 <td align="center"><strong>KWANTUM</strong></td>
		 <td align="center"><strong>STATUS</strong></td>
		 <td align="center"><strong>STATUS2</strong></td>
      </tr >
	  </thead>
	  <tbody >
  <?
  if(isset($_GET['page']))
    $page = ($_GET['page']*$rows_per_page)-$rows_per_page;
  else
    $page = 0;  
  $b=$page+1;
  for($i=0; $i<$total;$i++) {		
		$idke="id_app".$i;
		$appke=$no_shipment_v[$i];

		?>  
		<tr>
		<td align="center"><input name="<?=$idke;?>" type="checkbox" value="<?=$appke;?>" onclick="toggleCheckbox(this)" /> <? echo $b;?></td>
		<td align="center"><? echo $tgl_kirim_v[$i]; ?></td>
		<td align="center"><? echo $no_shipment_v[$i]; ?></td>
		<td align="center"><? echo $no_pol_v[$i]; ?></td>
		<td align="center"><? echo number_format($shp_cost_v[$i],0,",","."); ?></td>
		<td align="center"><? echo $sold_to_v[$i]." / ".$nama_sold_to_v[$i]; ?></td>
		<td align="center"><? echo $ship_to_v[$i]." / ".$nama_ship_to_v[$i]; ?></td>
		<td align="center"><? echo $vendor_v[$i]." / ".$nama_vendor_v[$i]; ?></td>
		<td align="center"><? echo number_format($qty_v[$i],0,",","."); ?></td>
		<td align="center"><? echo $status_v[$i]; ?></td>
		<td align="center"><? echo $status2_v[$i]; ?></td>
		</tr>
	  <? $b++; } ?>
	  </tbody>
	  <tfoot>
	  <tr class="quote">
		<td colspan="12" align="center">
		<input name="simpan" type="submit" class="button" id="simpan" value="Save" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<a href="recalculate_shp.php" target="isi" class="button">Cancel</a>
		<input name="btn-upload" type="button" class="button" id="btn-upload" value="Upload" style="margin-left: 15px;display:<?= $display_upload ?>;" onClick="upload()">
		<input name="total" type="hidden" value="<?=$total;?>" />
		<input name="action" type="hidden" value="<?=$action;?>" />		 </td>
	    </tr>
	  </tfoot>
	</table>
	</div>
	<?
	}?>
<div align="center">
<?
echo $komen;

?></div>
		</form>

<form id="recalcForm" action="recalculate_upload.php" method="post" target="_blank">
  <input type="hidden" name="ids" id="idsInput">
</form>

<p>&nbsp;</p>

<? if ($total> 11){ ?>
<script type="text/javascript">
var t = new ScrollableTable(document.getElementById('myScrollTable'), 300);
</script>
<? } ?>
</p>
</div>
<? include ('../include/ekor.php'); ?>
	<script language=javascript>
	//We write the table and the div to hide the content out, so older browsers won't see it
		obj=document.getElementById("tunggu_ya");
		obj.style.display = "none";
		obj_tampil=document.getElementById("halaman_tampil");
		obj_tampil.style.display = "inline";
		let selectedShipments = [];

		function upload() {
			const idsString = selectedShipments.join(',');
			document.getElementById('idsInput').value = idsString;
			const popup = window.open('', 'popupWindow', 'width=800,height=600,scrollbars=yes');
			const form = document.getElementById('recalcForm');
			form.target = 'popupWindow';
			form.submit();
		}

		function toggleCheckbox(checkbox) {
			const value = checkbox.value;

			if (checkbox.checked) {
				// Tambahkan jika belum ada
				if (!selectedShipments.includes(value)) {
					selectedShipments.push(value);
				}
			} else {
				// Hapus jika tidak dicentang
				const index = selectedShipments.indexOf(value);
				if (index !== -1) {
					selectedShipments.splice(index, 1);
				}	
			}

			console.log("Selected:", selectedShipments); // Debug log
		}
	</script>
</body>
</html>
