<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$page="evaluasi_app.php";
$NO_EV = $_POST['nomor'];
$NO_KONT = $_POST['kontrak'];
$KD_DIS = $_POST['KD_DISTR'];



if(isset($_POST['cari'])){

		$sql= "SELECT * FROM EVALUASI WHERE ID='$ID' OR NO_KONTRAK='".$KD_KONT."' OR KD_DISTR='".$KD_DIS."'" ;
		$query= oci_parse($conn, $sql);
	    oci_execute($query);
	while($row=oci_fetch_array($query)){
		$ID=$row['ID'];
		$KD_DIST[]=$row['KD_DISTR'];
		$KODE_DIST = intval($row['KD_DISTR']);
		$ALAMAT[]=$row['ALAMAT'];
        $NO_SU[]=$row['NO_SURAT'];
        $TGL_SURAT[]=$row['TGL_SURAT'];
		$HAL[]=$row['HAL'];
		$JUDUL[]=$row['JUDUL'];
		$NO_KONTRAK[]=$row['NO_KONTRAK'];
		$TGL_KONTRAK[]=$row['TGL_KONTRAK'];
		$NO_KONTRAK_KEY=$row['NO_KONTRAK'];
		$HAL_HARGA[]=$row['HAL_HARGA'];
        $NM_KONTRAKTOR[]=$row['NM_KONTRAKTOR'];
        $NO_KONTRAKT[]=$row['NO_KONTRAKT'];
		$TGL_KONTRAKT[]=$row['TGL_KONTRAKT'];
		$HAL_KONTRAKT[]=$row['HAL_KONTRAKT'];
		$JNS_SEMEN[]=$row['JNS_SEMEN'];
		$PELAKSANA[]=$row['PELAKSANA_EVALUASI'];
		$TGL_EVA[]=$row['TGL_EVALUASI'];
        $NM_EVA[]=$row['NAMA_EVALUATOR'];
        $HARGA_JUAL[]=$row['HARGA_JUAL'];
	    $STATUS_EVA[]=$row['STATUS_EV'];
		$APP_KADEP[]=$row['APP_KADEP'];
		$NM_PROYEK[]=$row['NM_PROYEK'];

		$sql2= "SELECT * FROM PT_MASTER_DISTRIBUTOR WHERE KODE_DISTRIBUTOR_SAP='".$KODE_DIST."'";
		$query2= @oci_parse($conn, $sql2);
	    @oci_execute($query2);
		while($row=@oci_fetch_array($query2)){
		$NAMA_DISTRIBUTOR_SAP[]=$row['NAMA_DISTRIBUTOR_SAP'];
		}

		$sql3= "SELECT * FROM HARGA_TEBUS WHERE NO_KONTRAK='".$NO_KONTRAK_KEY."'";
		$query3= @oci_parse($conn, $sql3);
	    @oci_execute($query3);
		while($row=@oci_fetch_array($query3)){
			$SHIPTO[]=$row['SHIP_TO'];
			$KOTA[]=$row['KD_DISTRIK'];
			$AFTUBAN[]=$row['H_TEBUS_AFT'];
			$AFGRESIK[]=$row['H_TEBUS_AFG'];
			$FRC[]=$row['H_TEBUS_FRC'];
			$TGL[]=$row['TANGGAL'];
	}
	}
	$total=count($SHIPTO);
	if ($total < 1)$komen = "Tidak Ada Data Yang Ditemukan";

	
}

echo "$www =$ID ";
 
if(isset($_POST['simpan'])){
         
		 $tttt = $_POST['nomor'];
		 $status_ganti =1;
				
			echo $sql ="UPDATE EVALUASI SET STATUS_EV=$status_ganti where ID='".$ID."' ";
			$query= oci_parse($conn, $sql);
			$q_status = oci_execute($query);			
			if($q_status) $pesan = 'Status berhasil diubah';
			else $pesan = 'Status gagal diubah';
		}	

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script language=javascript>
function finddistr(org) {
		var com_org = document.getElementById('org');		
		var strURL="cari_distr.php?org="+com_org.value;
		popUp(strURL);
		}

</script> 
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="500" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Evaluasi</th>
</tr></table></div>
<?
	if($total<1){

		if($pesan!='') {
			echo "<table width='400'>
				<tr bgcolor='#BAF3B1'>
					<th align='left'><img src='../images/ceklis.png' height='12'><font color='#008000' size='1'>&nbsp;".$pesan."</font></th>
				</tr>
			</table>";
		}
?>

<div align="center">
<table width="500" align="center" class="adminlist">
<tr>
<th align="left" colspan="4">Daftar Evaluasi</th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
  <table width="500" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
	<tr width="174">
      <td class="puso">No.Evaluasi</td>
      <td class="puso">:</td>
      <td><input type="text" id="nomor" name="nomor" value=""/>
	</tr>
	<tr width="174">
      <td class="puso">No.Kontrak</td>
      <td class="puso">:</td>
      <td><input type="text" id="kontrak" name="kontrak" value=""/>
	</tr>
	<tr width="174">
      <td class="puso">Kode Distributor</td>
      <td class="puso">:</td>
      <td>
		  <input name="org" type="hidden" id="org" value="<?=$user_org?>"/><div id="distrdiv">
		  <input name="KD_DISTR" id="KD_DISTR" type="text" size="10" maxlength="10" value="" onChange="ketik_distr(this)"/>
		  <input name="nama_sold_to" id="nama_sold_to" type="text" size="30" value="" readonly="true"/>	    
		  <input name="btn_distr" type="button" class="button" id="btn_distr" value="..." onClick="finddistr()"/></div>
	  </tr>
     <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){


?>
	<div align="center">
	<table width="900" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">Daftar Evaluasi</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" >
	<table width="900" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>No.</strong></td>
		<td align="center"><strong>No.Evaluasi</strong></td>
		<td align="center"><strong>Kode Distributor</strong></td>
		<td align="center"><strong>No.Surat </strong></td>
		<td align="center"><strong>Header</strong></td>
		<td align="center"><strong>No.Kontrak</strong></td>
		<td align="center"><strong>Tanggal</strong></td>
		<td align="center"><strong>Proyek</strong></td>
		<td align="center"><strong>Kontraktor</strong></td>
		<td align="center"><strong>Harga Jual Distibutor</strong></td>
		<td align="center"><strong>AFTuban</strong></td>
		<td align="center"><strong>AFGresik</strong></td>
		<td align="center"><strong>Franco</strong></td>
		<td align="center"><strong>Status</strong></td>
		<td align="center"><strong>Approve</strong></td>
	  </tr >
  <?  

   		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}
		
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $ID[$i]; ?></td>
		<td align="center"><? echo $KD_DIST[$i]; ?></td>		
		<td align="center"><? echo $NO_SU[$i]; ?></td>
		<td align="center"><? echo $JUDUL[$i]; ?></td>
		<td align="center"><? echo $NO_KONTRAK[$i]; ?></td>
		<td align="right"><? echo $TGL_KONTRAK[$i]; ?></td>
		<td align="center"><? echo $NM_PROYEK[$i]; ?></td>
		<td align="right"><? echo $NM_KONTRAKTOR[$i]; ?></td>
		<td align="right"><? echo $HARGA_JUAL[$i]; ?></td>
		<td align="right"><? echo $AFTUBAN[$i]; ?></td>
		<td align="right"><? echo $AFGRESIK[$i]; ?></td>
		<td align="right"><? echo $FRC[$i]; ?></td>
		<td align="center"><? echo $STATUS_EVA[$i]; ?></td>
		<td align="center"><input type='checkbox' name='verifikasi'  value=''/><img src='../images/ceklis.png' height='12'></td>
					
					
		
		</td>
		</tr>
	  <? } ?>

    </tr> 
	</table>	
	<table>
	<tr align="center">
      <td colspan="4"><div align="center">
        <br><br><br>
		<input name="simpan" type="submit" class="button" id="simpan" value="Verifikasi" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 		 <input name="action" type="hidden" value="verifikasi" />
		<input class="button" type=button value='Cancel' onClick="self.history.back();">
      </div></td>
	</table>
	 <? } ?>
</form>
</body>
</html>
