<?
ob_start();
session_start();

	include_once('include/setting.php');
	include("sapfrc/sapclasses/sap.php");
    $sap = new SAPConnection();
    $sap->Connect("include/logon_data.conf");


    if ($sap->GetStatus() == SAPRFC_OK ) //$sap->Open ();
       $sap->Open ();
    if ($sap->GetStatus() != SAPRFC_OK ) {
       $sap->PrintStatus();
       exit;
    }
	
	//KONFIRMASI LOGIN UNTUK USER
	
	$fcex = &$sap->NewFunction ("Z_ZCPH_RFC_TPLNREXIT");
    if ($fcex == false ) {
       $sap->PrintStatus();
       exit;
    }else{
		$fcex->I_INPUT=$_POST['funcloc'];
		 $fcex->Call();
		if ($fcex->GetStatus() == SAPRFC_OK) {
			$out = $fcex->I_OUTPUT;
			#echo $out;
			$fce = &$sap->NewFunction ("Z_ZCPH_RFC_NOTIFCREATE");
			if ($fce == false ) {
			   $sap->PrintStatus();
			   exit;
			}
			
			//IMPORT PARAMETER
			
				// IMPORT I_NUMBER
				#$import=NOTIF_TYPE($_POST['tpy']);
				$fce->I_NOTIF_TYPE=$_POST['tpy'];
				
				// IMPORT I_LOGINNAME
				$fce->I_LOGINNAME=$_SESSION['username'];
						
				$tgl				= I_Format_Tanggal($_POST['tgl']);
				$jam				= I_Format_Waktu($_POST['jam']);
				$start_date			= I_Format_Tanggal($_POST['start_date']);
				$start_jam			= I_Format_Waktu($_POST['start_jam']);
				$end_date			= I_Format_Tanggal($_POST['end_date']);
				$end_jam			= I_Format_Waktu($_POST['end_jam']);
				$mal_start			= I_Format_Tanggal($_POST['mal_start']);
				$malT_start			= I_Format_Waktu($_POST['malT_start']);
				$mal_end			= I_Format_Tanggal($_POST['mal_end']);
				$malT_end			= I_Format_Waktu( $_POST['malT_end']);
				$tgl_diminta		= I_Format_Tanggal($_POST['tgl_diminta']);
				$jam_diminta		= I_Format_Waktu( $_POST['jam_diminta']);
				$tgl_datang			= I_Format_Tanggal($_POST['tgl_datang']);
				// IMPORT I_NOTIFHEADER
				$fce->I_NOTIFHEADER=array (
				"REFOBJECTTYPE"=>"",
				"REFOBJECTKEY"=>"",
				"REFRELTYPE"=>"",
				"EQUIPMENT"=>$_POST['equip'],
				"FUNCT_LOC"=>$out,
				"ASSEMBLY"=>"",
				"SERIALNO"=>"",
				"MATERIAL"=>"",
				"DIVISION"=>"",
				"SALES_ORG"=>"",
				"DISTR_CHAN"=>"",
				"SALES_OFFICE"=>"",
				"SALES_GRP"=>"",
				"SHORT_TEXT"=>$_POST['notif_text'],
				"PRIORITY"=>$_POST['prior'],
				"DESSTDATE"=>$start_date,
				"DESSTTIME"=>$start_jam,
				"DESENDDATE"=>$end_date,
				"DESENDTM"=>$end_jam,
				"DEVICEDATA"=>"",
				"PM_WKCTR"=>"",
				"PURCH_NO_C"=>"",
				"PURCH_DATE"=>"",
				"PLANPLANT"=>$_POST['plan2'],
				"PLANGROUP"=>$_POST['plan1'],
				"BREAKDOWN"=>$_POST['break1x'],
				"STRMLFNDATE"=>$mal_start,
				"STRMLFNTIME"=>$malT_start,
				"REPORTEDBY"=>$_POST['rbay'],
				"NOTIF_DATE"=>$tgl,
				"NOTIFTIME"=>$jam,
				"CODE_GROUP"=>"",
				"CODING"=>"",
				"DOC_NUMBER"=>"",
				"ITM_NUMBER"=>"",
				"ENDMLFNDATE"=>$mal_end,
				"ENDMLFNTIME"=>$malT_end,
				"SCENARIO"=>"",
				"ASSEMBLY_EXTERNAL"=>"",
				"ASSEMBLY_GUID"=>"",
				"ASSEMBLY_VERSION"=>"",
				"MATERIAL_EXTERNAL"=>"",
				"MATERIAL_GUID"=>"",
				"MATERIAL_VERSION"=>"",
				"MAINTLOC"=>"",
				"MAINTROOM"=>"");

				$fceXX = &$sap->NewFunction ("Z_ZCPH_RFC_WBSORDERCONV");
				if ($fceXX == false ) {
				   $sap->PrintStatus();
				   exit;
				}
				
				$fceXX->INPUT=$_POST['wbs_element'];
				$fceXX->FLAG="I";

				$fceXX->Call();

				if ($fceXX->GetStatus() == SAPRFC_OK) {
					$WBS_ELEMENT=$fceXX->OUTPUT;
				}

				$fce->I_BDCCHG=array (
					"GEWRK"=>$_POST['mwc1'],
					"SWER0"=>$_POST['mwc2'],
					"SWERK"=>$_POST['main_plan'],
					"MSAUS"=>$_POST['break2x'],
					"BEBER"=>$_POST['plan_section'],
					"ARBPL"=>$_POST['workCenter'],
					"ABCKZ"=>$_POST['abc_indicator'],
					"EQFNR"=>$_POST['sort_field'],
					"BUKRS"=>$_POST['comp_code'],
					"ANLNR"=>$_POST['asset'],
					"ANLUN"=>$_POST['asset2'],
					"KOSTL"=>$_POST['cost_center'],
					"PROID"=>$WBS_ELEMENT,
					"AUFNR"=>$_POST['settlementorder']);

				//echo $_POST['notif_status2'];
				//$fce->I_USR_STATUS=array ("STATUS_EXT"=>$_POST['notif_status2']);
				
				echo $_POST['notif_status2'];
				$usrStatus = explode($_POST['notif_status2'],' ');
				$i=0;
				foreach($usrStatus as $userStatus){
					echo $userStatus[$i];
					echo "<br>";
					$i++;
				}
				exit();

			// IMPORT T_LONGTEXTS
			$fce->T_LONGTEXTS->append (array("TEXT_LINE"=>$_POST['TEXT_LINE']));

			// IMPORT I_VEHICLEDETAIL
			$fce->I_VEHICLEDETAIL=array (
				"ZZPEMAKAI"=>$_POST['nm_pemakai'],
				"ZZTELP"=>$_POST['tlp'],
				"ZZJEMPUT"=>$_POST['tmp_jemput'],
				"ZZPENUMPANG"=>$_POST['jml_p'],
				"ZZTGLDIMINTA"=>I_Format_Tanggal($_POST['tgl_diminta']),
				"ZZJAMDIMINTA"=>I_Format_Waktu($_POST['jam_diminta']),
				"ZZTGLDATANG"=>I_Format_Tanggal($_POST['tgl_datang']),
				"ZZTUJUAN1"=>$_POST['tujuan1'],
				"ZZTUJUAN2"=>$_POST['tujuan2'],
				"ZZTUJUAN3"=>$_POST['tujuan3'],
				"ZZKEPERLUAN"=>$_POST['keperluan']);

			// IMPORT T_NOTIFITEM
			//echo "NOTIF TYPE=".$_GET['tpy'];
			//echo $_POST["jmlRowItem"];
			if($_GET['tpy']!='03' && $_GET['tpy']!='04' && $_GET['tpy']!='02' && $_GET['tpy']!='14'){
				$T_NOTIFITEM = array();
				if ($_POST["jmlRowItem"]>1) {
					for ($i=1;$i<=$_POST["jmlRowItem"];$i++) {
						$T_NOTITEM=array(
							"REFOBJECTKEY"=>"%000000000010001",
							"ITEM_KEY"=>cekNomerUrut($i), //tabhkn di detail
							"ITEM_SORT_NO"=>cekNomerUrut($i),
							"DL_CODEGRP"=>"",
							"DL_CODE"=>"",
							"ASSEMBLY"=>"",
							"ASSEMBLY_EXTERNAL"=>"",
							"ASSEMBLY_GUID"=>"",
							"ASSEMBLY_VERSION"=>"",
							"DESCRIPT"=>$_POST['DESCRIPT'.$i],
							"D_CODEGRP"=>$_POST['D_CODEGRP'.$i],
							"D_CODE"=>$_POST['D_CODE'.$i],
							"CHGTYPE"=>'A'
						);
						$T_NOTIFCAUS=array(
							"REFOBJECTKEY"=>"%000000000010001",
							"CAUSE_KEY"=>"0001", //tabhkn di detail
							"CAUSE_SORT_NO"=>cekNomerUrut($i),
							"ITEM_KEY"=>cekNomerUrut($i),
							"CAUSETEXT"=>$_POST['CAUSETEXT'.$i],
							"CAUSE_CODEGRP"=>$_POST['CAUSE_CODEGRP'.$i],
							"CAUSE_CODE"=>$_POST['CAUSE_CODE'.$i],
							"ITEM_SORT_NO"=>"0001",
							"CHGTYPE"=>'A'
						);
						//echo "<br>T_NOTIFITEM<br><br>";
						//print_r($T_NOTITEM);
						//echo "<br>T_NOTIFCAUS<br><br>";
						//print_r($T_NOTIFCAUS);
						$fce->T_NOTITEM->append ($T_NOTITEM);
						$fce->T_NOTIFCAUS->append ($T_NOTIFCAUS);
					}
				} else if ($_POST["jmlRowItem"]==1) {
					$T_NOTITEM=array(
						"REFOBJECTKEY"=>"%000000000010001",
						"ITEM_KEY"=>"0001", //tabhkn di detail
						"ITEM_SORT_NO"=>"0001",
						"DL_CODEGRP"=>"",
						"DL_CODE"=>"",
						"ASSEMBLY"=>"",
						"ASSEMBLY_EXTERNAL"=>"",
						"ASSEMBLY_GUID"=>"",
						"ASSEMBLY_VERSION"=>"",
						"DESCRIPT"=>$_POST['DESCRIPT'],
						"D_CODEGRP"=>$_POST['D_CODEGRP'],
						"D_CODE"=>$_POST['D_CODE'],
						"CHGTYPE"=>"A"
					);
					$T_NOTIFCAUS=array(
						"REFOBJECTKEY"=>"%000000000010001",
						"CAUSE_KEY"=>"0001", //tabhkn di detail
						"CAUSE_SORT_NO"=>"0001",
						"ITEM_KEY"=>"0001",
						"CAUSETEXT"=>$_POST['CAUSETEXT'],
						"CAUSE_CODEGRP"=>$_POST['CAUSE_CODEGRP'],
						"CAUSE_CODE"=>$_POST['CAUSE_CODE'],
						"ITEM_SORT_NO"=>"0001",
						"CHGTYPE"=>"A"
					);
					//echo "<br>T_NOTIFITEM<br><br>";
					//print_r($T_NOTITEM);
					//echo "<br>T_NOTIFCAUS<br><br>";
					//print_r($T_NOTIFCAUS);
					$fce->T_NOTITEM->append ($T_NOTITEM);
					$fce->T_NOTIFCAUS->append ($T_NOTIFCAUS);
				}
			}
			
			$NOTIFACTV = array();
			$ACTVREP=array();
			//ECHO $_POST["jmlRowAct"];
			if($_GET['tpy']!='04' && $_GET['tpy']!='07'){
				if ($_POST["jmlRowAct"]>1) {
					for($s='0';$s<=($_POST["jmlRowAct"]-1);$s++){
						if($_POST["r_".$s."_c_0"]!=''){
							$item=$_POST["r_".$s."_c_0"];
							$cg=$_POST["r_".$s."_c_1"];
							$ac=$_POST["r_".$s."_c_2"];
							$actx=$_POST["r_".$s."_c_3"];
							$at=$_POST["r_".$s."_c_4"];
							$lt=$_POST["r_".$s."_c_5"];
							$qf=$_POST["r_".$s."_c_6"];
							$sd=$_POST["r_".$s."_c_7"];
							$stm=I_Format_Tanggal($_POST["r_".$s."_c_8"]);
							$ed=I_Format_Waktu($_POST["r_".$s."_c_9"]);
							$estm=I_Format_Tanggal($_POST["r_".$s."_c_10"]);
							$activi=I_Format_Waktu($_POST["r_".$s."_c_11"]);
							#echo "TPY=".$_GET['tpy'];
							if($_GET['tpy']=='03'){
								#saprfc_table_init ($fce,"T_ACTVREP");
								$T_ACTVREP=array(
									"MANDT"=>"",
									"QMNUM"=>"",
									"SEQNO"=>$_POST["r_".$s."_c_1"],
									"EQUNR"=>$_POST["r_".$s."_c_2"],
									"EQKTX"=>$_POST["r_".$s."_c_3"],
									"CODE"=>$_POST["r_".$s."_c_4"],
									"KURZTEXT"=>$_POST["r_".$s."_c_5"],
									"KURZTEXT_L"=>$_POST["r_".$s."_c_6"],
									"PERSON"=>$_POST["r_".$s."_c_7"],
									"BEGDA"=>I_Format_Tanggal($_POST["r_".$s."_c_8"]),
									"BEGTI"=>I_Format_Waktu($_POST["r_".$s."_c_9"]),
									"ENDDA"=>I_Format_Tanggal($_POST["r_".$s."_c_10"]),
									"ENDTI"=>I_Format_Waktu($_POST["r_".$s."_c_11"]),
									"XSEL"=>""
								);
								$fce->T_ACTVREP->append($T_ACTVREP);
							}else{
								$T_NOTIFACTV=array(
									"REFOBJECTKEY"=>"%000000000010001",
									"ACT_SORT_NO"=>cekNomerUrut($s+1),
									"ITEM_SORT_NO"=>cekNomerUrut($_POST["r_".$s."_c_0"]),
									"ACT_CODEGRP"=>$_POST["r_".$s."_c_1"],
									"ACT_CODE"=>$_POST["r_".$s."_c_2"],
									"TXT_ACTCD"=>$_POST["r_".$s."_c_3"],
									"ACTTEXT"=>$_POST["r_".$s."_c_4"],
									"START_DATE"=>I_Format_Tanggal($_POST["r_".$s."_c_7"]),
									"START_TIME"=>I_Format_Waktu($_POST["r_".$s."_c_8"]),
									"END_DATE"=>I_Format_Tanggal($_POST["r_".$s."_c_9"]),
									"END_TIME"=>I_Format_Waktu($_POST["r_".$s."_c_10"]),
									"ACT_KEY"=>cekNomerUrut($s+1),
									"CHGTYPE"=>"A"
								);
								//echo "<br>T_NOTIFACTV<br><br>";
								//print_r($T_NOTIFACTV);
								$fce->T_NOTIFACTV->Append($T_NOTIFACTV);
							}
						}
					}
				} else if ($_POST["jmlRowAct"]==1) {
					if($_GET['tpy']=='03'){
						#saprfc_table_init ($fce,"T_ACTVREP");
						if ($_POST["r_0_c_1"]!='' && $_POST["r_0_c_2"]!='') {
							$T_ACTVREP=array(
								"MANDT"=>"",
								"QMNUM"=>"",
								"SEQNO"=>$_POST["r_0_c_1"],
								"EQUNR"=>$_POST["r_0_c_2"],
								"EQKTX"=>$_POST["r_0_c_3"],
								"CODE"=>$_POST["r_0_c_4"],
								"KURZTEXT"=>$_POST["r_0_c_5"],
								"KURZTEXT_L"=>$_POST["r_0_c_6"],
								"PERSON"=>$_POST["r_0_c_7"],
								"BEGDA"=>I_Format_Tanggal($_POST["r_0_c_8"]),
								"BEGTI"=>I_Format_Waktu($_POST["r_0_c_9"]),
								"ENDDA"=>I_Format_Tanggal($_POST["r_0_c_10"]),
								"ENDTI"=>I_Format_Waktu($_POST["r_0_c_11"]),
								"XSEL"=>""
							);
							//echo "<br>T_ACTVREP<br><br>";
							$fce->T_ACTVREP->append($T_ACTVREP);
						}
					}else{
						if($_GET['tpy']=='02' || $_GET['tpy']=='14'){
							$item = '0000';
						} else {
							$item = cekNomerUrut($_POST["r_0_c_0"]);
						}
						$T_NOTIFACTV=array(
							"REFOBJECTKEY"=>"%000000000010001",
							"ACT_SORT_NO"=>"0001",
							"ITEM_SORT_NO"=>$item,
							"ACT_CODEGRP"=>$_POST["r_0_c_1"],
							"ACT_CODE"=>$_POST["r_0_c_2"],
							"TXT_ACTCD"=>$_POST["r_0_c_3"],
							"ACTTEXT"=>$_POST["r_0_c_4"],
							"START_DATE"=>I_Format_Tanggal($_POST["r_0_c_7"]),
							"START_TIME"=>I_Format_Waktu($_POST["r_0_c_8"]),
							"END_DATE"=>I_Format_Tanggal($_POST["r_0_c_9"]),
							"END_TIME"=>I_Format_Waktu($_POST["r_0_c_10"]),
							"ACT_KEY"=>"0001",
							"CHGTYPE"=>"A"
						);
						//echo "<br>T_NOTIFACTV<br><br>";
						//print_r($T_NOTIFACTV);
						$fce->T_NOTIFACTV->Append($T_NOTIFACTV);
					}
				}
			}
			//print_r($T_ACTVREP);
			//exit();			
			$fce->Call();			   
			if ($fce->GetStatus() == SAPRFC_OK) {
			
				$fce->T_RETURN->Reset();
				while ( $fce->T_RETURN->Next() ){
					if($fce->T_RETURN->row["TYPE"]=="E"){
						$hasil = "Error : ";
						//echo "<div align='center'><font color='#FF0000'><strong>Error : ".$fce->T_RETURN->row["MESSAGE"]."</strong></font></div>";
						$message .= $fce->T_RETURN->row["MESSAGE"].", ";
					}else{
						$hasil = "Sukses : ";
						//echo "<div align='center'><font color='#FF0000'><strong>Sukses : ".$fce->T_RETURN->row["MESSAGE"]."</strong></font></div>";
						//echo "<meta http-equiv='refresh' content = '1;URL=main_frame.php?frame=changeNotif&mode=change'>";
						$message .= $fce->T_RETURN->row["MESSAGE"].", ";
					}
				}
				echo "<script>alert('".$hasil." ".$message."')</script>";
				echo "<meta http-equiv='refresh' content = '0;URL=main_frame.php?frame=createNotif'>";
			}else{
				ECHO $fce->GetStatusText();
				echo $sap->getStatusText()."</p>";
			}
		}else{
			echo $sap->getStatusText()."</p>";
		}
	}

	#$sap->Close();
?>