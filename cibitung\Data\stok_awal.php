<?
session_start();
include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

$halaman_id=939;
$user_id=$_SESSION['user_id'];
$user_org=$_SESSION['user_org'];
$distr_id=$fungsi->sapcode($_SESSION['distr_id']);
$nm_distr=$fungsi->findOneByOne($conn,"TB_USER_BOOKING","ID",$user_id,"NAMA_DISTRIBUTOR");

if ($fungsi->keamanan($halaman_id,$user_id)==0) {
?>
				<SCRIPT LANGUAGE="JavaScript">
				<!--
					alert("Anda tidak berhak meng akses halaman ini.... \n Login Dahulu...");
				//-->
				</SCRIPT>

	 <a href="../index.php">Login....</a>
<?

exit();
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<link href="../Templates/template_css.css" rel="stylesheet" type="text/css" />
<script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />

<head>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")

function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }

function IsNumeric(obj)
   //  check for valid numeric strings	
   {
   var strValidChars = "0123456789.";
   var strChar;
   var strString = obj.value;
   
   if (strString.length == 0){
     alert("Harus Diisi Angka..!!!");
	 obj.value="";
	 return false;
	} else {
		if (parseInt(strString) > 0 ){
		   //  test strString consists of valid characters listed above
		   for (i = 0; i < strString.length; i++)
			  {
			  strChar = strString.charAt(i);
			  if (strValidChars.indexOf(strChar) == -1)
				 {
				 alert("Hanya Masukkan Angka...!");
				 obj.value="";
				 return false;
				 }
			  }
		 }else{
		 alert("Masukkan Angka Lebih Dari 0..!!!");
		 obj.value="";
		 return false;
		 }	  
	 } 
   }
 
function cektanggal(obj) {
var com_tgl = document.getElementById(obj);
var com_kn = document.getElementById('tglnya');
var tgl = com_tgl.value;
var kn = com_kn.value;
var tgl1 = parseInt(tgl.substr(0,2));
var bln1 = parseInt(tgl.substr(3,2));
var th1 = parseInt(tgl.substr(6,4));
var tglo = bln1+"/"+tgl1+"/"+th1;
var tglx = new Date(tglo);
var tgl2 = parseInt(kn.substr(0,2));
var bln2 = parseInt(kn.substr(3,2));
var th2 = parseInt(kn.substr(6,4));
var tgln = bln2+"/"+tgl2+"/"+th2;
var knx = new Date(tgln);
	if( (tglx >= knx) )
	{
	 com_tgl.value=tgl;
	} else { com_tgl.value=kn; }
}

function cek_data() {
		var obj_spj = document.getElementById('nox_spj');
		var cek_spj = obj_spj.value;
		
		if (cek_spj == "") {
			window.alert("No SPJ tidak boleh kosong");
			return false;
		}
		
		return true;
 }
 
function validasi_data() {
	var obj = document.getElementById('jumlah');
	var cek = obj.value;
		
	for (var i = 1; i <= cek; i++){	
		if (validasi('kd_reason', 'Order Reason', 'R', 'keterangan', 'Keterangan', 'R')) {
		
		}else{
		document.hasil = false;
		return false;
		}		
	}
}
function findplant() {
		var comorg = document.getElementById('org');
                var comdis = document.getElementById('sold');
		var strURL="cari_plant.php?org="+comorg.value+"&distr="+comdis.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
        var comdis=document.getElementById('sold');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan+"&distr="+comdis.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {
					document.getElementById('plantdiv').innerHTML=req.responseText;
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}
		}
		req.open("GET", strURL, true);
		req.send(null);
	}
}

function ketik_produk(obj) {
	var strURL="ketik_semen.php?produk="+obj.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {
					document.getElementById("produkdiv").innerHTML=req.responseText;
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}
		}
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findproduk() {
		var strURL="cari_semen.php";
		popUp(strURL);
}
   
//-->
</script>

<title>Aplikasi SGG Online: Transfer Stok :)</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<body>
<div align="center">
<table width="700" align="center" class="adminheading" border="0">
<tr>
<th class="ba1">Insert Stok Awal</th>
</tr></table></div>

<div align="center">
<table width="700" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> Form Entri Stok Awal</th>
</tr>
</table>

<?
if($_POST['save']){
		echo $qty = $_POST['qty'];
		$qty = $_POST['qty'];
		$qty = $_POST['qty'];
	  $sql0="UPDATE crm_stok_gdg SET stok = '$qty',stok_awal ='$qty'WHERE kd_gdg = '$kd_gdg' AND kd_material='$produk'";
	$query0= oci_parse($conn, $sql0);
	$sukses = oci_execute($query0);
	if($sukses) echo "berhasil";
	else echo "gagal";

}
//onSubmit="validasi('nopol','','R','sopir','','R','sold_to','','R','shipto','','R','plant','','R');return document.hasil"
?>
 <form action="stok_awal.php" method="post" name="tambah">

 <table width="700" border="0" class="adminform" align="center">
 <tr>
    <td width="175"><strong>Gudang/Silo </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2">
            <input name="org" type="hidden" id="org" value="<?=$user_org?>"/>
            <input name="sold" type="hidden" id="sold" value="<?=$distr_id?>"/>
            <div id="plantdiv">
	    <input name="plant" type="text" class="inputlabel" id="plant" value="<?=$kd_gdg?>" onChange="ketik_plant(this)" maxlength="4" size="6"/>
	    <input name="nama_plant" type="text" id="nama_plant" value="<?=$nm_gdg?>" readonly="true"size="20"/>
            <input name="btn_plant" type="button" class="button" id="btn_plant" value="..." onClick="findplant()"/>
	    <input name="val_error_plant" type="hidden" id="val_error_plant" value="0" />
	  </div>
	</td>
	</tr>
  <tr>
    <td width="175"><strong>Material </strong></td>
    <td width="12"><strong>:</strong></td>
	<td colspan="2"><div id="produkdiv">
	  <input name="produk" type="text" class="inputlabel" id="produk" value="<?=$produk?>" onChange="ketik_produk(this)" maxlength="20" size="12"/>
           <input name="nama_produk" type="text" class="inputlabel" id="nama_produk" value="<?=$nama_produk?>" readonly="true"  size="20"/>
           <input name="uom" type="text" class="inputlabel" id="uom" value="<?=$uom?>" readonly="true" size="4" />
            <input name="btn_produk" type="button" class="button" id="btn_produk" value="..." onClick="findproduk()"/>
      <input name="val_error_produk" type="hidden" id="val_error_produk" value="0" />
    </div></td>
	</tr>
     <tr>
	<td><strong>Stok Awal</strong></td>
	<td><strong>:</strong></td>
	<td><input type="text" name="qty" id="qty" value="<?=$qty?>" size="10" /></td>
	</tr>
     <tr>
	<td><strong></strong></td>
	<td><strong></strong></td>
    <td ><div align="left">
	<input name="save" type="submit" class="button" id="save" value="Simpan"/>
         <input name="action" type="hidden" value="stokawal"/>
        <input name="status" type="hidden" value="<?=$status?>" />
        <a href="stok_awal.php" class="button">Cancel</a></div></td>
    </tr>
</table> 
</form>
</div>

</body>
</html>
