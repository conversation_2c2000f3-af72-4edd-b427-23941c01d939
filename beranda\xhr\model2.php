<?php
session_start();
include ('../../include/ex_fungsi.php');
$fungsi=new ex_fungsi();
$conn=$fungsi->ex_koneksi();
$user_id=trim($_SESSION['user_id']);
$user_org=$_SESSION['user_org'];
$user_name=$_SESSION['user_name'];

$user_data=$fungsi->ex_find_user($conn,$user_id);
$vendor=$user_data["vendor"];
if($vendor != ""){
    $filter .= " join EX_INVOICE b on NO_INV_SAP = b.ACCOUNTING_DOC and b.NO_VENDOR = '".$vendor."'";
    $filter1 .= " and I.NO_VENDOR = '".$vendor."'";
} else {
    $filter .= "";
    $filter1 .= "";
}

if($user_id==''){
  echo json_encode(array("Auth"=>false));
} else {
  $p = (!empty($_GET["p"]) ? $_GET["p"] : "");
  switch ($p) {
    case "stat-inv-exp":
    $tglAwal = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
    $tglAkhir = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
    $company = (!empty($_GET["company"]) ? $_GET["company"] : "");

  // This Main Query

    $q1 = "SELECT
    SUM (JMLEKSVER) AS JMLEKSVER,
    SUM (EKSVER) AS EKSVER,
    SUM (TERVER) AS TERVER,
    SUM (VERYES) AS VERYES,
    SUM (VERNO) AS VERNO,
    SUM (EKSBEND) AS EKSBEND,
    SUM (TERBER) AS TERBER,
    SUM (STATBAYSIAP) AS STATBAYSIAP,
    SUM (STATBAYPAID) AS STATBAYPAID,
    SUM (STATBAYCOL) AS STATBAYCOL
    FROM
    (
      SELECT
      SUM (JMLEKSVER) AS JMLEKSVER,
      SUM (EKSVER) AS EKSVER,
      SUM (TERVER) AS TERVER,
      SUM (VERYES) AS VERYES,
      SUM (VERNO) AS VERNO,
      SUM (EKSBEND) AS EKSBEND,
      SUM (TERBER) AS TERBER,
      SUM (STATBAYSIAP) AS STATBAYSIAP,
      SUM (STATBAYPAID) AS STATBAYPAID,
      SUM (STATBAYCOL) AS STATBAYCOL
      FROM
      (
        SELECT
        1 AS JMLEKSVER,
        CASE
        WHEN TGL_VER <= TO_DATE ('0010-01-01', 'YYYY-MM-DD')
        AND TGL_KIRVER <= TO_DATE ('0001-01-01', 'YYYY-MM-DD')
        AND TGL_BEND <= TO_DATE ('0001-01-01', 'YYYY-MM-DD') THEN
        1
        ELSE
        0
        END AS EKSVER,
        0 AS TERVER,
        0 AS JMLTOTVER,
        0 AS VERYES,
        0 AS VERNO,
        0 AS EKSBEND,
        0 AS TERBER,
        0 AS STATBAYSIAP,
        0 AS STATBAYPAID,
        0 AS STATBAYCOL
        FROM
        EX_INVOICE_SYNC_SAP".$filter."
        WHERE
        TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
        AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
        AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
        AND COMPANY = '".$company."'
        ) A
        UNION ALL
        SELECT
        SUM (JMLEKSVER) AS JMLEKSVER,
        SUM (EKSVER) AS EKSVER,
        SUM (TERVER) AS TERVER,
        SUM (VERYES) AS VERYES,
        SUM (VERNO) AS VERNO,
        SUM (EKSBEND) AS EKSBEND,
        SUM (TERBER) AS TERBER,
        SUM (STATBAYSIAP) AS STATBAYSIAP,
        SUM (STATBAYPAID) AS STATBAYPAID,
        SUM (STATBAYCOL) AS STATBAYCOL
        FROM
        (
          SELECT
          0 AS JMLEKSVER,
          0 AS EKSVER,
          1 AS TERVER,
          0 AS JMLTOTVER,
          0 AS VERYES,
          0 AS VERNO,
          0 AS EKSBEND,
          0 AS TERBER,
          0 AS STATBAYSIAP,
          0 AS STATBAYPAID,
          0 AS STATBAYCOL
          FROM
          EX_INVOICE_SYNC_SAP".$filter."
          WHERE
          TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
          AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
          AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
          AND (
            TGL_VER > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
            OR TGL_KIRVER > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
            )
            AND COMPANY = '".$company."'
            ) A
            UNION ALL
            SELECT
            SUM (JMLEKSVER) AS JMLEKSVER,
            SUM (EKSVER) AS EKSVER,
            SUM (TERVER) AS TERVER,
            SUM (VERYES) AS VERYES,
            SUM (VERNO) AS VERNO,
            SUM (EKSBEND) AS EKSBEND,
            SUM (TERBER) AS TERBER,
            SUM (STATBAYSIAP) AS STATBAYSIAP,
            SUM (STATBAYPAID) AS STATBAYPAID,
            SUM (STATBAYCOL) AS STATBAYCOL
            FROM
            (
              SELECT
              0 AS JMLEKSVER,
              0 AS EKSVER,
              0 AS TERVER,
              0 AS JMLTOTVER,
              1 AS VERYES,
              0 AS VERNO,
              0 AS EKSBEND,
              0 AS TERBER,
              0 AS STATBAYSIAP,
              0 AS STATBAYPAID,
              0 AS STATBAYCOL
              FROM
              EX_INVOICE_SYNC_SAP".$filter."
              WHERE
              TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
              AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
              AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
              AND (
                TGL_VER > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
                OR TGL_BEND > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
                )
                AND COMPANY = '".$company."'
                ) A
                UNION ALL
                SELECT
                SUM (JMLEKSVER) AS JMLEKSVER,
                SUM (EKSVER) AS EKSVER,
                SUM (TERVER) AS TERVER,
                SUM (VERYES) AS VERYES,
                SUM (VERNO) AS VERNO,
                SUM (EKSBEND) AS EKSBEND,
                SUM (TERBER) AS TERBER,
                SUM (STATBAYSIAP) AS STATBAYSIAP,
                SUM (STATBAYPAID) AS STATBAYPAID,
                SUM (STATBAYCOL) AS STATBAYCOL
                FROM
                (
                  SELECT
                  0 AS JMLEKSVER,
                  0 AS EKSVER,
                  0 AS TERVER,
                  0 AS JMLTOTVER,
                  0 AS VERYES,
                  1 AS VERNO,
                  0 AS EKSBEND,
                  0 AS TERBER,
                  0 AS STATBAYSIAP,
                  0 AS STATBAYPAID,
                  0 AS STATBAYCOL
                  FROM
                  EX_INVOICE_SYNC_SAP".$filter."
                  WHERE
                  TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                  AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                  AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                  AND STATUS_UKP = 'Dokumen di UKP'
                  AND COMPANY = '".$company."'
                  ) A
                  UNION ALL
                  SELECT
                  SUM (JMLEKSVER) AS JMLEKSVER,
                  SUM (EKSVER) AS EKSVER,
                  SUM (TERVER) AS TERVER,
                  SUM (VERYES) AS VERYES,
                  SUM (VERNO) AS VERNO,
                  SUM (EKSBEND) AS EKSBEND,
                  SUM (TERBER) AS TERBER,
                  SUM (STATBAYSIAP) AS STATBAYSIAP,
                  SUM (STATBAYPAID) AS STATBAYPAID,
                  SUM (STATBAYCOL) AS STATBAYCOL
                  FROM
                  (
                    SELECT
                    0 AS JMLEKSVER,
                    0 AS EKSVER,
                    0 AS TERVER,
                    0 AS JMLTOTVER,
                    0 AS VERYES,
                    0 AS VERNO,
                    1 AS EKSBEND,
                    0 AS TERBER,
                    0 AS STATBAYSIAP,
                    0 AS STATBAYPAID,
                    0 AS STATBAYCOL
                    FROM
                    EX_INVOICE_SYNC_SAP".$filter."
                    WHERE
                    TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                    AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                    AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                    AND TGL_KIRVER > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
                    AND TGL_BEND <= TO_DATE ('0010-01-01', 'YYYY-MM-DD')
                    AND COMPANY = '".$company."'
                    ) A
                    UNION ALL
                    SELECT
                    SUM (JMLEKSVER) AS JMLEKSVER,
                    SUM (EKSVER) AS EKSVER,
                    SUM (TERVER) AS TERVER,
                    SUM (VERYES) AS VERYES,
                    SUM (VERNO) AS VERNO,
                    SUM (EKSBEND) AS EKSBEND,
                    SUM (TERBER) AS TERBER,
                    SUM (STATBAYSIAP) AS STATBAYSIAP,
                    SUM (STATBAYPAID) AS STATBAYPAID,
                    SUM (STATBAYCOL) AS STATBAYCOL
                    FROM
                    (
                      SELECT
                      0 AS JMLEKSVER,
                      0 AS EKSVER,
                      0 AS TERVER,
                      0 AS JMLTOTVER,
                      0 AS VERYES,
                      0 AS VERNO,
                      0 AS EKSBEND,
                      1 AS TERBER,
                      0 AS STATBAYSIAP,
                      0 AS STATBAYPAID,
                      0 AS STATBAYCOL
                      FROM
                      EX_INVOICE_SYNC_SAP".$filter."
                      WHERE
                      TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                      AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                      AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                      AND TGL_BEND > TO_DATE ('0010-01-01', 'YYYY-MM-DD')
                      AND COMPANY = '".$company."'
                      ) A
                      UNION ALL
                      SELECT
                      SUM (JMLEKSVER) AS JMLEKSVER,
                      SUM (EKSVER) AS EKSVER,
                      SUM (TERVER) AS TERVER,
                      SUM (VERYES) AS VERYES,
                      SUM (VERNO) AS VERNO,
                      SUM (EKSBEND) AS EKSBEND,
                      SUM (TERBER) AS TERBER,
                      SUM (STATBAYSIAP) AS STATBAYSIAP,
                      SUM (STATBAYPAID) AS STATBAYPAID,
                      SUM (STATBAYCOL) AS STATBAYCOL
                      FROM
                      (
                        SELECT
                        0 AS JMLEKSVER,
                        0 AS EKSVER,
                        0 AS TERVER,
                        0 AS JMLTOTVER,
                        0 AS VERYES,
                        0 AS VERNO,
                        0 AS EKSBEND,
                        0 AS TERBER,
                        1 AS STATBAYSIAP,
                        0 AS STATBAYPAID,
                        0 AS STATBAYCOL
                        FROM
                        EX_INVOICE_SYNC_SAP".$filter."
                        WHERE
                        TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                        AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                        AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                        AND STATUS = 'SIAP BAYAR'
                        AND COMPANY = '".$company."'
                        ) A
                        UNION ALL
                        SELECT
                        SUM (JMLEKSVER) AS JMLEKSVER,
                        SUM (EKSVER) AS EKSVER,
                        SUM (TERVER) AS TERVER,
                        SUM (VERYES) AS VERYES,
                        SUM (VERNO) AS VERNO,
                        SUM (EKSBEND) AS EKSBEND,
                        SUM (TERBER) AS TERBER,
                        SUM (STATBAYSIAP) AS STATBAYSIAP,
                        SUM (STATBAYPAID) AS STATBAYPAID,
                        SUM (STATBAYCOL) AS STATBAYCOL
                        FROM
                        (
                          SELECT
                          0 AS JMLEKSVER,
                          0 AS EKSVER,
                          0 AS TERVER,
                          0 AS JMLTOTVER,
                          0 AS VERYES,
                          0 AS VERNO,
                          0 AS EKSBEND,
                          0 AS TERBER,
                          0 AS STATBAYSIAP,
                          1 AS STATBAYPAID,
                          0 AS STATBAYCOL
                          FROM
                          EX_INVOICE_SYNC_SAP".$filter."
                          WHERE
                          TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                          AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                          AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                          AND STATUS = 'PAID'
                          AND COMPANY = '".$company."'
                          ) A
                          UNION ALL
                          SELECT
                          SUM (JMLEKSVER) AS JMLEKSVER,
                          SUM (EKSVER) AS EKSVER,
                          SUM (TERVER) AS TERVER,
                          SUM (VERYES) AS VERYES,
                          SUM (VERNO) AS VERNO,
                          SUM (EKSBEND) AS EKSBEND,
                          SUM (TERBER) AS TERBER,
                          SUM (STATBAYSIAP) AS STATBAYSIAP,
                          SUM (STATBAYPAID) AS STATBAYPAID,
                          SUM (STATBAYCOL) AS STATBAYCOL
                          FROM
                          (
                            SELECT
                            0 AS JMLEKSVER,
                            0 AS EKSVER,
                            0 AS TERVER,
                            0 AS JMLTOTVER,
                            0 AS VERYES,
                            0 AS VERNO,
                            0 AS EKSBEND,
                            0 AS TERBER,
                            0 AS STATBAYSIAP,
                            0 AS STATBAYPAID,
                            1 AS STATBAYCOL
                            FROM
                            EX_INVOICE_SYNC_SAP".$filter."
                            WHERE
                            TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD')
                            AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD')
                            AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'
                            AND STATUS = 'COLLECTION'
                            AND COMPANY = '".$company."'
                            ) A
                            ) b";

                            $parse1 = oci_parse($conn, $q1);
                            oci_execute($parse1);
                            $out = array();
                            $arr1 = oci_fetch_assoc($parse1);
                            $out = $arr1;
                            $chartStatVer = array(
                              "datasets" => array(array(
                                "data" => array(intval($out["VERYES"]), intval($out["VERNO"])),
                                "backgroundColor" => array("#009688", "#F44336"),
                                "label" => "Status Verifikasi"
                              )),
                              "labels" => array("DITERIMA", "DITOLAK")
                            );

                            $chartStatPemb = array(
                              "datasets" => array(array(
                                "data" => array(intval($out["STATBAYSIAP"]), intval($out["STATBAYPAID"]), intval($out["STATBAYCOL"])),
                                "backgroundColor" => array("#FFC107", "#009688", "#F44336"),
                                "label" => "Status Pembayaran"
                              )),
                              "labels" => array("SIAP BAYAR", "DIBAYAR", "DITAGIH")
                            );

                            // Rata-rata Hari BEND
                            $queriAvgBend="SELECT
                            AVG(TO_DATE (TGL_BEND, 'dd-mm-yy') - TO_DATE (TGL_VER, 'dd-mm-yy')) AS SELISIH
                            FROM
                            EX_INVOICE_SYNC_SAP".$filter."
                            WHERE
                            TRUNC (TGL) BETWEEN TO_DATE ('".$tglAwal."', 'YYYY-MM-DD')

                            AND TO_DATE ('".$tglAkhir."', 'YYYY-MM-DD')
                            AND COMPANY = '".$company."'
                            AND TGL_VER != TO_DATE ('1-01-01', 'YYYY-MM-DD')
                            AND TGL_BEND != TO_DATE('1-01-01', 'YYYY-MM-DD')";

                            $par1 = oci_parse($conn, $queriAvgBend);
                            oci_execute($par1);
                            $arr3 = oci_fetch_assoc($par1);
                            $avg_bend= number_format($arr3["SELISIH"],0);

                            $queriAvgVer="SELECT
                            AVG(TO_DATE (TGL_VER, 'dd-mm-yy') - TO_DATE (TGL, 'dd-mm-yy')) AS SELISIH
                            FROM
                            EX_INVOICE_SYNC_SAP".$filter."
                            WHERE
                            TRUNC (TGL) BETWEEN TO_DATE ('".$tglAwal."', 'YYYY-MM-DD')

                            AND TO_DATE ('".$tglAkhir."', 'YYYY-MM-DD')
                            AND COMPANY = '".$company."'
                            AND TGL_VER != TO_DATE ('1-01-01', 'YYYY-MM-DD')" ;

                            $par2 = oci_parse($conn, $queriAvgVer);
                            oci_execute($par2);

                            $arr4 = oci_fetch_assoc($par1);
                            $avg_ver= number_format($arr4["SELISIH"],0);


                            echo json_encode(array("_callbackPeriodeStatInvExp"=>$tglAwal."|".$tglAkhir, "singleData"=>$out, "chartStatver"=>$chartStatVer, "chartStatPemb"=>$chartStatPemb,"avgbend"=>$avg_bend,"avgver"=>$avg_ver));

                            break;

                            case 'stat-inv-exp-detail':
                            $proses = (!empty($_GET["proses"]) ? $_GET["proses"] : "");
                            $tglAwal = (!empty($_GET["tglAwal"]) ? $_GET["tglAwal"] : "");
                            $tglAkhir = (!empty($_GET["tglAkhir"]) ? $_GET["tglAkhir"] : "");
                            $company = (!empty($_GET["company"]) ? $_GET["company"] : "");
                            $masterQuery = "SELECT NO_INV_SAP, TGL, NAME1 FROM EX_INVOICE_SYNC_SAP".$filter." WHERE COMPANY = '".$company."'";
                            if ($proses == "EKSVER") {
                              $q1 = $masterQuery." AND TGL_VER <= TO_DATE ('0010-01-01', 'YYYY-MM-DD') AND  TGL_KIRVER <= TO_DATE ('0001-01-01', 'YYYY-MM-DD') AND  TGL_BEND <= TO_DATE ('0001-01-01', 'YYYY-MM-DD')  AND TRUNC(TGL) BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD') AND NVL (STATUS, '----') NOT LIKE 'CANCEL%'";
                              $parse = oci_parse($conn, $q1);
                              oci_execute($parse);
                              $return = array();
                              while($arr = oci_fetch_assoc($parse)){
                                $return[] = $arr;
                              }
                              echo json_encode(array("detail"=>$return));
                            } else if($proses == "TERVER"){
                              $q1 = $masterQuery." AND TRUNC(TGL) BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD') AND NVL (STATUS, '----') NOT LIKE 'CANCEL%' AND (TGL_VER > TO_DATE ('0010-01-01', 'YYYY-MM-DD') OR TGL_KIRVER > TO_DATE ('0010-01-01', 'YYYY-MM-DD'))";
                              $parse = oci_parse($conn, $q1);
                              oci_execute($parse);
                              $return = array();
                              while($arr = oci_fetch_assoc($parse)){
                                $return[] = $arr;
                              }
                              echo json_encode(array("detail"=>$return));
                            } elseif ($proses == "EKSBEND") {
                              $q1 = $masterQuery." AND TRUNC(TGL) BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD') AND NVL (STATUS, '----') NOT LIKE 'CANCEL%' AND TGL_KIRVER > TO_DATE ('0010-01-01', 'YYYY-MM-DD') AND TGL_BEND <= TO_DATE ('0010-01-01', 'YYYY-MM-DD')";
                              $parse = oci_parse($conn, $q1);
                              oci_execute($parse);
                              $return = array();
                              while($arr = oci_fetch_assoc($parse)){
                                $return[] = $arr;
                              }
                              echo json_encode(array("detail"=>$return));
                            } elseif ($proses == "TERBER") {
                              $q1 = $masterQuery." 	AND TGL BETWEEN TO_DATE('".$tglAwal."', 'YYYY-MM-DD') AND TO_DATE('".$tglAkhir."', 'YYYY-MM-DD') AND NVL (STATUS, '----') NOT LIKE 'CANCEL%' AND TGL_BEND > TO_DATE ('0010-01-01', 'YYYY-MM-DD')";
                              $parse = oci_parse($conn, $q1);
                              oci_execute($parse);
                              $return = array();
                              while($arr = oci_fetch_assoc($parse)){
                                $return[] = $arr;
                              }
                              echo json_encode(array("detail"=>$return));
                            }else {
                              echo json_encode(array("detail"=>false));
                            }
                            break;

                            case "chart-assigment":
                            $from = (!empty($_GET["from"]) ? $_GET["from"] : "");
                            $to = (!empty($_GET["to"]) ? $_GET["to"] : "");
                            $company = (!empty($_GET["company"]) ? $_GET["company"] : "");

                            $q1 = "SELECT
                            B.NAMA_LENGKAP AS ASSING_TO,
                            COUNT (I.NO_INVOICE) JMLINV
                            FROM
                            KPI_TERIMA_INV_VENDOR I
                            JOIN EX_INVOICE f ON f.NO_INVOICE = I.NO_INVOICE
                            AND f.NO_INVOICE_SAP IS NULL
                            JOIN KPI_TERIMA_ASSINGMENT A ON A .NO_GROUP_VENDOR = I.NOGROUP_VENDOR
                            JOIN TB_USER_BOOKING B ON b. ID = A .ASSING_TO
                            WHERE
                            DEL = 0
                            AND I.TGL_TERIMA BETWEEN TO_DATE('".$from."', 'YYYY-MM-DD') AND TO_DATE('".$to."', 'YYYY-MM-DD')
                            AND ORG = '".$company."'".$filter1."
                            GROUP BY
                            B.NAMA_LENGKAP
                            ORDER BY
                            ASSING_TO";
                            $parse = oci_parse($conn, $q1);
                            oci_execute($parse);
                            $arr_label = array();
                            $arr_data = array();
                            $arr_color = array();
                            while ($arr = oci_fetch_assoc($parse)) {
                              array_push($arr_label, $arr["ASSING_TO"]);
                              array_push($arr_data, $arr["JMLINV"]);
                              array_push($arr_color, "#009688");
                            }
                            $returnChart = array(
                              "labels" => $arr_label,
                              "datasets" => array(array(
                                "label" => "Operator Distran",
                                "fill" => "false",
                                "backgroundColor" => $arr_color,
                                "data" => $arr_data
                              ))
                            );
                            echo json_encode(array("chart"=>$returnChart, "tes"=>$q1));
                            break;

                            case "detail-assigment":
                            $name = (!empty($_GET["name"]) ? $_GET["name"] : "");
                            $company = (!empty($_GET["company"]) ? $_GET["company"] : "");

                            $q1 = "SELECT
                            A .ASSING_TO AS ID_ASSING,
                            I.NOGROUP_VENDOR,
                            I.NO_INVOICE,
                            I.NAMA_VENDOR,
                            I.TGl_TERIMA,
                            B.NAMA_LENGKAP AS ASSING_TO,
                            CAST (
                              CURRENT_DATE - TGL_TERIMA AS INTEGER
                            ) AS LAMA_PROSES
                            FROM
                            KPI_TERIMA_INV_VENDOR I
                            JOIN EX_INVOICE f ON f.NO_INVOICE = I.NO_INVOICE
                            AND f.NO_INVOICE_SAP IS NULL
                            JOIN KPI_TERIMA_ASSINGMENT A ON A .NO_GROUP_VENDOR = I.NOGROUP_VENDOR
                            JOIN TB_USER_BOOKING B ON b. ID = A .ASSING_TO
                            WHERE
                            I.DEL = 0
                            AND B.NAMA_LENGKAP = '".$name."'
                            AND ORG = '".$company."'".$filter1."
                            ORDER BY
                            I.NO_INVOICE ";
                            $parse = oci_parse($conn, $q1);
                            oci_execute($parse);
                            $return = array();
                            while ($arr = oci_fetch_assoc($parse)) {
                              $return[] = $arr;
                            }
                            echo json_encode(array("dataDetail"=>$return));
                            break;

                            default:
                            echo json_encode(array("success" => false));
                          }
                        }
                        ?>
