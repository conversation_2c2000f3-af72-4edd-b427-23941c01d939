<? 
session_start();
include ('../include/crm_fungsi1.php');
include ('../include/validasi.php');
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();

if(isset($_POST['cari'])){
		$tglm = $_POST['tgl1'];
		list($day,$month,$year)=split("-",$tglm);
		$tglm=$year.$month.$day;
		$tgls = $_POST['tgl2'];
		list($day1,$month1,$year1)=split("-",$tgls);
		$tgls=$year1.$month1.$day1;

		$sap = new SAPConnection();
	    $sap->Connect("../include/sapclasses/logon_data.conf");
		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) {
		echo $sap->PrintStatus();
		exit;
		}
	    
		$fce = $sap->NewFunction("Z_ZAPPSD_SELISIH_OA_TERBAYAR");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}    		
		        $fce->I_BUKRS ="2000";
		        $fce-> S_DELIV_TYPE->ROW["SIGN"]="I";
				$fce-> S_DELIV_TYPE->ROW["OPTION"]="EQ";
				$fce-> S_DELIV_TYPE->ROW["LOW"]="ZLF";
				$fce-> S_DELIV_TYPE->ROW["HIGH"]="";
				$fce->S_DELIV_TYPE->Append($fce->S_DELIV_TYPE->ROW);

				$fce-> S_GD_MV_DATE->ROW["SIGN"]="I";
				$fce-> S_GD_MV_DATE->ROW["OPTION"]="BT";
				$fce-> S_GD_MV_DATE->ROW["LOW"]=$tglm;//"20100101";
				$fce-> S_GD_MV_DATE->ROW["HIGH"]=$tgls;//"20111231";
				$fce->S_GD_MV_DATE->Append($fce->S_GD_MV_DATE->ROW);
					
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {	
		    $fce->T_SELISIH_OA->Reset();
			while ( $fce->T_SELISIH_OA->Next() ){
			
		 		$VKORG[] = $fce->T_SELISIH_OA->row["VKORG"];
		 		$VBELN[] = $fce->T_SELISIH_OA->row["VBELN"];
		 		$POSNR[] = $fce->T_SELISIH_OA->row["POSNR"];
		 		$VGBEL[] = $fce->T_SELISIH_OA->row["VGBEL"];
		 		$VGPOS[] = $fce->T_SELISIH_OA->row["VGPOS"];
		 		$MATNR[] = $fce->T_SELISIH_OA->row["MATNR"];
		 		$ARKTX[] = $fce->T_SELISIH_OA->row["ARKTX"];
		 		$WERKS[] = $fce->T_SELISIH_OA->row["WERKS"];
		 		$LFIMG[] = $fce->T_SELISIH_OA->row["LFIMG"];
		 		$MEINS[] = $fce->T_SELISIH_OA->row["MEINS"];
                $NTGEW[] = $fce->T_SELISIH_OA->row["NTGEW"];
		 		$GEWEI[] = $fce->T_SELISIH_OA->row["GEWEI"];
		 		$TON[] = $fce->T_SELISIH_OA->row["TON"];
		 		$KET_TON[] = $fce->T_SELISIH_OA->row["KET_TON"];
		 		$LFART[] = $fce->T_SELISIH_OA->row["LFART"];
		 		$WADAT_IST[] = $fce->T_SELISIH_OA->row["WADAT_IST"];
		 		$INCO1[] = $fce->T_SELISIH_OA->row["INCO1"];
		 		$ZTERM[] = $fce->T_SELISIH_OA->row["ZTERM"];
		 		$VKBUR[] = $fce->T_SELISIH_OA->row["VKBUR"];
		 		$BEZEI[] = $fce->T_SELISIH_OA->row["BEZEI"];
		 		$BZIRK[] = $fce->T_SELISIH_OA->row["BZIRK"];
		 		$BZTXT[] = $fce->T_SELISIH_OA->row["BZTXT"];
		 		$KUNNR[] = $fce->T_SELISIH_OA->row["KUNNR"];
		 		$NAME1_SHIP[] = $fce->T_SELISIH_OA->row["MNAME1_SHIP"];
                $KUNAG[] = $fce->T_SELISIH_OA->row["KUNAG"];
		 		$NAME1_SOLD[] = $fce->T_SELISIH_OA->row["NAME1_SOLD"];
		 		$TKNUM[] = $fce->T_SELISIH_OA->row["TKNUM"];
		 		$SHTYP[] = $fce->T_SELISIH_OA->row["SHTYP"];
		 		$EXTI1[] = $fce->T_SELISIH_OA->row["EXTI1"];
		 		$ADD01[] = $fce->T_SELISIH_OA->row["ADD01"];
		 		$WBSTK[] = $fce->T_SELISIH_OA->row["WBSTK"];
		 		$FKSTK[] = $fce->T_SELISIH_OA->row["FKSTK"];
		 		$TRSTA[] = $fce->T_SELISIH_OA->row["TRSTA"];
		 		$FKART_BILL[] = $fce->T_SELISIH_OA->row["FKART_BILL"];
		 		$VBELN_BILL[] = $fce->T_SELISIH_OA->row["VBELN_BILL"];
		 		$POSNR_BILL[] = $fce->T_SELISIH_OA->row["POSNR_BILL"];
		 		$FKDAT[] = $fce->T_SELISIH_OA->row["FKDAT"];
		 		$KNUMV[] = $fce->T_SELISIH_OA->row["KNUMV"];
                $NET[] = $fce->T_SELISIH_OA->row["NET"];
		 		$PPN[] = $fce->T_SELISIH_OA->row["PPN"];
		 		$PPH[] = $fce->T_SELISIH_OA->row["PPH"];
		 		$SP3[] = $fce->T_SELISIH_OA->row["SP3"];
		 		$WAERS[] = $fce->T_SELISIH_OA->row["WAERS"];
		 		$FKNUM[] = $fce->T_SELISIH_OA->row["FKNUM"];
		 		$STABR[] = $fce->T_SELISIH_OA->row["STABR"];
		 		$STFRE[] = $fce->T_SELISIH_OA->row["STFRE"];
		 		$STBER[] = $fce->T_SELISIH_OA->row["STBER"];
		 		$ERDAT[] = $fce->T_SELISIH_OA->row["ERDAT"];
		 		$BUDAT[] = $fce->T_SELISIH_OA->row["BUDAT"];
		 		$EBELN[] = $fce->T_SELISIH_OA->row["EBELN"];
		 		$EBELP[] = $fce->T_SELISIH_OA->row["EBELP"];
		 		$NETWR[] = $fce->T_SELISIH_OA->row["NETWR"];
                $MWSBP[] = $fce->T_SELISIH_OA->row["MWSBP"];
		 		$DMBTR[] = $fce->T_SELISIH_OA->row["DMBTR"];
		 		$TOTAL_AMOUNT[] = $fce->T_SELISIH_OA->row["TOTAL_AMOUNT"];
		 		$WAERS_P[] = $fce->T_SELISIH_OA->row["WAERS_P"];
		 		$AUGRU[] = $fce->T_SELISIH_OA->row["AUGRU"];
		 		$BEZEI_TVAU[] = $fce->T_SELISIH_OA->row["BEZEI_TVAU"];
                $NCOUNT[] = $fce->T_SELISIH_OA->row["NCOUNT"];
			}
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();
		
		$total = count($VKORG);
	

}
?>
<script language=javascript>
<!-- Edit the message as your wish -->

var message="You dont have permission to right click";

function clickIE()
 
{if (document.all)
{(message);return false;}}
 
function clickNS(e) {
if
(document.layers||(document.getElementById&&!document.all))
{
if (e.which==2||e.which==3) {(message);return false;}}}
if (document.layers)
{document.captureEvents(Event.MOUSEDOWN);document.  onmousedown=clickNS;}
else
{document.onmouseup=clickNS;document.oncontextmenu  =clickIE;}
 
document.oncontextmenu=new Function("return false")
function getXMLHTTP() { 
		var xmlhttp=false;	
		try{
			xmlhttp=new XMLHttpRequest();
		}
		catch(e)	{		
			try{			
				xmlhttp= new ActiveXObject("Microsoft.XMLHTTP");
			}
			catch(e){
				try{
				xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
				}
				catch(e1){
					xmlhttp=false;
				}
			}
		}
		 	
		return xmlhttp;
    }
function findplant() {	
		var comorg = document.getElementById('org');
		var strURL="cari_plant.php?org="+comorg.value;
		popUp(strURL);
}
function ketik_plant(obj) {
	var com=document.getElementById('org');
	var nilai_tujuan =obj.value;
	var cplan=document.getElementById('nama_plant');						
	cplan.value = "";
	var strURL="ketik_plant.php?org="+com.value+"&plant="+nilai_tujuan;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById('plantdiv').innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}
function findshipto() {	
		var com_sold = document.getElementById('sold_to');
		var strURL="cari_shipto.php?&sold_to="+com_sold.value;
		popUp(strURL);
}

function ketik_shipto(obj) {
	var com_sold = document.getElementById('sold_to');
	var strURL="ketik_shipto.php?shipto="+obj.value+"&sold_to="+com_sold.value;
	var req = getXMLHTTP();
	if (req) {
		req.onreadystatechange = function() {
			if (req.readyState == 4) {
				// only if "OK"
				if (req.status == 200) {						
					document.getElementById("shiptodiv").innerHTML=req.responseText;						
				} else {
					alert("There was a problem while using XMLHTTP:\n" + req.statusText);
				}
			}				
		}			
		req.open("GET", strURL, true);
		req.send(null);
	}
}

</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data Realisasi Distributor :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/print.css" rel="stylesheet" type="text/css" media="print" />

</head>

<body>
<div align="center">
<table width="600" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Maping Ongkos Angkut </th>
</tr></table></div>
<?
	if($total<1){
?>

<div align="center">
<table width="600" align="center" class="adminlist">
<tr>
<th align="left" colspan="4"> &nbsp;Form Search Maping Ongkos Angkut </th>
</tr>
</table>
</div>

<form id="tambah" name="tambah" method="post" action="<? echo $page; ?>" onSubmit="validasi('tgl1','','R','tgl2','','R');return document.hasil">
  <table width="600" align="center" class="adminform">
    <tr width="174">
      <td class="puso">&nbsp;</td>
      <td class="puso">&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td  class="puso">Tanggal</td>
      <td  class="puso">:</td>
      <td ><input name="tgl1" type="text" id="tgl1" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl1');"/>&nbsp; s.d &nbsp;
	<input name="tgl2" type="text" id="tgl2" size=12 value="<?=gmdate("d-m-Y",time()+60*60*7);?>" onClick="return showCalendar('tgl2');"/></td>
    </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    <td rowspan="2"><input name="cari" type="submit" id="cari" value="Find" class="button"/> </tr>
    <tr>
      <td class="ThemeOfficeMenu">&nbsp;</td>
      <td class="ThemeOfficeMenu">&nbsp;</td>
    </tr>
  </table>
</form>
<? } ?>
<br />
<br />
<?
	if($total>0){
?>
	<div align="center">
	<form name="export" method="post" action="daftar_tsdp_xls.php">
	<input name="plant" type="hidden" id="plant" value="<?=$plant?>"/>
	<input name="shipto" type="hidden" id="shipto" size=12 value="<?=$shipto?>"/>
	<input name="tgl1" type="hidden" id="tgl1" size=12 value="<?=$tgl1?>" />
	<input name="tgl2" type="hidden" id="tgl2" size=12 value="<?=$tgl2?>" />
	<input name="Print" type="button" id="Print" value="Cetak"  onclick="javascript:window.print();" class="nonPrint" /> 	
	&nbsp;&nbsp;
	<input name="excel" type="Submit" id="excel" value="Export" /> 	
	<table width="3800" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Maping Ongkos Angkut</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="3800" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		 <td align="center"><strong>Sales Organization</strong></td>
		<td align="center"><strong >Sales Document</strong></td>
		<td align="center"><strong >Item (SD)</strong></td>
		<td align="center"><strong >Reference document</strong></td>
		<td align="center"><strong >Reference item</strong></td>
		<td align="center"><strong >Material</strong></td>
		<td align="center"><strong >Nama Material</strong></td>
		<td align="center"><strong >Plant</strong></td>
		<td align="center"><strong >Delivery quantity</strong></td>
		<td align="center"><strong >Base Unit of Measure</strong></td>
		<td align="center"><strong >Net Weight</strong></td>
		<td align="center"><strong >Weight unit</strong></td>
		<td align="center"><strong >Delivery quantity</strong></td>
		<td align="center"><strong >Sales unit</strong></td>
		<td align="center"><strong>Delivery Type</strong></td>
		 <td align="center"><strong>Act. Gds Mvmnt Date</strong></td>
		<td align="center"><strong >Incoterms</strong></td>
		<td align="center"><strong >Terms of Payment</strong></td>
		<td align="center"><strong >Sales Office</strong></td>
		<td align="center"><strong >Name</strong></td>
		<td align="center"><strong >Sales district</strong></td>
		<td align="center"><strong >District name</strong></td>
		<td align="center"><strong >Ship-to party</strong></td>
		<td align="center"><strong >Nama ship to</strong></td>
		<td align="center"><strong >Sold-to party</strong></td>
		<td align="center"><strong >Nama Sold to</strong></td>
		<td align="center"><strong >Shipment number</strong></td>
		<td align="center"><strong >Shipment type</strong></td>
		<td align="center"><strong >External ID 1</strong></td>
		<td align="center"><strong >Vehicle</strong></td>
		<td align="center"><strong >Total gds mvt stat.</strong></td>
		<td align="center"><strong>Billing status</strong></td>
		 <td align="center"><strong>Trns.plan.status</strong></td>
		<td align="center"><strong >Billing Type</strong></td>
		<td align="center"><strong >Sales Document</strong></td>
		<td align="center"><strong >Item (SD)</strong></td>
		<td align="center"><strong >Billing Date</strong></td>
		<td align="center"><strong >Doc. condition no.</strong></td>
		<td align="center"><strong >Neto</strong></td>
		<td align="center"><strong >PPN</strong></td>
		<td align="center"><strong >PPH</strong></td>
		<td align="center"><strong >Diesc</strong></td>
		<td align="center"><strong >Currency</strong></td>
		<td align="center"><strong >Shipment Cost Number</strong></td>
		<td align="center"><strong >Transferred</strong></td>
		<td align="center"><strong >Assigned</strong></td>
		<td align="center"><strong>Calculated</strong></td>
		 <td align="center"><strong>Created on</strong></td>
		<td align="center"><strong >Settlement date</strong></td>
		<td align="center"><strong >Purchasing Document</strong></td>
		<td align="center"><strong >Item</strong></td>
		<td align="center"><strong >ShpCst Net</strong></td>
		<td align="center"><strong >ShpCst Tax</strong></td>
		<td align="center"><strong >Total MIRO</strong></td>
		<td align="center"><strong >Total Amount</strong></td>
		<td align="center"><strong >Currency</strong></td>
		<td align="center"><strong >Order reason</strong></td>
		<td align="center"><strong >Name</strong></td>
		<td align="center"><strong >Count</strong></td>
      </tr >
  <?  
  		$totaldo= 0;
  		for($i=0; $i<$total;$i++) {
		$totaldo= $totaldo+$qty_do[$i];
		$b=$i+1;
		if(($i % 2) == 0)	{	 
		echo "<tr class='row0'>";
			}
		else	{	
		echo "<tr class='row1'>";
			}	
		?>     
		<td align="center"><? echo $b; ?></td>
		<td align="left"><? echo $VKORG[$i]; ?></td>
		<td align="left"><? echo $VBELN[$i]; ?></td>
		<td align="left"><? echo $POSNR[$i]; ?></td>
		<td align="left"><? echo $VGBEL[$i]; ?></td>
		<td align="left"><? echo $VGPOS[$i]; ?></td>
		<td align="right"><? echo $MATNR[$i]; ?></td>
		<td align="left"><? echo $ARKTX[$i]; ?></td>
		<td align="left"><? echo $WERKS[$i]; ?></td>
		<td align="left"><? echo $LFIMG[$i]; ?></td>
		<td align="left"><? echo $MEINS[$i]; ?></td>
		<td align="left"><? echo $NTGEW[$i]; ?></td>
		<td align="left"><? echo $GEWEI[$i]; ?></td>
		<td align="center"><? echo $TON[$i]; ?></td>
		<td align="left"><? echo $KET_TON[$i]; ?></td>
		<td align="left"><? echo $LFART[$i]; ?></td>
		<td align="left"><? echo $WADAT_IST[$i]; ?></td>
		<td align="left"><? echo $INCO1[$i]; ?></td>
		<td align="left"><? echo $ZTERM[$i]; ?></td>
		<td align="right"><? echo $VKBUR[$i]; ?></td>
		<td align="left"><? echo $BEZEI[$i]; ?></td>
		<td align="left"><? echo $BZIRK[$i]; ?></td>
		<td align="left"><? echo $BZTXT[$i]; ?></td>
		<td align="left"><? echo $KUNNR[$i]; ?></td>
		<td align="left"><? echo $NAME1_SHIP[$i]; ?></td>
		<td align="left"><? echo $KUNAG[$i]; ?></td>
		<td align="center"><? echo $NAME1_SOLD[$i]; ?></td>
		<td align="left"><? echo $TKNUM[$i]; ?></td>
		<td align="left"><? echo $SHTYP[$i]; ?></td>
		<td align="left"><? echo $EXTI1[$i]; ?></td>
		<td align="left"><? echo $ADD01[$i]; ?></td>
		<td align="left"><? echo $WBSTK[$i]; ?></td>
		<td align="right"><? echo $FKSTK[$i]; ?></td>
		<td align="left"><? echo $TRSTA[$i]; ?></td>
		<td align="left"><? echo $FKART_BILL[$i]; ?></td>
		<td align="left"><? echo $VBELN_BILL[$i]; ?></td>
		<td align="left"><? echo $POSNR_BILL[$i]; ?></td>
		<td align="left"><? echo $FKDAT[$i]; ?></td>
		<td align="left"><? echo $KNUMV[$i]; ?></td>		
		<td align="left"><? echo $NET[$i]; ?></td>
		<td align="left"><? echo $PPN[$i]; ?></td>
		<td align="left"><? echo $PPH[$i]; ?></td>
		<td align="left"><? echo $SP3[$i]; ?></td>
		<td align="left"><? echo $WAERS[$i]; ?></td>
		<td align="right"><? echo $FKNUM[$i]; ?></td>
		<td align="left"><? echo $STABR[$i]; ?></td>
		<td align="left"><? echo $STFRE[$i]; ?></td>
		<td align="left"><? echo $STBER[$i]; ?></td>
		<td align="left"><? echo $ERDAT[$i]; ?></td>
		<td align="left"><? echo $BUDAT[$i]; ?></td>
		<td align="left"><? echo $EBELN[$i]; ?></td>
		<td align="left"><? echo $EBELP[$i]; ?></td>
		<td align="left"><? echo $NETWR[$i]; ?></td>
		<td align="left"><? echo $MWSBP[$i]; ?></td>
		<td align="left"><? echo $DMBTR[$i]; ?></td>
		<td align="left"><? echo $TOTAL_AMOUNT[$i]; ?></td>
		<td align="right"><? echo $WAERS_P[$i]; ?></td>
		<td align="left"><? echo $AUGRU[$i]; ?></td>
		<td align="left"><? echo $BEZEI_TVAU[$i]; ?></td>
		<td align="left"><? echo $NCOUNT[$i]; ?></td>

		</tr>
	  <? } ?>
                <tr>
                    <td></td>
                    <td colspan="7">TOTAL</td>
                    <td align="right"><? echo number_format($totaldo,3,".",","); ?></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
	</table>	
	<p>&nbsp;</p>
	</div>
<?	} ?>
<div align="center">
<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>
</body>
</html>
