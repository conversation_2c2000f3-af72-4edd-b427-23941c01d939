<?php
	echo "Starting Job: ".date("Y-m-d H:i:s")."\n";
	ini_set('memory_limit', '-1');
	ini_set('max_execution_time', 0);

	// ------------------------------------------------------
	$username_con3 = "marketplace";
	$password_con3= "semengres1k";
	$db3='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = DEVSGG)(SERVER = DEDICATED)))';
	$con3 = oci_connect($username_con3, $password_con3, $db3 , 'AL32UTF8');

	$username_conn = "dev";
	$password_conn= "semeru2";
	$db='(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SID = XE)(SERVER = DEDICATED)))';
	$conn = oci_connect($username_conn, $password_conn, $db , 'AL32UTF8');
	if(!$conn){
		echo "Gagal Koneksi";
		exit;
	}
	echo "ORC CSMS Connection OK : ".date("Y-m-d H:i:s")."\n";

	if(!$con3){
		echo "Gagal Koneksi";
		exit();
	}
	echo "ORC 3PL Connection OK : ".date("Y-m-d H:i:s")."\n";

  $usernameProd = "APPBISD";
  $passwordProd = "gresik45smigone1";
  $dbProd = '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = pdbsi)(SERVER = DEDICATED)))';
  $connProd = oci_connect($usernameProd, $passwordProd, $dbProd , 'AL32UTF8');
  if(!$connProd){
    echo "GAGAL KONEKSI PROD";
    exit();
  }

  $q3 = oci_parse($connProd, "SELECT
																DISTINCT(KD_KOTA) AS KD_KOTA
															FROM
															ZREPORT_M_KOTA A,
															ZREPORT_M_AREA B
															WHERE
															A .KD_AREA = B.KD_AREA
															AND NO_AREA = '5' or NO_AREA = '1'"); ancuk
	oci_execute($q3);
  $rowDistrict = oci_fetch_all($q3, $dataDistrict);
	$allDistrict = implode("', '", $dataDistrict["KD_KOTA"]);
	$distrik = "('$allDistrict')";

	// SELECT 3PL DATA
	$sql = "
			SELECT
			    SPJ.NO_SPJ, SPJ.NO_POL, SPJ.STATUS, substr(TFG.KWANTUM, 1, LENGTH(TFG.KWANTUM)-4) AS QTY_BAIK, TFG.ITEM_NO AS KD_PRODUK, TFG.PRODUK AS NM_PRODUK, TFG.KD_SHIP_TO AS KD_GUDANG_TUJUAN, TFG.NM_SHIP_TO AS NM_GUDANG_TUJUAN
			FROM
			    T_SPJ_HISTORY SPJ
			    LEFT JOIN T_SPJ TFG ON SPJ.NO_SPJ = TFG.NO_SPJ
			WHERE
			    SPJ.STATUS = 'KELUAR'
			    -- AND SPJ.NO_SPJ = '2003300168'
			    AND SPJ.NOTES IS NULL
			    AND SYSDATE >= TO_DATE(SPJ.WAKTU, 'YYYY-MM-DD HH24:Mi:SS') + INTERVAL '2' DAY
			    AND (TFG.ITEM_NO IS NOT NULL AND TFG.KD_SHIP_TO IS NOT NULL)
          AND TFG.DISTRIK IN ".$distrik."
			";
			echo "<pre>";
			echo $sql;
		echo "</pre>";
		exit();
	$qsql = oci_parse($con3, $sql);
	oci_execute($qsql);

	$row = oci_fetch_all($qsql, $data3pl);

	$all_spj = implode("','", $data3pl['NO_SPJ']);
	// echo "<pre>";
	// 	print_r(array("jumlah"=>count($data3pl["NO_SPJ"]), "data"=>$data3pl["NO_SPJ"]));
	// echo "</pre>";
	// exit();
	// echo $row." rows fetch <br>\n";
	// echo "<pre>";
	// print_r($data3pl);
	// echo "</pre>";
	// exit();

	//UPDATE CSMS
	for ($i=0; $i < $row; $i++) {
		//update ex_trans_hdr
		$sql2 = "UPDATE EX_TRANS_HDR
					SET TANGGAL_DATANG = SYSDATE,
					 TANGGAL_BONGKAR = SYSDATE,
					 QTY_KTG_RUSAK = '0',
					 QTY_SEMEN_RUSAK = '0'
					WHERE
						NO_SHP_TRN IN ('$all_spj')";
		$qsql2 = oci_parse($conn, $sql2);
		oci_execute($qsql2);

		//GET PIC
		$qPic = "select b.PIC FROM T_SPJ A JOIN M_CUSTOMER B ON A.KD_SHIP_TO = B.KD_CUSTOMER WHERE A.NO_SPJ = '".$data3pl["NO_SPJ"][$i]."'";
		$parsePic = oci_parse($con3, $qPic);
		oci_execute($parsePic);
		$arr = oci_fetch_assoc($parsePic);
		$pic = $arr["PIC"];

		// insert EX_INPUTCLAIM_SEMEN
		$q1 = "INSERT INTO EX_INPUTCLAIM_SEMEN
					 (NO_SPJ, QTY_KTG_RUSAK, QTY_SEMEN_RUSAK, TANGGAL_DATANG,
					  TANGGAL_BONGKAR, STATUS, CREATE_BY, CREATE_DATE, JAM_DATANG,
					  JAM_BONGKAR, DELETE_MARK, PIC_GUDANG)
						VALUES
						('".$data3pl["NO_SPJ"][$i]."', '0', '0', SYSDATE,
						SYSDATE, 'ELOG', 'AUTO_APPROVE', SYSDATE, '".date("H:i")."',
						'".date("H:i")."', '0', '".$pic."')";
		$parseInputClaim = oci_parse($conn, $q1);
		echo (oci_execute($parseInputClaim)) ? "HORE" : $q1;
	}
	// exit;

	for ($i=0; $i < $row; $i++) {
		// print_r($data3pl["NO_SPJ"]);

		$sql8 = "
		UPDATE T_SPJ
		SET
			STATUS = 'SELESAI BONGKAR',
			WAKTU_BONGKAR = TO_CHAR(SYSDATE, 'YYYYMMDD HH24:MI:SS')
		WHERE NO_SPJ IN ('$all_spj')
		";
		$qsql8 = oci_parse($con3, $sql8);
		oci_execute($qsql8);
	}
	// exit();

	//INSERT 3PL
	for ($i=0; $i < $row; $i++) {
		$sql3 = "
		MERGE INTO T_SPJ_HISTORY m USING DUAL
			ON
			(m.NO_SPJ = '".$data3pl['NO_SPJ'][$i]."'
				AND m.NO_POL = '".$data3pl['NO_POL'][$i]."'
				AND m.STATUS = 'SUDAH BONGKAR')
			WHEN NOT MATCHED THEN
				INSERT (m.NO_SPJ, m.NO_POL, m.WAKTU, m.STATUS, m.NOTES)
				VALUES
				('".$data3pl['NO_SPJ'][$i]."', '".$data3pl['NO_POL'][$i]."', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), 'SUDAH BONGKAR', 'AUTO APPROVE SPJ')
            ";
		$qsql3 = oci_parse($con3, $sql3);
		oci_execute($qsql3);
	}

	// INSERT 3PL T_INVENTORY
	// SLOC
		// SO1 TERIMA BAIK
		// S02 TERIMA PECAH
		// S04 TERIMA HILANG
	for ($i=0; $i < $row; $i++) {
		$sql4 = "SELECT * FROM(
					SELECT
						NO_REF,
						QTY,
						MATERIAL,
						KD_DOCTYPE,
						TANGGAL_KEGIATAN,
						CREATE_BY,
						CREATE_DATE,
						DELETE_MARK,
						KD_CUSTOMER,
						NM_CUSTOMER,
						KD_SLOC,
						KD_PRODUK,
						NM_PRODUK,
						JENIS,
						STOK_LAMA,
						STOK_BARU
					FROM
						TPL_T_INVENTORY
					WHERE
						NO_REF ='".$data3pl['NO_SPJ'][$i]."'
					AND KD_SLOC='S01'
					ORDER BY CREATE_DATE DESC
				)
				WHERE ROWNUM <=1

				UNION
				SELECT * FROM(
					SELECT
						NO_REF,
						QTY,
						MATERIAL,
						KD_DOCTYPE,
						TANGGAL_KEGIATAN,
						CREATE_BY,
						CREATE_DATE,
						DELETE_MARK,
						KD_CUSTOMER,
						NM_CUSTOMER,
						KD_SLOC,
						KD_PRODUK,
						NM_PRODUK,
						JENIS,
						STOK_LAMA,
						STOK_BARU
					FROM
						TPL_T_INVENTORY
					WHERE
						NO_REF ='".$data3pl['NO_SPJ'][$i]."'
					AND KD_SLOC='S02'
					ORDER BY CREATE_DATE DESC
				)
				WHERE ROWNUM <=1
				UNION
				SELECT * FROM(
					SELECT
						NO_REF,
						QTY,
						MATERIAL,
						KD_DOCTYPE,
						TANGGAL_KEGIATAN,
						CREATE_BY,
						CREATE_DATE,
						DELETE_MARK,
						KD_CUSTOMER,
						NM_CUSTOMER,
						KD_SLOC,
						KD_PRODUK,
						NM_PRODUK,
						JENIS,
						STOK_LAMA,
						STOK_BARU
					FROM
						TPL_T_INVENTORY
					WHERE
						NO_REF ='".$data3pl['NO_SPJ'][$i]."'
					AND KD_SLOC='S04'
					ORDER BY CREATE_DATE DESC
				)
				WHERE ROWNUM <=1";
		$qsql4 = oci_parse($con3, $sql4);
		oci_execute($qsql4);

		$row1 = oci_fetch_all($qsql4, $data_inv);
		// echo "<pre>";
		// print_r($data_inv);
		// echo "</pre>";
		// exit();
		// echo "Data : ".$data_inv['QTY'];

		if($data_inv['QTY']==NULL){
			$QTY[0] = $data3pl['QTY_BAIK'][$i];
			$QTY[1] = '0';
			$QTY[2] = '0';

			$SLOC[0] = 'S01';
			$SLOC[1] = 'S02';
			$SLOC[2] = 'S04';

			$data_gudang['STOK'] = '';

		} else {


		$sql5 = "
		SELECT STOK, STOK_PECAH, STOK_HILANG FROM TPL_T_STOK
			WHERE KD_CUSTOMER = '".$data3pl['KD_GUDANG_TUJUAN'][$i]."'
			AND KD_PRODUK = '".$data3pl['KD_PRODUK'][$i]."'
		";
		$qsql5 = oci_parse($con3, $sql5);
		oci_execute($qsql5);

		$row2 = oci_fetch_all($qsql5, $data_gudang);
		}
		// echo "<pre>";
		// print_r($data_gudang);
		// echo "</pre>";
		// exit();

		$no_spj = $data3pl['NO_SPJ'][$i];
		$kd_cstmr = $data3pl['KD_GUDANG_TUJUAN'][$i];
		$nm_cstmr = $data3pl['NM_GUDANG_TUJUAN'][$i];
		$kd_prd = $data3pl['KD_PRODUK'][$i];
		$nm_prd = $data3pl['NM_PRODUK'][$i];

		if($data_gudang['STOK']==NULL) {
			$data_gudang['STOK'] = $QTY[0];
			$data_gudang['STOK_PECAH'] = $QTY[1];
			$data_gudang['STOK_HILANG'] = $QTY[2];
			$data_inv['QTY'][0] = 0;
			$data_inv['QTY'][1] = 0;
			$data_inv['QTY'][2] = 0;

			for ($k=0; $k < count($SLOC); $k++) {
			$sql9 = "
			INSERT INTO TPL_T_INVENTORY
			(NO_REF, QTY, MATERIAL, KD_DOCTYPE, TANGGAL_KEGIATAN, CREATE_BY, CREATE_DATE, DELETE_MARK, KD_CUSTOMER, NM_CUSTOMER, KD_SLOC, KD_PRODUK, NM_PRODUK, JENIS, STOK_LAMA, STOK_BARU)
			VALUES
			('".$no_spj."', '".$QTY[$k]."', '".$kd_prd."-".$nm_prd."', '101', TO_DATE('".DATE("Y-m-d H:i:s")."', 'YYYY-MM-DD HH24:MI:SS'), 'AUTO_APPROVE',TO_DATE('".DATE("Y-m-d H:i:s")."', 'YYYY-MM-DD HH24:MI:SS'), '0', '".$kd_cstmr."', '".$nm_cstmr."', '".$SLOC[$k]."', '".$kd_prd."', '".$nm_prd."', 'SEMEN', '0', '".$QTY[$k]."')
			";
			// echo $sql9; exit();
 			$qsql9 = oci_parse($con3, $sql9);
			oci_execute($qsql9);

			}

		} else {

			for ($j=0; $j < $row1; $j++) {

				if($data_inv['KD_SLOC'][$j] == 'S01'){
					$stok = $data_gudang['STOK'];
				} elseif ($data_inv['KD_SLOC'][$j] = 'S02') {
					$stok = $data_gudang['STOK_PECAH'];
				} else {
					$stok = $data_gudang['STOK_HILANG'];
				}

				$sql6 = "
			 	INSERT INTO TPL_T_INVENTORY
				(NO_REF, QTY, MATERIAL, KD_DOCTYPE, TANGGAL_KEGIATAN, CREATE_BY, CREATE_DATE, DELETE_MARK, KD_CUSTOMER, NM_CUSTOMER, KD_SLOC, KD_PRODUK, NM_PRODUK, JENIS, STOK_LAMA, STOK_BARU)
				VALUES
				('".$no_spj."', '".$data_inv['QTY'][$j]."', '".$kd_prd."-".$nm_prd."', '".$data_inv['KD_DOCTYPE'][$j]."', 'AUTO_APPROVE', TO_DATE('".DATE("Y-m-d H:i:s")."', 'YYYY-MM-DD HH24:MI:SS'), '0', '".$kd_cstmr."', '".$data_inv['KD_SLOC'][$j]."', '".$kd_prd."', '".$nm_prd."', 'SEMEN', '".$stok."', '".$stok+$data_inv['QTY'][$j]."')
				";

				$qsql6 = oci_parse($con3, $sql6);
				oci_execute($qsql6);
				// echo $sql6; exit();

			}

		}

		$sstok=$data_gudang['STOK']+$data_inv['QTY'][0];
		$specah=$data_gudang['STOK_PECAH']+$data_inv['QTY'][1];
		$shilang=$data_gudang['STOK_HILANG']+$data_inv['QTY'][2];

		if(substr($kd_prd, 4, 1) == '3') {
			$jenis = 'SEMEN';
		} else {
			$jenis = 'KANTONG';
		}

		$sql7 = "
		MERGE INTO TPL_T_STOK USING DUAL ON
		(KD_CUSTOMER = '".$kd_cstmr."' AND
		 KD_PRODUK = '".$kd_prd."'
		)
		WHEN MATCHED THEN UPDATE
		SET STOK = '".$sstok."', STOK_PECAH = '".$specah."', STOK_HILANG = '".$shilang."'
		WHEN NOT MATCHED THEN INSERT
		(KD_CUSTOMER, NM_CUSTOMER, KD_PRODUK, NM_PRODUK, STOK, STOK_PECAH, STOK_HILANG, CREATE_DATE, CREATE_BY, JENIS)
		VALUES
		('".$kd_cstmr."', '".$nm_cstmr."', '".$kd_prd."', '".$nm_prd."', '".$sstok."','".$specah."','".$shilang."', SYSDATE, 'AUTO_APPROVE', '".$jenis."')
		";
		// echo $sql7; exit();
		$qsql7 = oci_parse($con3, $sql7);
		oci_execute($qsql7);

		$sql10 = "
		UPDATE T_SPJ_HISTORY
			SET NOTES = 'AUTO_APPROVE'
		WHERE NO_SPJ = '".$data3pl['NO_SPJ'][$i]."'
		AND NO_POL = '".$data3pl['NO_POL'][$i]."'
		AND STATUS = 'KELUAR'
		";
		$qsql10 = oci_parse($con3, $sql10);
		oci_execute($qsql10);

	}

	// End Script
	echo "DONE : ".date("Y-m-d H:i:s")."\n";
