<?
$mtime = microtime();
$mtime = explode(" ",$mtime);
$mtime = $mtime[1] + $mtime[0];
$starttime = $mtime; 
   
	#include_once("sapfrc/sapclasses/sap.php");
        
        # SAP Connection #
        require_once("../../sgg/include/connect/SAPDataModule_Connection.php");
        $sap_con = New SAPDataModule_Connection();
        $sap = $sap_con->getConnSAP();
        
        #Koneksi MySQL EIS###
        $sap_eis = $sap_con->koneksiEIS();
        
        //2405 : ciwandan
        $q_kode = mysql_query("select kd_plant from m_plant where kd_plant='2405'") ;//'2601','2405','2602','2604','2605','2606','2608')"); //2609	
        while ($data = mysql_fetch_array($q_kode)) {
		echo "<br>".$kdplant = $data["kd_plant"];
	
		$sap = new SAPConnection();
		$sap->Connect("logon_data.conf");

		if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
		if ($sap->GetStatus() != SAPRFC_OK ) { $sap->PrintStatus(); exit; }

		$fce = &$sap->NewFunction ("Z_ZAPPSD_MM_VALSTOCK");
		if ($fce == false ) { $sap->PrintStatus(); exit; }	
		
		$tglstock = date('Ymd');
		$fce->I_BUKRS = '2000';
		$fce->I_WERKS = $kdplant;
		$fce->I_DATE = $tglstock;//date('Ymd', strtotime("-".$n_days." days"));//
		$fce->I_DATE_TO = $tglstock;//date('Ymd', strtotime("-".$n_days." days"));//
		#$fce->I_MATNR = '121-302-0010';
		$fce->I_FLAG = '2'; 
		$fce->I_SUM ='X';
		$fce->Call();
		
                #echo	$q = "delete from sap_total_stock_gd_penyangga  where werks='".$kdplant."' tgl='".$tglstock."'";
		#mysql_query($q);

		if ($fce->GetStatus() == SAPRFC_OK ) {	
			$sukses=0;
                        $gagal=0;
                        $fce->T_RETURNDATA->Reset();
			while ( $fce->T_RETURNDATA->Next() ){
                                echo "dor";
				$WERKS = $fce->T_RETURNDATA->row["WERKS"];
				$NAME1 = $fce->T_RETURNDATA->row["NAME1"];
				$MATNR = $fce->T_RETURNDATA->row["MATNR"];
				$MAKTX = $fce->T_RETURNDATA->row["MAKTX"];
				$MTART = $fce->T_RETURNDATA->row["MTART"];
				$MTBEZ = $fce->T_RETURNDATA->row["MTBEZ"];
				$START_STOCK = $fce->T_RETURNDATA->row["START_STOCK"];
				$START_STOCKX = $fce->T_RETURNDATA->row["START_STOCKX"];
				$REC_TOTAL = $fce->T_RETURNDATA->row["REC_TOTAL"];
				$REC_TOTALX = $fce->T_RETURNDATA->row["REC_TOTALX"];
				$ISSUE_TOTAL = $fce->T_RETURNDATA->row["ISSUE_TOTAL"];
				$ISSUE_TOTALX = $fce->T_RETURNDATA->row["ISSUE_TOTALX"];
				$END_STOCK = $fce->T_RETURNDATA->row["END_STOCK"];
				$END_STOCKX = $fce->T_RETURNDATA->row["END_STOCKX"];
				
				$q_stock ="INSERT INTO sap_total_stock_silo_test (tgl,WERKS, NAME1, MATNR, MAKTX, MTAR, MTBEZ, START_STOCK, START_STOCKX, REC_TOTAL, REC_TOTALX, ISSUE_TOTAL, ISSUE_TOTALX, END_STOCK, END_STOCKX) values ('".$tglstock."','".$WERKS."','".$NAME1."','".$MATNR."','".$MAKTX."','".$MTART."','".$MTBEZ."','".$START_STOCK."','".$START_STOCKX."','".$REC_TOTAL."','".$REC_TOTALX."','".$ISSUE_TOTAL."','".$ISSUE_TOTALX."','".$END_STOCK."','".$END_STOCKX."')";
                                $hasil=mysql_query($q_stock,$sap_eis);
				if ($hasil) $sukses++;
				else $gagal++;
		       		
			}
		}
                echo "Success insert ".$s."Record for".$tgl."<br>";
		$sap->Close();
	}

$mtime = microtime();
$mtime = explode(" ",$mtime);
$mtime = $mtime[1] + $mtime[0];
$endtime = $mtime;
$totaltime = ($endtime - $starttime);
echo "Waktu : ".$totaltime." seconds"; 
?> 