<?
session_start();

include ('../include/crm_fungsi.php');
include ('../include/validasi.php'); 
$fungsi=new crm_fungsi();
$conn=$fungsi->crm_koneksi();
echo $sql ="INSERT INTO M_KONTRAKTOR(NM_KONTRAKTOR, ALMT_KONTRAKTOR, CONTACT_P, TELEPON) VALUES ('".$NM_KONTRAKTOR."','".$ALMT_KONTRAKTOR."', '".$CONTACT_P."','".$TELEPON."')";
$query= oci_parse($conn,$sql);
oci_execute($query,OCI_DEFAULT);
oci_commit($conn);
if ($query) {
	echo ("<br>Input data berhasil");
	} else {
	echo ("<br>Maaf input data gagal");
}

?>





