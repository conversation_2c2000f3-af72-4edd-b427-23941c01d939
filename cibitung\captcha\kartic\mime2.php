<?php
 /* --------------------------------------------------
    This is a simple example that shows how to use 
    the class to send email with HTML.
    e.g., monthly newsletter

    This particular code be iteratively done different
    subscribers (of course you can use a mailing list
    name if you have one set up)
 -------------------------------------------------- */
 include "MIME.class";
 
 //Change the $to address when you try out! This can be picked from a database too
 //and iteratively sent out!
 $to = '<EMAIL>'; 
 $str = "Newsletter for ".date('M Y', time());
 
 $html_data = '<html><head><title>'.$str.'</title></head><body bgcolor="#fff0ff" text="#0000ee"><table bgcolor="#000000" width="100%"><tr><td><h1>COMPANY INC.</h1></td></tr></table><h4>Dear Customer</h4><h5>This is our monthly newsletter.<p>In this month\'s feature we will look at:<p><a href="#cookie">Cookies</a><br><a href="#sessions">Sessions</a><br><a href="#imap">Imap</a><p>We are also happy to announce our technical partnership with XYZ Inc., a leading E-Business solutions provider.<p><a name="cookie"><h4>Cookies</h4></a><p><a name="sessions"><h4>Sessions</h4></a><p><a name="imap"><h4>IMAP Protocol</h4></a></h5><hr><h6><font color="#ff0000">To unsubscribe, click on <a href="http://localhost/newsletter/unsubcribe?user='.$to.'">this</a> link.</font></h6></body></html>';

$mime = new MIME_mail("<EMAIL>", $to, $str);
$mime->attach($html_data, "", HTML, BASE64);
$mime->send_mail();
?>
