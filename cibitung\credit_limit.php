<?
session_start();
include ('../include/crm_fungsi.php');


	$loginFile = array(	"ASHOST"	=> "**********", 	// application server host name
								"SYSNR" 	=> "00",			// service
								"CLIENT" 	=> "030", 			// client
								"USER" 		=> "amin", 	// user
								"PASSWD" 	=> "aminazhar"		// password
								);
			$sap = new SAPConnection();
			$sap->Connect($loginFile);
			if ($sap->GetStatus() == SAPRFC_OK ) $sap->Open ();
			if ($sap->GetStatus() != SAPRFC_OK )
			{
			   echo $sap->PrintStatus();
			     exit;
            }

		$fce = $sap->NewFunction ("Z_CREDIT_EXPOSURE_ALL");
		if ($fce == false ) {
		   $sap->PrintStatus();
		   exit;
		}

		$date = date (dmY);
	
        $fce->X_KKBER ='2000';
		$fce->X_KUNNR = '0000000100';
		$fce->X_KUNNR_TO = '0000000399'; 
		$fce->X_DATE_CREDIT_EXPOSURE = $date; 
	
		$fce->Call();
		if ($fce->GetStatus() == SAPRFC_OK ) {		
			
			$fce->T_OUTPUT->Reset();
			$s=0;
			while ( $fce->T_OUTPUT->Next() ){
				$KUNNR[$s] = $fce->T_OUTPUT->row["KUNNR"];
				$NAME1[$s] = $fce->T_OUTPUT->row["NAME1"];
				$CREDITLIMIT[$s] = $fce->T_OUTPUT->row["CREDITLIMIT"];
				$DELTA_TO_LIMIT[$s] = $fce->T_OUTPUT->row["DELTA_TO_LIMIT"];
				$PROSEN_CL[$s] = $fce->T_OUTPUT->row["PROSEN_CL"];
				$s++;
			} 
		/*	$dt = $fce->T_OUTPUT->Export(0,0);
			echo "<pre>";
			print_r($dt);
			echo "</pre>"; */
		}else
        		$fce->PrintStatus();

		$fce->Close();	
		$sap->Close();	
		$total=count($KUNNR);	


?>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Aplikasi SGG Online: Lihat Data SPJ :)</title><script language="JavaScript" type="text/javascript" src="../include/calendar/arsip.javascript.js"></script>
<script language="JavaScript" src="../include/calendar/JSCookMenu_mini.js" type="text/javascript"></script>
<!-- import the calendar script -->
<script type="text/javascript" src="../include/calendar/calendar_mini.js"></script>
<!-- import the language module -->
<script type="text/javascript" src="../include/calendar/lang/calendar-en.js"></script>
<link href="../include/calendar/calendar-mos.css" rel="stylesheet" type="text/css">
<link href="../Templates/css/template_css.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/admin_login.css" rel="stylesheet" type="text/css" />
<link href="../Templates/css/theme.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div align="center">
<table width="700" align="center" class="adminheading" border="0">
<tr>
<th class="kb2">Daftar Kredit Limit</th>
</tr></table></div>
<div align="center">
	<table width="1200" align="center">
	<tr>
	<th align="right" colspan="4"><span>
	 </span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1000" align="center" class="adminlist">
	<tr>
	<th align="left" colspan="4"><span class="style5">&nbsp;Tabel Daftar Kredit Limit</span></th>
	</tr>
	</table>
	</div> 
	<div align="center">
	<table width="1000" align="center" class="adminlist">
	  <tr class="quote">
		<td align="center"><strong>&nbsp;&nbsp;No.</strong></td>
		<td align="center"><strong >No KUNNR</strong></td>
		<td align="center"><strong >NAMA KUNNR</strong></td>
		<td align="center"><strong >KREDIT LIMIT</strong></td>
		<td align="center"><strong >DELTA</td>
		<td align="center"><strong >Customer's credit limit</td>
     </tr >
  <?  for($i=0; $i<$total;$i++) {
		$b=$i+1;
	?>     
		<td align="center"><? echo $b; ?></td>
		<td align="center"><? echo $KUNNR[$i]; ?></td>
		<td align="left"><? echo $NAME1[$i]; ?></td>
		<td align="right"><? echo $CREDITLIMIT[$i]; ?></td>
		<td align="right"><? echo $DELTA_TO_LIMIT[$i]; ?></td>
		<td align="right"><? echo $PROSEN_CL[$i]; ?></td>
		
		</tr>
	  <? } ?>
	  <tr class="quote">
		<td colspan="17" align="center"></td>
	    </tr>
	</table>

	</div>
<div align="center">

<?
echo $komen;

?></div>

<p>&nbsp;</p>
</p>

</body>
</html>

